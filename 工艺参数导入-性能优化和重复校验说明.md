# 工艺参数导入 - 性能优化和重复校验说明

## 优化概述

针对之前每个参数都需要查询数据库的性能问题，以及缺少重复项校验的问题，进行了以下优化：

1. **批量查询优化**：一次性查询工步下的所有参数，避免N+1查询问题
2. **Excel重复项校验**：在解析过程中检测Excel中的重复参数代码
3. **缓存策略优化**：改进缓存机制，提高查询效率

## 主要改进

### 1. 批量查询优化

#### 优化前（N+1查询问题）
```java
// 每个参数都要查询一次数据库
StepParameter existingParameter = stepParameterRepository.findByStepIdAndParamCode(stepId, paramCode);
```

#### 优化后（批量查询）
```java
// 一次性查询工步下所有参数
Map<String, StepParameter> existingParametersMap = getExistingParametersForStep(stepId);
StepParameter existingParameter = existingParametersMap.get(paramCodeFromExcel);
```

### 2. Excel重复项校验

在解析Excel过程中，实时检测重复的参数代码：

```java
// 校验Excel中的重复项
String duplicateKey = processCode + "_" + paramCodeFromExcel;
if (excelParamCodeMap.containsKey(duplicateKey)) {
    throw new RuntimeException(String.format("Excel中存在重复的参数：工序[%s]下的参数代码[%s]在第%d行重复出现", 
        processCode, paramCodeFromExcel, row.getRowNum() + 1));
}
excelParamCodeMap.put(duplicateKey, paramCodeFromExcel);
```

### 3. 缓存策略优化

#### 新的缓存实现
```java
private Map<String, StepParameter> getExistingParametersForStep(String stepId) {
    return stepParameterCache.computeIfAbsent(stepId, id -> {
        List<StepParameter> existingParams = stepParameterRepository.findByStepIdOrderByCreatedTime(id);
        return existingParams.stream()
                .collect(Collectors.toMap(
                    StepParameter::getParamCode, 
                    param -> param, 
                    (existing, replacement) -> existing // 如果有重复的paramCode，保留第一个
                ));
    });
}
```

## 性能对比

### 优化前
- **数据库查询次数**：N次（N为Excel中的参数数量）
- **查询类型**：单条记录查询
- **缓存效率**：低（每个参数单独缓存）

### 优化后
- **数据库查询次数**：M次（M为涉及的工步数量，通常M << N）
- **查询类型**：批量查询
- **缓存效率**：高（按工步批量缓存）

### 性能提升示例
假设导入100个参数，涉及5个工步：
- **优化前**：100次数据库查询
- **优化后**：5次数据库查询
- **性能提升**：95%的查询减少

## 错误处理

### 1. Excel重复项错误
```
Excel中存在重复的参数：工序[PROCESS001]下的参数代码[PARAM001]在第3行重复出现
```

### 2. 错误信息包含
- 重复的工序代码
- 重复的参数代码
- 重复出现的行号（从1开始计数）

## 代码实现细节

### 1. 重复项检测Map
```java
// 用于校验Excel中的重复项
Map<String, String> excelParamCodeMap = new ConcurrentHashMap<>();
```

### 2. 批量参数查询
```java
// 获取工步下所有已存在的参数（一次性查询，使用缓存）
Map<String, StepParameter> existingParametersMap = getExistingParametersForStep(stepId);
```

### 3. 参数数量计算优化
```java
// 基于已获取的参数Map计算数量，避免重复查询数据库
int currentCount = stepParamCountMap.computeIfAbsent(stepId, id -> existingParametersMap.size());
```

## 测试用例

### 1. 重复项校验测试
```java
@Test(expectedExceptions = RuntimeException.class, expectedExceptionsMessageRegExp = ".*Excel中存在重复的参数.*")
public void testDuplicateParamCodeInExcel() {
    // 创建包含重复参数代码的Excel
    ByteArrayInputStream inputStream = createDuplicateExcel();
    // 应该抛出异常
    stepParameterExcelImporter.parseExcel(inputStream);
}
```

### 2. 批量查询测试
```java
@Test
public void testUpdateExistingParameter() {
    // 模拟工步下已有一个参数PARAM001
    when(stepParameterRepository.findByStepIdOrderByCreatedTime(anyString()))
        .thenReturn(List.of(existingParam));
    
    // 验证更新和新增逻辑
}
```

## 使用场景

### 1. 大批量导入
- 适用于一次性导入大量参数的场景
- 显著减少数据库查询次数
- 提高导入效率

### 2. 数据质量保证
- 防止Excel中的重复数据导入
- 提供明确的错误提示
- 支持数据预校验

### 3. 混合操作优化
- 新增和更新操作都得到优化
- 缓存机制支持复杂的导入场景

## 注意事项

1. **内存使用**：批量查询会增加内存使用，但对于一般规模的参数数量是可接受的
2. **事务处理**：重复项校验在事务开始前进行，避免部分数据导入后回滚
3. **错误定位**：错误信息包含具体的行号，便于用户定位和修复问题
4. **缓存一致性**：同一工步的参数查询结果会被缓存，确保一致性

## 总结

通过批量查询优化和重复项校验，工艺参数导入功能在性能和数据质量方面都得到了显著提升：

- **性能提升**：减少90%以上的数据库查询
- **数据质量**：防止重复数据导入
- **用户体验**：提供清晰的错误提示
- **系统稳定性**：减少数据库压力，提高系统稳定性
