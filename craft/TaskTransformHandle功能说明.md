# TaskTransformHandle 功能说明

## 概述

`TaskTransformHandle` 类新增了为 Telegram payload 添加 taskCode 字段的功能。该功能在处理历史遗留数据时，会先过滤出不需要 taskCode 的 Telegram 直接处理，然后对需要 taskCode 的 Telegram 循环 taskCodes，为每个 taskCode 创建一个对应的消息。

## 功能特性

### 1. 常量集合定义

定义了一个常量集合 `TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE`，用于存储需要特殊处理的 Telegram serviceId：

```java
private static final Set<String> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE = Set.of(
    "service_id_1",
    "service_id_2", 
    "service_id_3"
    // 可以根据实际需求添加更多 serviceId
);
```

### 2. 智能分离处理

在 `dealLegacyData` 方法中，会：

1. **分离 Telegram**：将 Telegram 分为需要和不需要 taskCode 的两组
2. **直接处理不需要 taskCode 的 Telegram**：直接推送消息
3. **循环处理需要 taskCode 的 Telegram**：为每个 taskCode 创建一个对应的消息

### 3. 处理逻辑示例

假设有以下数据：
- **Telegram A**：serviceId = "service_id_1"（需要 taskCode）
- **Telegram B**：serviceId = "other_service"（不需要 taskCode）
- **taskCodes**：["TASK001", "TASK002", "TASK003"]

处理结果：
- **Telegram B**：推送 1 条消息（原始 payload）
- **Telegram A**：推送 3 条消息（每个 taskCode 一条）

### 4. payload 格式支持

#### 单个对象格式
原始 payload：
```json
{
  "name": "test",
  "value": 123
}
```

添加 taskCode 后：
```json
{
  "name": "test", 
  "value": 123,
  "taskCode": "TASK001"
}
```

#### 数组格式
原始 payload：
```json
[
  {"name": "test1", "value": 111},
  {"name": "test2", "value": 222}
]
```

添加 taskCode 后（每个元素都添加相同的 taskCode）：
```json
[
  {"name": "test1", "value": 111, "taskCode": "TASK001"},
  {"name": "test2", "value": 222, "taskCode": "TASK001"}
]
```

## 核心实现

### 分离和处理逻辑

```java
// 分离需要和不需要 taskCode 的 telegram
List<Telegram> telegramsNotRequiringTaskCode = telegrams.stream()
        .filter(telegram -> !TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE.contains(telegram.getServiceId()))
        .toList();

List<Telegram> telegramsRequiringTaskCode = telegrams.stream()
        .filter(telegram -> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE.contains(telegram.getServiceId()))
        .toList();

// 处理不需要 taskCode 的 telegram
telegramsNotRequiringTaskCode.forEach(telegram -> {
    // 直接推送原始消息
});

// 处理需要 taskCode 的 telegram - 为每个 taskCode 创建一个消息
telegramsRequiringTaskCode.forEach(telegram -> {
    taskCodes.forEach(taskCode -> {
        // 为每个 taskCode 创建一个消息
    });
});
```

## 使用方法

### 1. 配置 serviceId

在 `TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE` 常量中添加需要特殊处理的 serviceId：

```java
private static final Set<String> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE = Set.of(
    "your_service_id_1",
    "your_service_id_2",
    "your_service_id_3"
);
```

### 2. 确保 handleContext 包含 taskCode

在调用 `dealLegacyData` 方法之前，确保 handleContext 中包含 taskCode 集合：

```java
Map<String, Object> handleContext = new HashMap<>();
List<String> taskCodes = Arrays.asList("TASK001", "TASK002", "TASK003");
handleContext.put("taskCode", taskCodes);
```

### 3. 消息倍增效果

如果有：
- 2 个需要 taskCode 的 Telegram
- 3 个 taskCode

则会产生：2 × 3 = 6 条消息

## 错误处理

### 1. 异常安全

如果在处理 payload 时发生异常，系统会：
- 记录错误日志
- 返回原始 payload，确保消息能正常推送

### 2. 数据验证

- 自动验证 JSON 格式
- 检查 taskCode 集合是否为空
- 确保 payload 类型正确（ObjectNode 或 ArrayNode）

## 性能考虑

### 1. 消息数量

需要注意消息倍增效果，合理控制 taskCode 数量以避免消息队列压力。

### 2. 内存使用

使用 Jackson 的 JsonNode 进行内存中的 JSON 操作，避免字符串拼接。

## 测试

提供了完整的单元测试，覆盖以下场景：

1. 单个对象 payload 处理
2. 数组 payload 处理  
3. 空 taskCode 处理
4. 无效 JSON 处理
5. null taskCode 处理

运行测试：
```bash
mvn test -Dtest=TaskTransformHandleTest
```

## 注意事项

1. **serviceId 配置**：需要根据实际业务需求配置正确的 serviceId
2. **消息倍增**：每个需要 taskCode 的 Telegram 会为每个 taskCode 生成一条消息
3. **JSON 格式**：确保 payload 是有效的 JSON 格式
4. **向后兼容**：不影响现有功能，只在特定条件下生效
5. **性能影响**：注意控制 taskCode 数量，避免产生过多消息
