version: "3"
services:
  DaMeng:
    image: ***************/bzlj/dm8:v_20250206
    restart: always
    container_name: DaMeng
    privileged: true
    environment:
      - SYSDBA_PWD=Changeme123
      - TZ=Asia/Shanghai
      - CASE_SENSITIVE=0       # 是否区分大小写，0 表示不区分，1 表示区分
      - LD_LIBRARY_PATH=/opt/dmdbms/bin
      - PAGE_SIZE=16
      - EXTENT_SIZE=32
      - LOG_SIZE=1024
      - UNICODE_FLAG=1  #utf-8
      - INSTANCE_NAME=dm8_test
      - COMPATIBLE_MODE=4 #兼容模式，兼容mysql
    volumes:
      - /data/common/dev/config/dev.ini:/opt/dmdbms/conf/dev.ini
      - /data/common/dev/data:/opt/dmdbms/data # 数据存储目录
      - /data/common/dev/logs:/opt/dmdbms/log
    ports:
      - "5236:5236"
  bwty-craft-service:
    container_name: bwty-craft-service
    hostname: bwty-craft-service
    image: ***************/bzlj/bwty-craft:polang-1.0.0
    restart: always
#    depends_on:
#      - DaMeng
    ports:
      - "7900:7900"
    environment:
      DM_URL: jdbc:dm://***************:5237/bwty-craft-dev?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
      DM_USERNAME: SYSDBA
      DM_PWD: Changeme_123

  kafka:
    image: ***************/middle-software/kafka:3.7.0
    container_name: kafka
    hostname: kafka
    restart: always
    user: root
    ports:
      - 9092:9092
      - 9093:9093
    security_opt:
      - seccomp=unconfined
    environment:
      ### 通用配置
      # 允许使用kraft，即Kafka替代Zookeeper
      - KAFKA_ENABLE_KRAFT=yes
      - KAFKA_CFG_NODE_ID=1
      # kafka角色，做broker，也要做controller
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      # 定义kafka服务端socket监听端口（Docker内部的ip地址和端口）
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
      # 定义外网访问地址（宿主机ip地址和端口）ip不能是0.0.0.0
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://***************:9092
      # 定义安全协议
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      # 集群地址
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      # 指定供外部使用的控制类请求信息
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      # 设置broker最大内存，和初始内存
      - KAFKA_HEAP_OPTS=-Xmx512M -Xms256M
      # 使用Kafka时的集群id，集群内的Kafka都要用这个id做初始化，生成一个UUID即可(22byte)
      - KAFKA_KRAFT_CLUSTER_ID=xYcCyHmJlIaLzLoBzVwIcP
      # 允许使用PLAINTEXT监听器，默认false，不建议在生产环境使用
      - ALLOW_PLAINTEXT_LISTENER=yes
      # 不允许自动创建主题
      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=false
      # broker.id，必须唯一，且与KAFKA_CFG_NODE_ID一致
      - KAFKA_BROKER_ID=1
    volumes:
      - /data/common/kafka/kafka:/bitnami/kafka

  nifi:
    image: apache/nifi:2.3.0
    container_name: nifi
    restart: unless-stopped
    ports:
      - "8443:8443"   # HTTPS UI
      - "10000:10000" # Remote Process Group通信
    environment:
      # 核心配置
      - NIFI_WEB_HTTPS_HOST=0.0.0.0
      - NIFI_CLUSTER_IS_NODE=false
      #   # 安全配置（自动生成证书）
      - NIFI_SENSITIVE_PROPS_KEY=MyC0mpl3xP@ssw0rd!2023
      # 核心参数：关闭 SNI 强制校验
      - NIFI_SECURITY_HTTPS_REQUIRE_SNI=false
      # 必须同步配置代理域名（即使使用 IP）
      - NIFI_WEB_PROXY_HOST=***************:8443
      # 补充安全参数（开发环境简化）
      - SINGLE_USER_CREDENTIALS_USERNAME=admin
      - SINGLE_USER_CREDENTIALS_PASSWORD=ctsBtRBKHRAx69EqUghvvgEvjnaLjFEB
      # 性能优化
      - NIFI_JVM_HEAP_INIT=2g
      - NIFI_JVM_HEAP_MAX=4g
#    volumes:
#      - /data/nifi/nifi/data:/opt/nifi/nifi-current
     # - /data/nifi/nifi/custom:/opt/nifi/nifi-current/custom # 扩展组件目录
    healthcheck:
      test: [ "CMD-SHELL", "curl -kf https://***************:8443/nifi-api/system-diagnostics || exit 1" ]
      interval: 30s

  nifi-registry:
    image: apache/nifi-registry:2.3.0
    container_name: nifi-registry
    restart: unless-stopped
    ports:
      - "18443:18443"
      - "18080:18080"
    environment:
      - NIFI_REGISTRY_WEB_HTTPS_HOST=0.0.0.0
      - NIFI_REGISTRY_WEB_HTTPS_PORT=18443
      - NIFI_REGISTRY_SENSITIVE_PROPS_KEY=MyC0mpl3xP@ssw0rd!2023
      - NIFI_REGISTRY_DB_DIR=/opt/nifi-registry/nifi-registry-current/database
      - NIFI_REGISTRY_EXTENSION_DIR=/opt/nifi-registry/nifi-registry-current/extensions
    # volumes:
    #   - /data/nifi/registry/data:/opt/nifi-registry/nifi-registry-current
    healthcheck:
      test: [ "CMD-SHELL", "curl -kf https://localhost:18443/nifi-registry-api/access || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 5

