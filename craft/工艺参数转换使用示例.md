# 工艺参数转换使用示例

## 功能特点

新的 `craftParamsTransform` 方法支持灵活的输入格式：
- **单个JSON对象**：直接处理单个参数对象
- **JSON数组**：递归处理数组中的每个元素

## 使用示例

### 示例1：处理单个参数对象

```java
@Autowired
private CraftParamTransformService craftParamTransformService;

public void processSingleParam() {
    String singleObjectJson = """
        {
            "RLBD_NET_RATE": {
                "MIN": "0.15",
                "MAX": "1.00"
            },
            "taskCode": "25RD70574",
            "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
            "processCode": "GDL",
            "PROD_HEAT_TEMP": {
                "MIN": "1050",
                "MAX": "1100"
            }
        }
        """;
    
    craftParamTransformService.craftParamsTransform(singleObjectJson);
    System.out.println("单个参数对象处理完成");
}
```

### 示例2：处理参数数组

```java
public void processMultipleParams() {
    String arrayJson = """
        [{
            "RLBD_NET_RATE": {
                "MIN": "0.15",
                "MAX": "1.00"
            },
            "taskCode": "25RD70574",
            "paramCode": "RLBD_NET_RATE",
            "processCode": "GDL"
        }, {
            "PROD_HEAT_TEMP": {
                "MIN": "1050",
                "MAX": "1100"
            },
            "taskCode": "25RD70575",
            "paramCode": "PROD_HEAT_TEMP",
            "processCode": "GDL"
        }]
        """;
    
    craftParamTransformService.craftParamsTransform(arrayJson);
    System.out.println("参数数组处理完成");
}
```

### 示例3：嵌套数组处理

```java
public void processNestedArrays() {
    String nestedArrayJson = """
        [[{
            "RLBD_NET_RATE": {
                "MIN": "0.15",
                "MAX": "1.00"
            },
            "taskCode": "25RD70574",
            "paramCode": "RLBD_NET_RATE",
            "processCode": "GDL"
        }], [{
            "PROD_HEAT_TEMP": {
                "MIN": "1050",
                "MAX": "1100"
            },
            "taskCode": "25RD70575",
            "paramCode": "PROD_HEAT_TEMP",
            "processCode": "GDL"
        }]]
        """;
    
    // 由于递归特性，嵌套数组也能正确处理
    craftParamTransformService.craftParamsTransform(nestedArrayJson);
    System.out.println("嵌套数组处理完成");
}
```

## 处理逻辑说明

### 递归处理机制

1. **输入判断**：
   - 如果输入是单个JSON对象 → 直接调用 `processParamNode()`
   - 如果输入是JSON数组 → 遍历数组，对每个元素递归调用 `craftParamsTransform()`

2. **递归优势**：
   - 支持任意层级的嵌套数组
   - 统一的处理逻辑，无需区分数组层级
   - 代码简洁，易于维护

### 错误处理

```java
public void handleErrors() {
    try {
        // 空数组测试
        craftParamTransformService.craftParamsTransform("[]");
    } catch (RuntimeException e) {
        System.out.println("空数组异常: " + e.getMessage());
        // 输出: 参数数组不能为空
    }
    
    try {
        // 缺少字段测试
        String invalidJson = """
            {
                "RLBD_NET_RATE": {
                    "MIN": "0.15",
                    "MAX": "1.00"
                },
                "paramCode": "RLBD_NET_RATE"
            }
            """;
        craftParamTransformService.craftParamsTransform(invalidJson);
    } catch (RuntimeException e) {
        System.out.println("字段缺失异常: " + e.getMessage());
        // 输出: 缺少必要字段: taskCode
    }
}
```

## 性能考虑

### 递归深度
- 理论上支持无限嵌套，但实际受JVM栈深度限制
- 建议嵌套层级不超过100层

### 内存使用
- 每次递归调用会创建新的字符串（`paramNode.toString()`）
- 大数据量时建议分批处理

### 优化建议
```java
// 对于大量数据，建议分批处理
public void processBatchData(List<String> jsonList) {
    int batchSize = 100;
    for (int i = 0; i < jsonList.size(); i += batchSize) {
        List<String> batch = jsonList.subList(i, 
            Math.min(i + batchSize, jsonList.size()));
        
        for (String json : batch) {
            craftParamTransformService.craftParamsTransform(json);
        }
        
        // 可选：添加延迟避免数据库压力
        Thread.sleep(100);
    }
}
```

## 总结

新的实现提供了更灵活的输入处理方式：
- ✅ 支持单个对象和数组格式
- ✅ 递归处理嵌套结构
- ✅ 统一的错误处理机制
- ✅ 保持向后兼容性
