# 使用基础镜像
FROM registry.cn-qingdao.aliyuncs.com/dataease/alpine-openjdk21-jre

RUN mkdir -p /opt/apps/

# 设置工作目录
WORKDIR /opt/apps

# 复制打包好的应用
ADD target/CraftApplication.jar /opt/apps/app.jar
ADD target/libs /opt/apps/libs

ENV JAVA_APP_JAR=/opt/apps/app.jar
ENV RUNNING_PORT=7900
ENV JAVA_OPTIONS="-Dfile.encoding=utf-8 -Dloader.path=/opt/apps"

HEALTHCHECK --interval=15s --timeout=5s --retries=20 --start-period=30s CMD nc -zv 127.0.0.1 $RUNNING_PORT


# 暴露端口
EXPOSE 7900

# 启动应用
ENTRYPOINT ["java","-Dloader.path=/opt/apps/libs", "-jar", "app.jar"]