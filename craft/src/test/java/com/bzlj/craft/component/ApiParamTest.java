package com.bzlj.craft.component;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import com.bzlj.craft.component.def.FormAttrDTO;
import com.bzlj.craft.component.service.impl.ComponentDealServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

/**
 * API参数测试类
 * 用于验证新的ApiParam注解功能
 */
@SpringBootTest
public class ApiParamTest {

    /**
     * 测试用的VO类，展示新的ApiParam用法
     */
    public static class TestVO {
        @ComponentProperty(
            label = "测试字段", 
            path = "testField", 
            dataSourceType = DataSourceType.API,
            dataSource = @ComponentProperty.DataSource(
                apiUrl = "/api/test",
                httpMethod = HttpMethod.POST,
                apiParams = {
                    @ComponentProperty.ApiParam(
                        key = "params", 
                        value = "{\"status\":\"active\",\"limit\":10}"
                    ),
                    @ComponentProperty.ApiParam(
                        key = "dependencyParams", 
                        value = "{\"userId\":\"${currentUserId}\",\"roleId\":\"${currentRoleId}\"}"
                    ),
                    @ComponentProperty.ApiParam(
                        key = "apiParams", 
                        value = "{\"version\":\"v1\",\"format\":\"json\"}"
                    )
                },
                mapping = @ComponentProperty.Mapping(
                    valueKey = "id",
                    labelKey = "name"
                )
            )
        )
        private String testField;
    }

    @Test
    public void testApiParamStructure() {
        // 这个测试主要是验证注解结构是否正确
        // 实际的功能测试需要在集成测试中进行

        ComponentProperty.DataSource dataSource = TestVO.class
            .getDeclaredFields()[0]
            .getAnnotation(ComponentProperty.class)
            .dataSource();

        ComponentProperty.ApiParam[] apiParams = dataSource.apiParams();

        // 验证参数数量
        assert apiParams.length == 3;

        // 验证参数类型和值
        for (ComponentProperty.ApiParam param : apiParams) {
            String key = param.key();
            String value = param.value();

            System.out.println("参数类型: " + key);
            System.out.println("参数值: " + value);

            // 验证key不为空
            assert key != null && !key.isEmpty();
            // 验证value不为空
            assert value != null && !value.isEmpty();
        }

        System.out.println("ApiParam结构测试通过！");
    }

    @Test
    public void testApiSerialization() throws Exception {
        // 测试API对象的序列化结构
        FormAttrDTO.DataSource.Api api = FormAttrDTO.DataSource.Api.builder()
            .url("/craft/craft/findProcessByPlantCode")
            .method(com.bzlj.base.enums.HttpMethod.GET)
            .extraParams(Map.of(
                "dependencyParams", Map.of("plantCode", "plant.plantCode"),
                "apiParams", Map.of("version", "v1")
            ))
            .build();

        // 使用Jackson序列化
        com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
        String json = mapper.writeValueAsString(api);

        System.out.println("序列化结果: " + json);

        // 验证序列化结果包含期望的结构
        assert json.contains("\"url\":\"/craft/craft/findProcessByPlantCode\"");
        assert json.contains("\"method\":\"get\"");
        assert json.contains("\"dependencyParams\":");
        assert json.contains("\"plantCode\":\"plant.plantCode\"");

        System.out.println("API序列化测试通过！");
    }
}
