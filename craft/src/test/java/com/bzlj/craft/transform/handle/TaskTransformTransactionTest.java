package com.bzlj.craft.transform.handle;

import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.repository.ProductionTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 任务转换事务问题测试
 * <p>
 * 测试修复"方法执行失败了，但是事务却提交了"的问题
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class TaskTransformTransactionTest {

    @Autowired
    private TaskTransformHandle taskTransformHandle;

    @Autowired
    private ProductionTaskRepository productionTaskRepository;

    /**
     * 测试事务边界问题修复
     * 验证在transform方法中调用deleteTask后，事务不会提前提交
     */
    @Test
    public void testTransactionBoundaryFix() {
        // 准备测试数据 - 创建一个已存在的任务
        String existingTaskJson = """
            {
                "plantCode": "TEST_PLANT",
                "processCode": "TEST_PROCESS",
                "taskCode": "EXISTING_TASK_001",
                "taskName": "已存在的测试任务",
                "equipmentCode": "TEST_EQUIPMENT"
            }
            """;

        Map<String, Object> handleContext1 = new HashMap<>();
        handleContext1.put("messageId", "test-message-001");

        // 先创建一个任务
        assertDoesNotThrow(() -> {
            taskTransformHandle.transform(existingTaskJson, handleContext1);
        });

        // 验证任务已创建
        ProductionTask existingTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("EXISTING_TASK_001", false);
        assertNotNull(existingTask, "任务应该被创建");

        // 准备新的测试数据 - 使用相同的taskCode，这会触发删除逻辑
        String newTaskJson = """
            {
                "plantCode": "TEST_PLANT",
                "processCode": "TEST_PROCESS", 
                "taskCode": "EXISTING_TASK_001",
                "taskName": "新的测试任务",
                "equipmentCode": "TEST_EQUIPMENT"
            }
            """;

        Map<String, Object> handleContext2 = new HashMap<>();
        handleContext2.put("messageId", "test-message-002");

        // 执行transform，这应该会先标记删除旧任务，然后创建新任务
        assertDoesNotThrow(() -> {
            taskTransformHandle.transform(newTaskJson, handleContext2);
        });

        // 验证新任务已创建，旧任务被标记删除
        ProductionTask newTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("EXISTING_TASK_001", false);
        assertNotNull(newTask, "新任务应该被创建");
        assertEquals("新的测试任务", newTask.getTaskName(), "应该是新任务的名称");

        log.info("事务边界测试完成");
    }

    /**
     * 测试异常情况下的事务回滚
     */
    @Test
    public void testTransactionRollbackOnException() {
        // 准备无效的测试数据（缺少必要字段）
        String invalidJson = """
            {
                "taskCode": "INVALID_TASK_001",
                "taskName": "无效任务"
            }
            """;

        Map<String, Object> handleContext = new HashMap<>();
        handleContext.put("messageId", "test-message-003");

        // 执行测试，应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            taskTransformHandle.transform(invalidJson, handleContext);
        });

        // 验证数据没有被保存（事务已回滚）
        ProductionTask savedTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("INVALID_TASK_001", false);
        assertNull(savedTask, "由于事务回滚，任务不应该被保存");

        log.info("事务回滚测试完成");
    }

    /**
     * 测试延迟删除机制
     */
    @Test
    public void testDelayedDeletionMechanism() {
        // 创建一个任务
        String taskJson = """
            {
                "plantCode": "TEST_PLANT",
                "processCode": "TEST_PROCESS",
                "taskCode": "DELAYED_DELETE_TASK",
                "taskName": "延迟删除测试任务",
                "equipmentCode": "TEST_EQUIPMENT"
            }
            """;

        Map<String, Object> handleContext = new HashMap<>();
        handleContext.put("messageId", "test-message-004");

        // 创建任务
        assertDoesNotThrow(() -> {
            taskTransformHandle.transform(taskJson, handleContext);
        });

        // 验证任务已创建
        ProductionTask task = productionTaskRepository.findFirstByTaskCodeAndDeleted("DELAYED_DELETE_TASK", false);
        assertNotNull(task, "任务应该被创建");

        // 再次执行相同的transform，触发延迟删除
        Map<String, Object> handleContext2 = new HashMap<>();
        handleContext2.put("messageId", "test-message-005");

        assertDoesNotThrow(() -> {
            taskTransformHandle.transform(taskJson, handleContext2);
        });

        // 验证handleContext中包含了要删除的任务ID
        String taskToDelete = (String) handleContext2.get("taskToDelete");
        assertNotNull(taskToDelete, "应该有要删除的任务ID");

        log.info("延迟删除机制测试完成，待删除任务ID: {}", taskToDelete);
    }

    /**
     * 测试并发场景下的事务处理
     */
    @Test
    public void testConcurrentTransactionHandling() {
        // 模拟并发创建相同taskCode的任务
        String taskJson = """
            {
                "plantCode": "TEST_PLANT",
                "processCode": "TEST_PROCESS",
                "taskCode": "CONCURRENT_TASK",
                "taskName": "并发测试任务",
                "equipmentCode": "TEST_EQUIPMENT"
            }
            """;

        // 并发执行多次
        for (int i = 0; i < 3; i++) {
            final int index = i;
            Map<String, Object> handleContext = new HashMap<>();
            handleContext.put("messageId", "test-message-concurrent-" + index);

            assertDoesNotThrow(() -> {
                taskTransformHandle.transform(taskJson, handleContext);
                log.info("并发测试 {} 完成", index);
            });
        }

        // 验证最终只有一个有效任务
        ProductionTask finalTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("CONCURRENT_TASK", false);
        assertNotNull(finalTask, "应该有一个最终的有效任务");

        log.info("并发事务处理测试完成");
    }
}
