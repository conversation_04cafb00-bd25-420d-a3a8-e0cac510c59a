package com.bzlj.craft.transform;

import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.transform.service.CraftParamTransformService;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

/**
 * CraftParamTransformService测试类
 * <AUTHOR>
 * @date 2025-07-30
 */
public class CraftParamTransformServiceTest extends BaseTestNGTest {

    @Autowired
    private CraftParamTransformService craftParamTransformService;

    @Test
    public void testCraftParamsTransformWithArray() {
        // 测试JSON数据
        String json = """
            [{
                "RLBD_NET_RATE": {
                    "MIN": "0.15",
                    "MAX": "1.00"
                },
                "taskCode": "25RD70574",
                "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
                "processCode": "GDL",
                "PROD_HEAT_TEMP": {
                    "MIN": "1050",
                    "MAX": "1100"
                }
            }]
            """;

        try {
            craftParamTransformService.craftParamsTransform(json);
            System.out.println("工艺参数转换成功（数组格式）");
        } catch (Exception e) {
            System.err.println("工艺参数转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCraftParamsTransformWithSingleObject() {
        // 测试单个JSON对象数据
        String json = """
            {
                "RLBD_NET_RATE": {
                    "MIN": "0.15",
                    "MAX": "1.00"
                },
                "taskCode": "25RD70574",
                "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
                "processCode": "GDL",
                "PROD_HEAT_TEMP": {
                    "MIN": "1050",
                    "MAX": "1100"
                }
            }
            """;

        try {
            craftParamTransformService.craftParamsTransform(json);
            System.out.println("工艺参数转换成功（单个对象格式）");
        } catch (Exception e) {
            System.err.println("工艺参数转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCraftParamsTransformWithInvalidJson() {
        // 测试无效JSON
        String invalidJson = "invalid json";

        try {
            craftParamTransformService.craftParamsTransform(invalidJson);
        } catch (RuntimeException e) {
            System.out.println("预期的异常: " + e.getMessage());
            assert e.getMessage().contains("工艺参数转换失败");
        }
    }

    @Test
    public void testCraftParamsTransformWithEmptyArray() {
        // 测试空数组
        String emptyArrayJson = "[]";

        try {
            craftParamTransformService.craftParamsTransform(emptyArrayJson);
        } catch (RuntimeException e) {
            System.out.println("预期的异常: " + e.getMessage());
            assert e.getMessage().contains("参数数组不能为空");
        }
    }

    @Test
    public void testCraftParamsTransformWithMissingFields() {
        // 测试缺少必要字段（单个对象格式）
        String missingFieldsJson = """
            {
                "RLBD_NET_RATE": {
                    "MIN": "0.15",
                    "MAX": "1.00"
                },
                "paramCode": "PROD_HEAT_TEMP,RLBD_NET_RATE",
                "processCode": "GDL"
            }
            """;

        try {
            craftParamTransformService.craftParamsTransform(missingFieldsJson);
        } catch (RuntimeException e) {
            System.out.println("预期的异常: " + e.getMessage());
            assert e.getMessage().contains("缺少必要字段");
        }
    }

    @Test
    public void testCraftParamsTransformWithMultipleObjects() {
        // 测试多个对象的数组
        String multipleObjectsJson = """
            [{
                "RLBD_NET_RATE": {
                    "MIN": "0.15",
                    "MAX": "1.00"
                },
                "taskCode": "25RD70574",
                "paramCode": "RLBD_NET_RATE",
                "processCode": "GDL"
            }, {
                "PROD_HEAT_TEMP": {
                    "MIN": "1050",
                    "MAX": "1100"
                },
                "taskCode": "25RD70575",
                "paramCode": "PROD_HEAT_TEMP",
                "processCode": "GDL"
            }]
            """;

        try {
            craftParamTransformService.craftParamsTransform(multipleObjectsJson);
            System.out.println("多个对象工艺参数转换成功");
        } catch (Exception e) {
            System.err.println("多个对象工艺参数转换失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
