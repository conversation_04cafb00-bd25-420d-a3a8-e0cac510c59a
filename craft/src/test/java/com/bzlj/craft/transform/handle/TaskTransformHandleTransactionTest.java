package com.bzlj.craft.transform.handle;

import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.repository.ProductionTaskRepository;
import com.bzlj.craft.util.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 任务转换处理器事务管理测试
 * <p>
 * 测试事务管理的正确性，包括：
 * 1. 正常情况下的事务提交
 * 2. 异常情况下的事务回滚
 * 3. 独立事务的执行
 * 4. 事务边界的正确处理
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class TaskTransformHandleTransactionTest {

    @Autowired
    private TaskTransformHandle taskTransformHandle;

    @Autowired
    private ProductionTaskRepository productionTaskRepository;

    @Autowired
    private TransactionUtils transactionUtils;

    /**
     * 测试正常情况下的事务提交
     */
    @Test
    public void testTransactionCommit() {
        // 准备测试数据
        String validJson = """
            {
                "plantCode": "TEST_PLANT",
                "processCode": "TEST_PROCESS",
                "taskCode": "TEST_TASK_001",
                "taskName": "测试任务",
                "equipmentCode": "TEST_EQUIPMENT"
            }
            """;

        Map<String, Object> handleContext = new HashMap<>();
        handleContext.put("messageId", "test-message-001");

        // 执行测试
        assertDoesNotThrow(() -> {
            taskTransformHandle.transform(validJson, handleContext);
        });

        // 验证数据是否正确保存
        ProductionTask savedTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("TEST_TASK_001", false);
        assertNotNull(savedTask, "任务应该被正确保存");
        assertEquals("TEST_TASK_001", savedTask.getTaskCode());
        assertEquals("测试任务", savedTask.getTaskName());
    }

    /**
     * 测试异常情况下的事务回滚
     */
    @Test
    public void testTransactionRollback() {
        // 准备无效的测试数据（缺少必要字段）
        String invalidJson = """
            {
                "taskCode": "TEST_TASK_002",
                "taskName": "测试任务2"
            }
            """;

        Map<String, Object> handleContext = new HashMap<>();
        handleContext.put("messageId", "test-message-002");

        // 执行测试，应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            taskTransformHandle.transform(invalidJson, handleContext);
        });

        // 验证数据没有被保存（事务已回滚）
        ProductionTask savedTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("TEST_TASK_002", false);
        assertNull(savedTask, "由于事务回滚，任务不应该被保存");
    }

    /**
     * 测试事务工具的独立事务功能
     */
    @Test
    public void testIndependentTransaction() {
        // 测试在新事务中执行操作
        String result = transactionUtils.executeInNewTransaction(() -> {
            log.info("在新事务中执行操作");
            return "success";
        });

        assertEquals("success", result);

        // 测试在当前事务中执行操作
        String result2 = transactionUtils.executeInCurrentTransaction(() -> {
            log.info("在当前事务中执行操作");
            return "success2";
        });

        assertEquals("success2", result2);

        // 测试以非事务方式执行操作
        String result3 = transactionUtils.executeWithoutTransaction(() -> {
            log.info("以非事务方式执行操作");
            return "success3";
        });

        assertEquals("success3", result3);
    }

    /**
     * 测试事务状态检查
     */
    @Test
    @Transactional
    public void testTransactionStatusCheck() {
        // 在事务中检查状态
        boolean inTransaction = transactionUtils.isInTransaction();
        assertTrue(inTransaction, "应该检测到当前在事务中");

        // 测试标记事务回滚
        assertDoesNotThrow(() -> {
            transactionUtils.markCurrentTransactionRollbackOnly();
        });
    }

    /**
     * 测试复杂的事务场景
     * 模拟主业务成功，但后续处理失败的情况
     */
    @Test
    public void testComplexTransactionScenario() {
        // 准备测试数据
        String validJson = """
            {
                "plantCode": "TEST_PLANT",
                "processCode": "TEST_PROCESS", 
                "taskCode": "TEST_TASK_003",
                "taskName": "测试任务3",
                "equipmentCode": "TEST_EQUIPMENT"
            }
            """;

        Map<String, Object> handleContext = new HashMap<>();
        handleContext.put("messageId", "test-message-003");

        // 执行主业务
        assertDoesNotThrow(() -> {
            taskTransformHandle.transform(validJson, handleContext);
        });

        // 验证主业务数据已保存
        ProductionTask savedTask = productionTaskRepository.findFirstByTaskCodeAndDeleted("TEST_TASK_003", false);
        assertNotNull(savedTask, "主业务数据应该被正确保存");

        // 模拟后续处理（这些应该在独立事务中执行，不影响主业务）
        assertDoesNotThrow(() -> {
            taskTransformHandle.clearRelationData("test-message-003");
        });

        // 验证主业务数据仍然存在
        ProductionTask stillExists = productionTaskRepository.findFirstByTaskCodeAndDeleted("TEST_TASK_003", false);
        assertNotNull(stillExists, "即使后续处理失败，主业务数据也应该保持");
    }

    /**
     * 测试并发事务场景
     */
    @Test
    public void testConcurrentTransactions() {
        // 模拟并发执行多个事务
        for (int i = 0; i < 5; i++) {
            final int index = i;
            transactionUtils.executeInNewTransaction(() -> {
                log.info("执行并发事务 {}", index);
                // 模拟一些数据库操作
                try {
                    Thread.sleep(100); // 模拟处理时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return "success-" + index;
            });
        }

        log.info("所有并发事务执行完成");
    }
}
