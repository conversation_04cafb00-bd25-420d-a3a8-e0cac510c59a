package com.bzlj.craft.service;

import cn.hutool.extra.spring.SpringUtil;
import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.dto.SysDictDTO;
import com.bzlj.craft.dto.SysDictItemDTO;
import com.bzlj.craft.service.impl.SysDictItemServiceImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.AopTestUtils;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.spy;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-31 11:29
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SysDictItemServiceImplTest extends BaseTestNGTest{

    @Autowired
    @Spy
    private SysDictItemServiceImpl sysDictItemService;

    @BeforeEach
    public void setUp() {

        SysDictItemServiceImpl service =
                AopTestUtils.getUltimateTargetObject(SpringUtil.getBean(SysDictItemServiceImpl.class));
        sysDictItemService = spy(service);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getEntityManager() {
        assertNotNull(sysDictItemService.getEntityManager());
    }

    @Test
    void getRepository() {
        assertNotNull(sysDictItemService.getRepository());
    }

    @Test
    void getDTOClass() {
        assertNotNull(sysDictItemService.getDTOClass());
    }

    @Test
    void getPOClass() {
        assertNotNull(sysDictItemService.getPOClass());
    }

    @Test
    void findByDictCode() {
        sysDictItemService.findByDictCode("test");
    }

    @Test
    void findItemCodesByDictCode() {

        SysDictItemDTO sysDictItemDTO = new SysDictItemDTO();
        sysDictItemDTO.setDictCode(new SysDictDTO());
        sysDictItemDTO.setItemCode("test");
        sysDictItemDTO.setItemName("test");
        sysDictItemDTO.setSortOrder(1);

        doReturn(Lists.newArrayList(sysDictItemDTO)).when(sysDictItemService).findAllWithCondition(Mockito.any());
        sysDictItemService.findItemCodesByDictCode("test");
    }

    @Test
    void findItemCodesByDictCode1() {
        doReturn(Lists.newArrayList()).when(sysDictItemService).findAllWithCondition(Mockito.any());
        sysDictItemService.findItemCodesByDictCode("test");
    }
}