package com.bzlj.craft.service;

import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.query.QualityTraceQuery;
import com.bzlj.craft.vo.qualitytrace.QualityTraceVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import static org.testng.Assert.assertNotNull;
import static org.testng.Assert.assertTrue;

public class IQualityTracingServiceTest extends BaseTestNGTest {

    @Autowired
    private IQualityTracingService qualityTracingService;

    /**
     * 测试初始化星云图数据方法
     * 验证场景：
     * 1. 当所有数据正常时，初始化成功应返回true
     * 2. 验证所有依赖仓库的方法都被正确调用
     * 3. 验证事务注解是否生效
     */
    @Test(testName = "测试initNebulaData方法 - 成功场景")
    public void testInitNebulaData_Success() throws NoSuchMethodException {
        // 执行测试方法
        Boolean result = qualityTracingService.initNebulaData();
        // 验证结果和交互
        assertTrue(result);
    }

    @Test(testName = "测试getQualityTracing方法 - 正常场景")
    public void testGetQualityTracing_Success() {
        // 准备测试查询参数
        QualityTraceQuery query = new QualityTraceQuery();
        query.setMaterialCode("M001");

        // 执行测试方法
        QualityTraceVO result = qualityTracingService.getQualityTracing(query);

        assertNotNull(result);

    }
}