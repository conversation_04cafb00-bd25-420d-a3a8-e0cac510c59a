package com.bzlj.craft.service;

import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SortItem;
import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.mongo.entity.AbnormalRecord;
import com.bzlj.craft.mongo.entity.AlarmContent;
import com.bzlj.craft.mongo.entity.DeviationTracing;
import com.bzlj.craft.mongo.repository.AbnormalRecordRepository;
import com.bzlj.craft.mongo.repository.DeviationTracingRepository;
import com.bzlj.craft.util.JsonUtils;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-26 16:48
 */
class DeviationWarningImplTest extends BaseTestNGTest {


    @Autowired
    private IDeviationWarningService deviationWarningService;

    @Autowired
    private AbnormalRecordRepository abnormalRecordRepository;

    @Autowired
    private DeviationTracingRepository deviationTracingRepository;

    @Test
    void findAlarmList(){
        SearchCondition searchCondition = new SearchCondition();
//        searchCondition.setSearchItems(SearchItems.builder()
//                .item(new SearchItem("taskId", "TASK001", null, SearchItem.Operator.EQ))
//                .build());
        searchCondition.setPageSize(10);
        searchCondition.setPageCurrent(1);
        searchCondition.setSortItems(Lists.newArrayList(SortItem.builder().fieldName("alertType").sortOrder(SortItem.DESC_ORDER_NAME).build()));
        deviationWarningService.findAlarmList(JsonUtils.toJson(searchCondition));
    }

    @Test
    void insert(){
        AbnormalRecord abnormalRecord = new AbnormalRecord();
        abnormalRecord.setId("1");
        abnormalRecord.setAlertTime(LocalDateTime.now());
        abnormalRecord.setAlertType(true);
        abnormalRecord.setTaskId("TASK001");
        abnormalRecord.setTaskCode("TASK001");
        abnormalRecord.setProcessType("1");
        AlarmContent alarmContent = new AlarmContent();
        alarmContent.setParameterName("参数1");
        alarmContent.setAlarmContent("参数1异常");
        alarmContent.setSuggestion("建议1");
        alarmContent.setWorkStepName("工步1");
        abnormalRecord.setLevel(1);
        abnormalRecord.setAlarmContent(alarmContent);
        abnormalRecordRepository.insert(abnormalRecord);

    }

    @Test
    void insertTracing(){
        DeviationTracing deviationTracing = new DeviationTracing();
        deviationTracing.setId("1");
        deviationTracing.setTaskId("TASK_VAR_9");
        deviationTracing.setTaskCode("TASK_VAR_9");
        deviationTracing.setProcessType("锻造_径/快锻加热");
        deviationTracing.setPlantCode("锻造");
        deviationTracing.setEquipmentCode("831-076");
        deviationTracing.setEquipmentId("EQUIP040804");
        deviationTracing.setDuration(new BigDecimal(40));
        deviationTracing.setEquipmentName("挤压机");
        deviationTracing.setAveDegree(new BigDecimal(20));
        deviationTracing.setMaxDegree(new BigDecimal(30));
        deviationTracing.setStepName("加热");
        deviationTracing.setStepId("320816534471708672_2");
        deviationTracing.setParameterName("熔速");
        deviationTracing.setParameterId("EDC658473F30B211");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        deviationTracing.setStartTime(LocalDateTime.parse("2025-06-05 18:00:00",dateTimeFormatter));
        deviationTracing.setEndTime(LocalDateTime.parse("2025-06-05 18:40:00",dateTimeFormatter));

        deviationTracingRepository.insert(deviationTracing);

    }
}