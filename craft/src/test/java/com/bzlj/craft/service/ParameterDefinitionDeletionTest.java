package com.bzlj.craft.service;

import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.service.impl.SurveillanceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 参数定义删除功能测试
 */
@ExtendWith(MockitoExtension.class)
class ParameterDefinitionDeletionTest {

    @Mock
    private WorkStepRepository workStepRepository;

    @Mock
    private ParameterDefinitionRepository parameterDefinitionRepository;

    @Mock
    private ParameterDefinitionExtendRepository parameterDefinitionExtendRepository;

    @Mock
    private ProcessParameterRepository processParameterRepository;

    @InjectMocks
    private SurveillanceServiceImpl surveillanceService;

    private WorkStep workStep1;
    private WorkStep workStep2;
    private ParameterDefinition paramDef1;
    private ParameterDefinition paramDef2;
    private ParameterDefinitionExtend paramDefExtend1;
    private ProcessParameter processParam1;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        workStep1 = new WorkStep();
        workStep1.setWorkStepId("WS001");
        workStep1.setDeleted(false);

        workStep2 = new WorkStep();
        workStep2.setWorkStepId("WS002");
        workStep2.setDeleted(false);

        paramDef1 = new ParameterDefinition();
        paramDef1.setParamDefId("PD001");
        paramDef1.setWorkStep(workStep1);
        paramDef1.setDeleted(false);

        paramDef2 = new ParameterDefinition();
        paramDef2.setParamDefId("PD002");
        paramDef2.setWorkStep(workStep2);
        paramDef2.setDeleted(false);

        paramDefExtend1 = new ParameterDefinitionExtend();
        paramDefExtend1.setId("PDE001");
        paramDefExtend1.setParamDefId("PD001");
        paramDefExtend1.setDeleted(false);

        processParam1 = new ProcessParameter();
        processParam1.setParamId("PP001");
        processParam1.setParamDef(paramDef1);
    }

    @Test
    void testDeleteWorkStepWithParameterDefinitions() {
        // Given
        String taskId = "TASK001";
        List<WorkStep> workSteps = Arrays.asList(workStep1, workStep2);
        List<String> workStepIds = Arrays.asList("WS001", "WS002");
        List<ParameterDefinition> parameterDefinitions = Arrays.asList(paramDef1, paramDef2);
        List<String> paramDefIds = Arrays.asList("PD001", "PD002");
        List<ParameterDefinitionExtend> paramDefExtends = Arrays.asList(paramDefExtend1);
        List<ProcessParameter> processParams = Arrays.asList(processParam1);

        // Mock repository calls
        when(workStepRepository.findByTaskTaskIdAndDeleted(taskId, false))
                .thenReturn(workSteps);
        when(parameterDefinitionRepository.findByWorkStepWorkStepIdInAndDeleted(workStepIds, false))
                .thenReturn(parameterDefinitions);
        when(parameterDefinitionExtendRepository.findByParamDefIdInAndDeleted(paramDefIds, false))
                .thenReturn(paramDefExtends);
        when(processParameterRepository.findByParamDefParamDefIdIn(paramDefIds))
                .thenReturn(processParams);

        // When
        // 使用反射调用私有方法，或者创建一个公共的测试方法
        // 这里我们假设有一个公共的deleteTask方法来测试
        // surveillanceService.deleteTask(taskId);

        // Then - 验证调用顺序和参数
        // 1. 查询工步
        verify(workStepRepository).findByTaskTaskIdAndDeleted(taskId, false);
        
        // 2. 查询参数定义
        verify(parameterDefinitionRepository).findByWorkStepWorkStepIdInAndDeleted(workStepIds, false);
        
        // 3. 查询参数定义扩展
        verify(parameterDefinitionExtendRepository).findByParamDefIdInAndDeleted(paramDefIds, false);
        
        // 4. 查询工艺参数
        verify(processParameterRepository).findByParamDefParamDefIdIn(paramDefIds);
        
        // 5. 删除工艺参数（物理删除）
        verify(processParameterRepository).deleteAll(processParams);
        
        // 6. 保存参数定义扩展（逻辑删除）
        verify(parameterDefinitionExtendRepository).saveAll(argThat(extends -> {
            return extends.stream().allMatch(extend -> extend.getDeleted());
        }));
        
        // 7. 保存参数定义（逻辑删除）
        verify(parameterDefinitionRepository).saveAll(argThat(paramDefs -> {
            return paramDefs.stream().allMatch(paramDef -> paramDef.getDeleted());
        }));
        
        // 8. 保存工步（逻辑删除）
        verify(workStepRepository).saveAll(argThat(steps -> {
            return steps.stream().allMatch(step -> step.getDeleted());
        }));
    }

    @Test
    void testDeleteWorkStepWithNoParameterDefinitions() {
        // Given
        String taskId = "TASK002";
        List<WorkStep> workSteps = Arrays.asList(workStep1);
        List<String> workStepIds = Arrays.asList("WS001");

        when(workStepRepository.findByTaskTaskIdAndDeleted(taskId, false))
                .thenReturn(workSteps);
        when(parameterDefinitionRepository.findByWorkStepWorkStepIdInAndDeleted(workStepIds, false))
                .thenReturn(Collections.emptyList());

        // When
        // surveillanceService.deleteTask(taskId);

        // Then
        verify(workStepRepository).findByTaskTaskIdAndDeleted(taskId, false);
        verify(parameterDefinitionRepository).findByWorkStepWorkStepIdInAndDeleted(workStepIds, false);
        
        // 由于没有参数定义，不应该调用扩展和工艺参数的删除方法
        verify(parameterDefinitionExtendRepository, never()).findByParamDefIdInAndDeleted(any(), any());
        verify(processParameterRepository, never()).findByParamDefParamDefIdIn(any());
        verify(processParameterRepository, never()).deleteAll(any());
        verify(parameterDefinitionExtendRepository, never()).saveAll(any());
        verify(parameterDefinitionRepository, never()).saveAll(any());
        
        // 但仍然应该删除工步
        verify(workStepRepository).saveAll(argThat(steps -> {
            return steps.stream().allMatch(step -> step.getDeleted());
        }));
    }

    @Test
    void testDeleteWorkStepWithEmptyWorkSteps() {
        // Given
        String taskId = "TASK003";

        when(workStepRepository.findByTaskTaskIdAndDeleted(taskId, false))
                .thenReturn(Collections.emptyList());

        // When
        // surveillanceService.deleteTask(taskId);

        // Then
        verify(workStepRepository).findByTaskTaskIdAndDeleted(taskId, false);
        
        // 由于没有工步，不应该调用任何其他删除方法
        verify(parameterDefinitionRepository, never()).findByWorkStepWorkStepIdInAndDeleted(any(), any());
        verify(parameterDefinitionExtendRepository, never()).findByParamDefIdInAndDeleted(any(), any());
        verify(processParameterRepository, never()).findByParamDefParamDefIdIn(any());
        verify(processParameterRepository, never()).deleteAll(any());
        verify(parameterDefinitionExtendRepository, never()).saveAll(any());
        verify(parameterDefinitionRepository, never()).saveAll(any());
        verify(workStepRepository, never()).saveAll(any());
    }
}
