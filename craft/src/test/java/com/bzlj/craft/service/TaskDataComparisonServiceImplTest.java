package com.bzlj.craft.service;


import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.dto.ContinuousStepDTO;
import com.bzlj.craft.dto.TaskDetailDTO;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;


/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-12 10:59
 */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class TaskDataComparisonServiceImplTest extends BaseTestNGTest {

    @Autowired
    private ITaskDataComparisonService taskDataComparisonService;



    @Test
    void paramComparison(){
        List<String> taskIds = Lists.newArrayList("TASK321001","TASK325005","TASK325006");
        List<TaskDetailDTO> taskDetailDTOS = taskDataComparisonService.paramComparison(taskIds);
        System.out.println(taskDetailDTOS);
    }

    @Test
    void continuousParams(){
        List<ContinuousStepDTO> continuousParams = taskDataComparisonService.continuousParams("TASK_VAR_9");
        System.out.println(continuousParams);
    }

}