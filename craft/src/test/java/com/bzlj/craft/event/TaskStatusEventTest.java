package com.bzlj.craft.event;

import com.bzlj.craft.entity.SysDictItem;
import com.bzlj.craft.event.listener.TaskStatusChangeEventListener;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 任务状态事件测试类
 * <p>
 * 测试任务状态变更事件的发布和处理功能
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
class TaskStatusEventTest {

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Mock
    private TaskStatusChangeEventListener eventListener;

    @Test
    void testTaskStatusChangeEventCreation() {
        // 准备测试数据
        String taskId = "task-001";
        String taskCode = "T001";
        SysDictItem oldStatus = createSysDictItem("not_start", "未开始");
        SysDictItem newStatus = createSysDictItem("in_progress", "进行中");
        String operatorId = "user-001";
        String changeReason = "手动启动任务";

        // 创建事件
        TaskStatusChangeEvent event = new TaskStatusChangeEvent(
            this, taskId, taskCode, oldStatus, newStatus, operatorId, changeReason
        );

        // 验证事件属性
        assertEquals(taskId, event.getTaskId());
        assertEquals(taskCode, event.getTaskCode());
        assertEquals(oldStatus, event.getOldStatus());
        assertEquals(newStatus, event.getNewStatus());
        assertEquals(operatorId, event.getOperatorId());
        assertEquals(changeReason, event.getChangeReason());
        assertNotNull(event.getChangeTime());
        assertTrue(event.isTaskStarted());
        assertFalse(event.isTaskCompleted());
        assertFalse(event.isTaskCancelled());
    }

    @Test
    void testTaskStatusEventPublisher() {
        // 创建发布器
        TaskStatusEventPublisher publisher = new TaskStatusEventPublisher();
        
        // 使用反射设置mock的ApplicationEventPublisher
        try {
            java.lang.reflect.Field field = TaskStatusEventPublisher.class.getDeclaredField("applicationEventPublisher");
            field.setAccessible(true);
            field.set(publisher, applicationEventPublisher);
        } catch (Exception e) {
            fail("设置ApplicationEventPublisher失败: " + e.getMessage());
        }

        // 准备测试数据
        String taskId = "task-002";
        String taskCode = "T002";
        SysDictItem oldStatus = createSysDictItem("in_progress", "进行中");
        SysDictItem newStatus = createSysDictItem("completed", "已完成");

        // 发布事件
        publisher.publishTaskStatusChangeEvent(taskId, taskCode, oldStatus, newStatus);

        // 验证事件发布
        ArgumentCaptor<TaskStatusChangeEvent> eventCaptor = ArgumentCaptor.forClass(TaskStatusChangeEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        TaskStatusChangeEvent capturedEvent = eventCaptor.getValue();
        assertEquals(taskId, capturedEvent.getTaskId());
        assertEquals(taskCode, capturedEvent.getTaskCode());
        assertEquals(oldStatus, capturedEvent.getOldStatus());
        assertEquals(newStatus, capturedEvent.getNewStatus());
        assertTrue(capturedEvent.isTaskCompleted());
    }

    @Test
    void testTaskStatusChangeEventDescription() {
        // 准备测试数据
        SysDictItem oldStatus = createSysDictItem("not_start", "未开始");
        SysDictItem newStatus = createSysDictItem("in_progress", "进行中");

        TaskStatusChangeEvent event = new TaskStatusChangeEvent(
            this, "task-003", "T003", oldStatus, newStatus
        );

        // 验证描述信息
        String description = event.getChangeDescription();
        assertTrue(description.contains("T003"));
        assertTrue(description.contains("未开始"));
        assertTrue(description.contains("进行中"));
    }

    @Test
    void testTaskStatusEventTypes() {
        // 测试任务开始事件
        TaskStatusChangeEvent startEvent = new TaskStatusChangeEvent(
            this, "task-004", "T004", 
            createSysDictItem("not_start", "未开始"),
            createSysDictItem("in_progress", "进行中")
        );
        assertTrue(startEvent.isTaskStarted());
        assertFalse(startEvent.isTaskCompleted());
        assertFalse(startEvent.isTaskCancelled());

        // 测试任务完成事件
        TaskStatusChangeEvent completeEvent = new TaskStatusChangeEvent(
            this, "task-005", "T005",
            createSysDictItem("in_progress", "进行中"),
            createSysDictItem("completed", "已完成")
        );
        assertFalse(completeEvent.isTaskStarted());
        assertTrue(completeEvent.isTaskCompleted());
        assertFalse(completeEvent.isTaskCancelled());

        // 测试任务取消事件
        TaskStatusChangeEvent cancelEvent = new TaskStatusChangeEvent(
            this, "task-006", "T006",
            createSysDictItem("in_progress", "进行中"),
            createSysDictItem("cancelled", "已取消")
        );
        assertFalse(cancelEvent.isTaskStarted());
        assertFalse(cancelEvent.isTaskCompleted());
        assertTrue(cancelEvent.isTaskCancelled());
    }

    /**
     * 创建测试用的SysDictItem对象
     */
    private SysDictItem createSysDictItem(String itemCode, String itemName) {
        SysDictItem dictItem = new SysDictItem();
        dictItem.setItemCode(itemCode);
        dictItem.setItemName(itemName);
        return dictItem;
    }
}
