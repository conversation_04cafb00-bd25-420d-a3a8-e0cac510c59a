package com.bzlj.craft.component.exec;

import com.bzlj.craft.component.command.ComponentRequest;
import com.bzlj.craft.component.result.ComponentResult;
import reactor.core.publisher.Flux;


/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 15:41
 */
public interface IComponentExecService {
    Flux<ComponentResult> functionExec(ComponentRequest request,String taskId);


    default ComponentResult handleException(String componentId,Exception e) {
        String errorMsg = e.getMessage();
        return new ComponentResult(
                componentId,
                null,
                null,
                true,
                errorMsg,true
        );
    }
}
