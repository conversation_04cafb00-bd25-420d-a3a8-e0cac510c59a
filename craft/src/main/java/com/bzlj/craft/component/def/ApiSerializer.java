package com.bzlj.craft.component.def;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Map;

/**
 * FormAttrDTO.DataSource.Api的自定义序列化器
 * 将extraParams中的参数提升到api对象的顶层
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class ApiSerializer extends JsonSerializer<FormAttrDTO.DataSource.Api> {

    @Override
    public void serialize(FormAttrDTO.DataSource.Api api, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeStartObject();
        
        // 写入基本字段
        gen.writeStringField("url", api.getUrl());
        if (api.getMethod() != null) {
            gen.writeStringField("method", api.getMethod().name().toLowerCase());
        }
        
        // 写入extraParams中的所有参数到顶层
        if (api.getParams() != null && !api.getParams().isEmpty()) {
            for (Map.Entry<String, Object> entry : api.getParams().entrySet()) {
                gen.writeObjectField(entry.getKey(), entry.getValue());
            }
        }
        
        gen.writeEndObject();
    }
}
