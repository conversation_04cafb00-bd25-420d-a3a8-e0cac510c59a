package com.bzlj.craft.component.factory;

import cn.hutool.extra.spring.SpringUtil;
import com.bzlj.craft.component.exec.IComponentExecService;
import com.bzlj.craft.component.exec.impl.ComponentExecServiceImpl;
import com.google.common.collect.Maps;

import java.lang.reflect.Method;
import java.util.Map;


public class ComponentFactory {
    /**
     * 方法缓存,避免无限通过反射去获取方法
     */
    private static final Map<String, Method> METHOD_CACHE = Maps.newConcurrentMap();
    /**
     * 类缓存
     */
    private static final Map<String, Object> CLASS_CACHE = Maps.newConcurrentMap();

    public static void cacheMethod(String name, Method method) {
        if (!METHOD_CACHE.containsKey(name)) {
            METHOD_CACHE.put(name, method);
        }
    }

    public static Method getMethod(String name) {
        if (METHOD_CACHE.containsKey(name)) {
            return METHOD_CACHE.get(name);
        }
        return null;
    }

    public static void cacheClass(String name, Object obj) {
        if (!CLASS_CACHE.containsKey(name)) {
            CLASS_CACHE.put(name, obj);
        }
    }

    public static Object getClass(String name) {
        if (CLASS_CACHE.containsKey(name)) {
            return CLASS_CACHE.get(name);
        }
        return null;
    }

    public static IComponentExecService getExecService() {
        return SpringUtil.getBean(ComponentExecServiceImpl.class);

    }
}
