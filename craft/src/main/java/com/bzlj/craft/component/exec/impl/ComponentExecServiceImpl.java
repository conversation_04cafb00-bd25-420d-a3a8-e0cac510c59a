package com.bzlj.craft.component.exec.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.bzlj.base.result.DataResult;
import com.bzlj.craft.component.command.ComponentRequest;
import com.bzlj.craft.component.exec.IComponentExecService;
import com.bzlj.craft.component.factory.ComponentFactory;
import com.bzlj.craft.component.result.ComponentResult;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.service.ISurveillanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Arrays;

import static com.bzlj.craft.exception.BiciErrorData.ERROR_RESPONSE;
import static com.bzlj.craft.exception.BiciErrorData.FETCH_COMPONENT_METHOD_ERROR;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 15:44
 */
@Service
@Slf4j
public class ComponentExecServiceImpl implements IComponentExecService {

    @Autowired
    private ISurveillanceService surveillanceService;

    @Override
    public Flux<ComponentResult> functionExec(ComponentRequest request,String taskId) {
        return Mono.fromCallable(() -> {
                            try {
                               return exec(request,taskId);
                            } catch (Exception e) {
                               return handleException(request.getComponentId(),e);
                            }
                        }
                )
                .timeout(Duration.ofSeconds(5))
                .retryWhen(Retry.backoff(2, Duration.ofMillis(300)))
                .flux();
    }


    private ComponentResult exec(ComponentRequest request,String taskId) {
        String[] parts = request.getMethodPath().split("\\.(?=[^\\.]+$)");
        String className = String.join(".", Arrays.copyOf(parts, parts.length - 1));
        String methodName = parts[parts.length - 1];
        Method method = getMethod(request.getMethodPath(), className, methodName);
        Object invokeData;
        DataResult result = null;
        try {
            String params = request.getParams();
            invokeData = method.invoke(ComponentFactory.getClass(className), params);
            result = (DataResult) invokeData;
        } catch (Exception e) {
            log.error("{}", e.getStackTrace(), e);
            throw FETCH_COMPONENT_METHOD_ERROR.buildException(e.getMessage());
        }
        boolean stopLooping = checkStopLopping(taskId,request.isLoop());

        return new ComponentResult(
                request.getComponentId(),
                null,
                result
                , false,  null, stopLooping
        );
    }

    /**
     * 检查是否停止循环
     * @param taskId
     * @param loop
     * @return
     */
    private boolean checkStopLopping(String taskId,boolean loop){
        if (!loop) {
            return true;
        }else {
            if(StringUtils.isEmpty(taskId)){
                return false;
            }
            String taskStatus = surveillanceService.findTaskStatus(taskId);
            return StringUtils.equals(taskStatus, TaskStatus.completed.getCode());
        }
    }

    public Method getMethod(String methodPath, String className, String methodName) {
        if (ComponentFactory.getMethod(methodPath) != null) {
            return ComponentFactory.getMethod(methodPath);
        }
        Class<?> aimClazz;
        if (!ObjectUtils.isEmpty(ComponentFactory.getClass(className))) {
            aimClazz = ComponentFactory.getClass(className).getClass();
        } else {
            Class<?> clazz = null;
            try {
                clazz = Class.forName(className);
                Object bean = SpringUtil.getBean(clazz);
                if (ObjectUtils.isEmpty(bean)) {
                    bean = clazz.newInstance();
                }
                aimClazz = bean.getClass();
                ComponentFactory.cacheClass(className, bean);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        Method method = null;
        try {
            method = aimClazz.getMethod(methodName, String.class);
            if (!method.getReturnType().equals(DataResult.class)) {
                throw ERROR_RESPONSE.buildException();
            }
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        ComponentFactory.cacheMethod(methodPath, method);
        return method;
    }
}
