package com.bzlj.craft.component.def;

import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import com.bzlj.craft.dto.SysDictItemDTO;
import com.bzlj.craft.enums.TypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表单属性
 * <AUTHOR>
 * @description:
 * @date 2025-03-07 11:15
 */
@Data
@Builder
public class FormAttrDTO {
    /**
     * 属性名称
     */
    private String dataIndex;

    /**
     * 属性标签
     */
    private String label;

    /**
     * 字段类型
     */
    private TypeEnum type;

    /**
     * 当前属性是否支持搜索
     */
    private boolean searchable;

    /**
     * 属性排序
     */
    private int order;

    /**
     * 是否展示
     */
    private boolean display;

    /**
     * 是否范围
     */
    private boolean range;

    /**
     * 属性字典项
     */
    private List<SysDictItemDTO> dictItems;

    /**
     * 属性格式
     */
    private String format;

    /**
     * 属性别名
     */
    private String alias;

    /**
     * 是否支持多选
     */
    private Boolean multiple;

    private Boolean initialRequest;

    private List<String> dependencies;


    /**
     * 子属性集合
     */
    private List<FormAttrDTO> children;

    private DataSourceType dataSourceType;

    private DataSource dataSource;

    public static TypeEnum formType(String type) {
        return switch (type) {
            case "Integer", "Long", "Float", "Double", "BigDecimal" -> TypeEnum.number;
            case "Date", "LocalDateTime" -> TypeEnum.datetime;
            case "Boolean" -> TypeEnum.bool;
            case "String" -> TypeEnum.string;
            default -> TypeEnum.obj;
        };
    }

    @Data
    @Builder
    public static class DataSource {
        private Api api;
        private Mapping mapping;
        @Data
        @Builder
        public static class Mapping {
            private String valueKey;
            private String labelKey;
        }

        @Data
        @Builder
        @JsonSerialize(using = ApiSerializer.class)
        public static class Api {
            private String url;
            private HttpMethod method;

            /**
             * 额外的参数字段，用于存储dependencyParams、apiParams等
             * 这些参数会在序列化时与url、method平级输出
             */
            @Builder.Default
            private Map<String, Object> params = new HashMap<>();
        }
    }


}
