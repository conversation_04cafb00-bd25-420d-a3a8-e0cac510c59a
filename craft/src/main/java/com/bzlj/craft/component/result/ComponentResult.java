package com.bzlj.craft.component.result;

import com.bzlj.base.result.DataResult;
import com.bzlj.craft.component.def.FormAttrDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 16:49
 */
@Data
@AllArgsConstructor
public class ComponentResult {

    private String componentId;

    private List<FormAttrDTO> formAttrDTOS;

    /**
     * 字段的数据
     */
    private DataResult result;

    private boolean hasError;

    private String errorMsg;

    private boolean stopLooping;
}
