package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "parameter_numeric")
public class ParameterNumeric {
    @Id
    @Column(name = "numeric_id", nullable = false)
    private Long id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "param_id", nullable = false)
    private StepParameter param;

    @NotNull
    @Column(name = "standard_value", nullable = false, precision = 30, scale = 10)
    private BigDecimal standardValue;

    @Column(name = "deviation_range", precision = 30, scale = 10)
    private BigDecimal deviationRange;

    @Size(max = 10)
    @NotNull
    @Column(name = "unit", nullable = false, length = 10)
    private String unit;

    @NotNull
    @Column(name = "min_value", nullable = false, precision = 30, scale = 10)
    private BigDecimal minValue;

    @NotNull
    @Column(name = "max_value", nullable = false, precision = 30, scale = 10)
    private BigDecimal maxValue;

    @Column(name = "center_value", precision = 30, scale = 10)
    private BigDecimal centerValue;

    @ColumnDefault("2")
    @Column(name = "decimal_places")
    private Byte decimalPlaces;

    @Size(max = 100)
    @Column(name = "measurement_method", length = 100)
    private String measurementMethod;

}