package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "equipment")
public class Equipment implements Serializable {
    @Id
    @Size(max = 50)
    @Column(name = "id", nullable = false, length = 50)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @Size(max = 100)
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Size(max = 50)
    @Column(name = "model", nullable = false, length = 50)
    private String model;

    @Size(max = 50)
    @Column(name = "code", nullable = false, length = 50)
    private String code;

    @Size(max = 100)
    @Column(name = "category", nullable = false, length = 100)
    private String category;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "type_code", nullable = false)
    private SysDictItem typeCode;

    @Size(max = 50)
    @Column(name = "manufacturer", nullable = false, length = 50)
    private String manufacturer;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "workshop_id", nullable = false)
    private WorkShop workshop;

    @Column(name = "specification", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> specification;

    @Column(name = "install_date", nullable = false)
    private LocalDate installDate;

    @Size(max = 200)
    @Column(name = "qr_code", length = 200)
    private String qrCode;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "status_code", nullable = false)
    private SysDictItem statusCode;

    @Column(name = "last_maintain")
    private LocalDate lastMaintain;

    
    @Column(name = "created_at")
    @CreatedDate
    private LocalDateTime createdAt;

    
    @Column(name = "updated_at")
    @LastModifiedDate
    private LocalDateTime updatedAt;
}