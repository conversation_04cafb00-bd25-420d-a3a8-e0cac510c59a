package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "metric_enum")
public class MetricEnum {
    @Id
    @Size(max = 36)
    @Column(name = "metric_id", nullable = false, length = 36)
    private String metricId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "metric_id", nullable = false)
    private QualityMetric qualityMetric;

    @NotNull
    @Column(name = "allowed_values", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> allowedValues;

    @ColumnDefault("0")
    @Column(name = "multi_select")
    private Boolean multiSelect;

}