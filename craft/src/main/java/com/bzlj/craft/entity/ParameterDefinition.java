package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "parameter_definition")
@EntityListeners(AuditingEntityListener.class)
public class ParameterDefinition implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "param_def_id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String paramDefId;

    @Size(max = 50)
    @Column(name = "param_name", nullable = false, length = 50)
    private String paramName;

    @Size(max = 50)
    @Column(name = "param_code", nullable = false, length = 50)
    private String paramCodee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "param_type")
    private SysDictItem paramType;

    @Size(max = 20)
    @Column(name = "unit", length = 20)
    private String unit;

    @Column(name = "min_value", precision = 12, scale = 3)
    private BigDecimal minValue;

    @Column(name = "max_value", precision = 12, scale = 3)
    private BigDecimal maxValue;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "work_step_id", nullable = false)
    private WorkStep workStep;

    @Column(name = "target_value")
    private String targetValue;

}