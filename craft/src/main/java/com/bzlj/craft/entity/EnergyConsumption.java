package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "energy_consumption")
public class EnergyConsumption implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "energy_id", nullable = false, length = 36)
    private String energyId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "equip_id", nullable = false)
    private Equipment equip;

    @NotNull
    @Column(name = "record_date", nullable = false)
    private LocalDateTime recordDate;

    @Size(max = 255)
    @Column(name = "energy_type")
    private String energyType;

    @NotNull
    @Column(name = "consumption", nullable = false, precision = 10, scale = 2)
    private BigDecimal consumption;

    @Size(max = 20)
    @Column(name = "unit", length = 20)
    private String unit;

    @Column(name = "cost", precision = 10, scale = 2)
    private BigDecimal cost;

}