package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "metric_attach")
public class MetricAttach {
    @Id
    @Size(max = 36)
    @Column(name = "metric_id", nullable = false, length = 36)
    private String metricId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "metric_id", nullable = false)
    private QualityMetric qualityMetric;

    @Size(max = 255)
    @NotNull
    @Column(name = "file_type_code", nullable = false)
    private String fileTypeCode;

    @Size(max = 255)
    @NotNull
    @Column(name = "storage_path", nullable = false)
    private String storagePath;

}