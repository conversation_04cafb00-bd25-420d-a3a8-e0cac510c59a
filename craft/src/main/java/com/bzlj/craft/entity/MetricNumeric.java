package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "metric_numeric")
public class MetricNumeric {
    @Id
    @Size(max = 36)
    @Column(name = "metric_id", nullable = false, length = 36)
    private String metricId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "metric_id", nullable = false)
    private QualityMetric qualityMetric;

    @Size(max = 20)
    @NotNull
    @Column(name = "unit", nullable = false, length = 20)
    private String unit;

    @Column(name = "min_value", precision = 20, scale = 6)
    private BigDecimal minValue;

    @Column(name = "max_value", precision = 20, scale = 6)
    private BigDecimal maxValue;

    @Column(name = "target_value", precision = 20, scale = 6)
    private BigDecimal targetValue;

    @Column(name = "tolerance", precision = 20, scale = 6)
    private BigDecimal tolerance;

    @ColumnDefault("2")
    @Column(name = "decimal_places")
    private Byte decimalPlaces;

}