package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "equipment_maintenance")
public class EquipmentMaintenance implements Serializable {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "equipment_id", nullable = false)
    private Equipment equipment;

    @NotNull
    @Lob
    @Column(name = "maintenance_type", nullable = false)
    private String maintenanceType;

    @NotNull
    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time")
    private LocalDateTime endTime;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "operator_id", nullable = false)
    private Employee operator;

    @Column(name = "cost", precision = 12, scale = 2)
    private BigDecimal cost;

    @Lob
    @Column(name = "technical_report")
    private String technicalReport;

    @Column(name = "component_replaced")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> componentReplaced;

    
    @Column(name = "created_at")
    private LocalDateTime createdAt;

}