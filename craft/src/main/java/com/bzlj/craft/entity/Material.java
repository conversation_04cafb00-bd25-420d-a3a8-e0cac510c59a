package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "material")
@EntityListeners(AuditingEntityListener.class)
public class Material implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "material_id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String materialId;

    @Size(max = 50)
    @Column(name = "steel_grade", nullable = false, length = 50)
    private String steelGrade;

    @Lob
    @Column(name = "material_type", nullable = false)
    private String materialType;

    @Column(name = "weight", nullable = false, precision = 10, scale = 2)
    private BigDecimal weight;

    @Column(name = "create_time")
    @CreatedDate
    private LocalDateTime createTime;

    @Size(max = 20)
    @Column(name = "heat_number", length = 20)
    private String heatNumber;

    @Column(name = "status")
    private String status;

    @Size(max = 255)
    @Column(name = "material_code", nullable = false)
    private String materialCode;

    @Size(max = 255)
    @Column(name = "material_name", nullable = false)
    private String materialName;

//    @Size(max = 255)
//    @Column(name = "graph_no")
//    private String graphNo; // 规格属性

    @Size(max = 255)
    @Column(name = "specification")
    private String specification;

    @Size(max = 255)
    @Column(name = "material_batch_no")
    private String materialBatchNo;

//    @Size(max = 255)
//    @Column(name = "compositions")
//    private String compositions;

    /**
     * 牌号
     */
    private String brand;

    /**
     * 锭型
     */
    @Column(name = "ingot_type")
    private String ingotType;  //规格属性

    /**
     * 锭号
     */
    @Column(name = "ingot_number")
    private String ingotNumber; //规格属性

    /**
     * 供应商
     */
    @Column(name = "supplier")
    private String supplier;

    @OneToMany(mappedBy = "material", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<MaterialAttr> materialAttrs;

}