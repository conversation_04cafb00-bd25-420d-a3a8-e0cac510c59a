package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "operation_attachment")
public class OperationAttachment implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "attach_id", nullable = false, length = 36)
    private String attachId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "log_id", nullable = false)
    private OperationLog log;

    @NotNull
    @Lob
    @Column(name = "file_type", nullable = false)
    private String fileType;

    @Size(max = 500)
    @NotNull
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    @Size(max = 500)
    @Column(name = "thumbnail_path", length = 500)
    private String thumbnailPath;

    @Column(name = "upload_time")
    private LocalDateTime uploadTime;

}