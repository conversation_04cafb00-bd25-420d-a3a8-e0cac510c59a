package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "process_text_parameter")
public class ProcessTextParameter {
    @Id
    @Size(max = 36)
    @Column(name = "param_id", nullable = false, length = 36)
    private String paramId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "param_id", nullable = false)
    private ProcessParameter processParameter;

    @NotNull
    @Lob
    @Column(name = "actual_value")
    private String actualValue;

}