package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "craft_process")
@EntityListeners(AuditingEntityListener.class)
public class CraftProcess {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "craft_id", nullable = false)
    private CraftMain craft;

    @Size(max = 10)
    @Column(name = "process_code", length = 255)
    private String processCode;

    @Column(name = "process_name")
    private String processName;

    @Column(name = "process_order", nullable = false)
    private Integer processOrder;

    @Column(name = "input_output_spec", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> inputOutputSpec;

    @Column(name = "equipment_constraint")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> equipmentConstraint;

    @Size(max = 32)
    @Column(name = "version_hash", length = 32)
    private String versionHash;

    @Size(max = 50)
    @Column(name = "created_by", nullable = false, length = 50)
    @CreatedBy
    private String createdBy;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "last_modified")
    @LastModifiedDate
    private LocalDateTime lastModified;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "process_type")
    @JsonIgnore
    private SysDictItem processType;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "plant_code", nullable = false)
    private Plant plant;

    @OneToMany(mappedBy = "process", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private Set<CraftProcessExtend> craftProcessExtends;
}