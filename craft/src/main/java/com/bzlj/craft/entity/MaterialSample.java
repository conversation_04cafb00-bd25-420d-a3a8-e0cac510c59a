package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "material_sample")
public class MaterialSample {
    @Id
    @Size(max = 36)
    @Column(name = "sample_id", nullable = false, length = 36)
    private String sampleId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "material_id", nullable = false)
    private Material material;

    @Size(max = 255)
    @NotNull
    @Column(name = "sample_code", nullable = false)
    private String sampleCode;
}