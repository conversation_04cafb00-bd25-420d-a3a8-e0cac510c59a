package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "team")
public class Team implements Serializable {
    @Id
    @Size(max = 20)
    @Column(name = "team_id", nullable = false, length = 20)
    private String teamId;

    @Size(max = 50)
    @NotNull
    @Column(name = "team_name", nullable = false, length = 50)
    private String teamName;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "workshop_id", nullable = false)
    private WorkShop workshop;

    @NotNull
    @Lob
    @Column(name = "team_type", nullable = false)
    private String teamType;

    @Lob
    @Column(name = "description")
    private String description;

    
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shift_code")
    private SysDictItem shiftCode;
}