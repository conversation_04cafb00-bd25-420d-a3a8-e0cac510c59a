package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "equipment_parameter")
public class EquipmentParameter implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "record_id", nullable = false, length = 36)
    private String recordId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "equip_id", nullable = false)
    private Equipment equip;

    @Size(max = 50)
    @NotNull
    @Column(name = "param_name", nullable = false, length = 50)
    private String paramName;

    @NotNull
    @Column(name = "param_value", nullable = false, precision = 12, scale = 3)
    private BigDecimal paramValue;

    @NotNull
    @Column(name = "collect_time", nullable = false)
    private LocalDateTime collectTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "task_id")
    private ProductionTask task;

}