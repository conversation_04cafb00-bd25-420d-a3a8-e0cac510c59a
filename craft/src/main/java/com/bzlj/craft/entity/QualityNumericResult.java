package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "quality_numeric_result")
public class QualityNumericResult {
    @Id
    @Column(name = "result_id", nullable = false)
    private Long id;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "result_id", nullable = false)
    private QualityResult qualityResult;

    @NotNull
    @Column(name = "actual_value", nullable = false, precision = 20, scale = 6)
    private BigDecimal actualValue;

}