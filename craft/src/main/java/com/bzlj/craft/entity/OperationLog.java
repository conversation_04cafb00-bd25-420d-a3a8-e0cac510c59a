package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "operation_log")
@EntityListeners(AuditingEntityListener.class)
public class OperationLog implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "log_id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String logId;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "task_id", nullable = false)
    private ProductionTask task;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workstep_id")
    private WorkStep workstep;

    @Size(max = 20)
    @Column(name = "operator_id", nullable = false, length = 20)
    @CreatedBy
    private String operatorId;

    @Lob
    @Column(name = "operation_type", nullable = false)
    private String operationType;

    @Column(name = "operation_time", nullable = false)
    private LocalDateTime operationTime;

    @Column(name = "description", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> description;

}