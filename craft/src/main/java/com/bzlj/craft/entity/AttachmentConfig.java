package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "attachment_config")
public class AttachmentConfig {
    @Id
    @Size(max = 32)
    @Column(name = "file_id", nullable = false, length = 32)
    private String fileId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "param_id", nullable = false)
    private StepParameter param;

    @Size(max = 255)
    @NotNull
    @Column(name = "original_name", nullable = false)
    private String originalName;

    @NotNull
    @ColumnDefault("'OSS'")
    @Lob
    @Column(name = "storage_engine", nullable = false)
    private String storageEngine;

    @Size(max = 500)
    @NotNull
    @Column(name = "storage_path", nullable = false, length = 500)
    private String storagePath;

    @Size(max = 500)
    @Column(name = "thumbnail_path", length = 500)
    private String thumbnailPath;

    @NotNull
    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    @Size(max = 32)
    @NotNull
    @Column(name = "md5_hash", nullable = false, length = 32)
    private String md5Hash;

    @Size(max = 50)
    @NotNull
    @Column(name = "upload_by", nullable = false, length = 50)
    private String uploadBy;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "upload_time")
    private LocalDateTime uploadTime;

    @Column(name = "metadata")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> metadata;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "file_type")
    private SysDictItem fileType;

    @Size(max = 20)
    @Column(name = "storage_engine_new", length = 20)
    private String storageEngineNew;

}