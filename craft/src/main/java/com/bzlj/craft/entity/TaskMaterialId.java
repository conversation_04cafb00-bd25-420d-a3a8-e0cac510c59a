package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class TaskMaterialId implements Serializable {
    private static final long serialVersionUID = -1091461228512320505L;
    @Size(max = 36)
    @NotNull
    @Column(name = "task_id", nullable = false, length = 36)
    private String taskId;

    @Size(max = 36)
    @NotNull
    @Column(name = "material_id", nullable = false, length = 36)
    private String materialId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        TaskMaterialId entity = (TaskMaterialId) o;
        return Objects.equals(this.materialId, entity.materialId) &&
                Objects.equals(this.taskId, entity.taskId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(materialId, taskId);
    }

}