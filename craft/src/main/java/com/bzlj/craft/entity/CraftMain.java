package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "craft_main")
public class CraftMain {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    private String id;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "parent_id")
    private CraftMain parent;

    @Size(max = 50)
    @NotNull
    @Column(name = "craft_code", nullable = false, length = 50)
    private String craftCode;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "version", nullable = false)
    private Integer version;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "is_template", nullable = false)
    private Boolean isTemplate = false;

    @Size(max = 255)
    @NotNull
    @Column(name = "craft_name", nullable = false)
    private String craftName;

    @Size(max = 50)
    @NotNull
    @Column(name = "created_by", nullable = false, length = 50)
    private String createdBy;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Size(max = 50)
    @Column(name = "modified_by", length = 50)
    private String modifiedBy;

    @Column(name = "modified_time")
    private LocalDateTime modifiedTime;

    @Lob
    @Column(name = "change_reason")
    private String changeReason;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "material_spec_id")
    private MaterialSpec materialSpec;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "current_status")
    private SysDictItem currentStatus;

}