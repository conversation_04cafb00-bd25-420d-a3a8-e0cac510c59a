package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "material_attr")
public class MaterialAttr implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "material_attr_id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String materialAttrId;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "material_id", nullable = false)
    private Material material;

    @Size(max = 255)
    @Column(name = "preorderDeptCode")
    private String preorderDeptCode;

    @Size(max = 255)
    @Column(name = "toDeptCode")
    private String toDeptCode;

    @Column(name = "attr")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> attr;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "attr_type", nullable = false)
    private SysDictItem attrType;
    
}