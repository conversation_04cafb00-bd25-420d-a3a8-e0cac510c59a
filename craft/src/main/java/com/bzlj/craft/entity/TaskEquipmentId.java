package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class TaskEquipmentId implements Serializable {
    private static final long serialVersionUID = -5150271647762055640L;
    @Size(max = 36)
    @NotNull
    @Column(name = "task_id", nullable = false, length = 36)
    private String taskId;

    @Size(max = 36)
    @NotNull
    @Column(name = "equip_id", nullable = false, length = 36)
    private String equipId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        TaskEquipmentId entity = (TaskEquipmentId) o;
        return Objects.equals(this.equipId, entity.equipId) &&
                Objects.equals(this.taskId, entity.taskId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(equipId, taskId);
    }

}