package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "plant")
@EntityListeners(AuditingEntityListener.class)
public class Plant implements Serializable {
    @Id
    @Column(name = "plant_code", nullable = false, length = 10)
    private String plantCode;

    @Size(max = 100)
    @NotNull
    @Column(name = "plant_name", nullable = false, length = 100)
    private String plantName;

    @Column(name = "established_date", nullable = false)
    private LocalDate establishedDate;

    @Size(max = 36)
    @Column(name = "manager_emp_id", length = 36)
    private String managerEmpId;

    
    @Column(name = "created_at")
    @CreatedDate
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    @LastModifiedDate
    private LocalDateTime updatedAt;

/*
 TODO [Reverse Engineering] create field to map the 'geo_location' column
 Available actions: Define target Java type | Uncomment as is | Remove column mapping
    @Column(name = "geo_location", columnDefinition = "polygon not null")
    private Object geoLocation;
*/
}