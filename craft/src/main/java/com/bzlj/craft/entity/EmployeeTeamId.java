package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class EmployeeTeamId implements Serializable {
    private static final long serialVersionUID = -7293603982190841806L;
    @Size(max = 36)
    @NotNull
    @Column(name = "employee_id", nullable = false, length = 36)
    private String employeeId;

    @Size(max = 20)
    @NotNull
    @Column(name = "team_id", nullable = false, length = 20)
    private String teamId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        EmployeeTeamId entity = (EmployeeTeamId) o;
        return Objects.equals(this.teamId, entity.teamId) &&
                Objects.equals(this.employeeId, entity.employeeId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(teamId, employeeId);
    }

}