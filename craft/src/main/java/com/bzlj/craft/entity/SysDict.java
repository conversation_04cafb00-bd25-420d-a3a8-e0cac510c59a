package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "sys_dict")
public class SysDict implements Serializable {
    @Id
    @Size(max = 50)
    @Column(name = "dict_code", nullable = false, length = 50)
    private String dictCode;

    @Size(max = 100)
    @NotNull
    @Column(name = "dict_name", nullable = false, length = 100)
    private String dictName;

    @Size(max = 500)
    @Column(name = "description", length = 500)
    private String description;

}