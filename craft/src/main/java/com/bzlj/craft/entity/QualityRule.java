package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "quality_rule")
public class QualityRule {
    @Id
    @Size(max = 36)
    @Column(name = "rule_id", nullable = false, length = 36)
    private String ruleId;

    @Size(max = 50)
    @NotNull
    @Column(name = "rule_code", nullable = false, length = 50)
    private String ruleCode;

    @Size(max = 255)
    @NotNull
    @Column(name = "rule_name", nullable = false)
    private String ruleName;

    @Lob
    @Column(name = "description")
    private String description;

    @ColumnDefault("1")
    @Column(name = "version")
    private Integer version;

    @Size(max = 50)
    @NotNull
    @Column(name = "created_by", nullable = false, length = 50)
    private String createdBy;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Size(max = 50)
    @Column(name = "modified_by", length = 50)
    private String modifiedBy;

    @Column(name = "modified_time")
    private LocalDateTime modifiedTime;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rule_type")
    private SysDictItem ruleType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "applicable_level")
    private SysDictItem applicableLevel;

}