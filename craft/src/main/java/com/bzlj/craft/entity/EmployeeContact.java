package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "employee_contact")
public class EmployeeContact implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "employee_id", nullable = false, length = 36)
    private String employeeId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "employee_id", nullable = false)
    private com.bzlj.craft.entity.Employee employee;

    @Size(max = 15)
    @NotNull
    @Column(name = "mobile", nullable = false, length = 15)
    private String mobile;

    @Size(max = 100)
    @Column(name = "work_email", length = 100)
    private String workEmail;

    @NotNull
    @Column(name = "emergency_contact", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> emergencyContact;

    
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

}