package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "env_monitor_data")
public class EnvMonitorData implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "data_id", nullable = false, length = 36)
    private String dataId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "point_id", nullable = false)
    private EnvMonitorPoint point;

    @Column(name = "create_time")
    private LocalDateTime createTime;

    @Size(max = 255)
    @Column(name = "indicators")
    private String indicators;

    @Size(max = 255)
    @Column(name = "value")
    private String value;

    @Size(max = 255)
    @Column(name = "indicator_nuit")
    private String indicatorNuit;

}