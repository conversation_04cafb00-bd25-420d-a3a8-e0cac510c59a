package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "quality_inspection")
public class QualityInspection implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "inspection_id", nullable = false, length = 36)
    private String inspectionId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "material_id", nullable = false)
    private Material material;


    @Size(max = 20)
    @NotNull
    @Column(name = "result", nullable = false, length = 20)
    private String result;

    @Size(max = 20)
    @Column(name = "inspector", length = 20)
    private String inspector;

    @Column(name = "inspect_time")
    private LocalDateTime inspectTime;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "task_id", nullable = false)
    private ProductionTask task;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "quality_rule_id", nullable = false)
    private QualityRule qualityRule;


    @Column(name = "sample_quantity")
    private Integer sampleQuantity;

    @Size(max = 255)
    @Column(name = "inspection_result_no")
    private String inspectionResultNo;


    @Size(max = 255)
    @Column(name = "judgmentResult")
    private String judgmentResult;

    @Size(max = 255)
    @Column(name = "conclusion")
    private String conclusion;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sample_id", nullable = false)
    private MaterialSample materialSample;

}