package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class TemplateInstanceId implements Serializable {
    private static final long serialVersionUID = 2291055087965477470L;
    @Size(max = 36)
    @NotNull
    @Column(name = "template_id", nullable = false, length = 36)
    private String templateId;

    @Size(max = 36)
    @NotNull
    @Column(name = "instance_id", nullable = false, length = 36)
    private String instanceId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        TemplateInstanceId entity = (TemplateInstanceId) o;
        return Objects.equals(this.instanceId, entity.instanceId) &&
                Objects.equals(this.templateId, entity.templateId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(instanceId, templateId);
    }

}