package com.bzlj.craft.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "process_parameter")
public class ProcessParameter implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "param_id", nullable = false, length = 36)
    private String paramId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "param_def_id", nullable = false)
    private ParameterDefinition paramDef;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "task_id", nullable = false)
    private ProductionTask task;

    @NotNull
    @Column(name = "collect_time", nullable = false)
    private LocalDateTime collectTime;

    @Size(max = 36)
    @Column(name = "equipment_id", length = 36)
    private String equipmentId;

    @Size(max = 20)
    @Column(name = "operator_id", length = 20)
    private String operatorId;

    @OneToOne(mappedBy = "processParameter", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private ProcessNumParameter processNumParameter;

    @OneToOne(mappedBy = "processParameter", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private ProcessDateParameter processDateParameter;

    @OneToOne(mappedBy = "processParameter", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private ProcessTextParameter processTextParameter;

}