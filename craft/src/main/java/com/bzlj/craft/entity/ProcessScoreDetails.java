package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "process_score_details")
public class ProcessScoreDetails {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    private String id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "task_id", nullable = false)
    private ProductionTask task;

    @Size(max = 255)
    @NotNull
    @Column(name = "process_step_name", nullable = false)
    private String processStepName;

    @Size(max = 255)
    @NotNull
    @Column(name = "process_param", nullable = false)
    private String processParam;

    @Size(max = 255)
    @Column(name = "standard")
    private String standard;

    @Size(max = 255)
    @Column(name = "actual")
    private String actual;

    @Size(max = 255)
    @Column(name = "deviation_degree")
    private String deviationDegree;

    @Column(name = "deduct", precision = 10, scale = 2)
    private BigDecimal deduct;

    @Column(name = "version")
    private Integer version;

    @Column(name = "process_score_id")
    private String processScoreId;

}