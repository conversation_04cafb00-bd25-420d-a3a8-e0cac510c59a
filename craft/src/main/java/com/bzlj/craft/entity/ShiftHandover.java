package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "shift_handover")
public class ShiftHandover implements Serializable {
    @Id
    @Size(max = 40)
    @Column(name = "handover_id", nullable = false, length = 40)
    private String handoverId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "outgoing_team_id", nullable = false)
    private Team outgoingTeam;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "incoming_team_id", nullable = false)
    private Team incomingTeam;

    @NotNull
    @Column(name = "equipment_status", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> equipmentStatus;

    @Column(name = "material_inventory")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> materialInventory;

    @Lob
    @Column(name = "quality_issues")
    private String qualityIssues;

    @Size(max = 36)
    @NotNull
    @Column(name = "sign_off_by_outgoing", nullable = false, length = 36)
    private String signOffByOutgoing;

    @Size(max = 36)
    @NotNull
    @Column(name = "sign_off_by_incoming", nullable = false, length = 36)
    private String signOffByIncoming;

    @NotNull
    @Column(name = "handover_time", nullable = false)
    private LocalDateTime handoverTime;

    
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "task_id", nullable = false)
    private ProductionTask task;
}