package com.bzlj.craft.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "env_monitor_points")
public class EnvMonitorPoint implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "point_id", nullable = false, length = 36)
    private String pointId;

    @Size(max = 255)
    @NotNull
    @Column(name = "point_name", nullable = false)
    private String pointName;

    @Size(max = 255)
    @Column(name = "point_type")
    private String pointType;

    @Size(max = 255)
    @Column(name = "location")
    private String location;

    @Column(name = "status")
    private Byte status;

    @Column(name = "create_time")
    private LocalDateTime createTime;

}