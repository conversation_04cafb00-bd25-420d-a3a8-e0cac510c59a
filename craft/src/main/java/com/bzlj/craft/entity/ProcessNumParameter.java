package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "process_num_parameter")
public class ProcessNumParameter {
    @Id
    @Size(max = 36)
    @Column(name = "param_id", nullable = false, length = 36)
    private String paramId;

    @MapsId
    @OneToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "param_id", nullable = false)
    private ProcessParameter processParameter;

    @NotNull
    @Column(name = "actual_value",  precision = 14, scale = 3)
    private BigDecimal actualValue;

}