package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "rule_binding")
public class RuleBinding {
    @Id
    @Size(max = 36)
    @Column(name = "binding_id", nullable = false, length = 36)
    private String bindingId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "rule_id", nullable = false)
    private QualityRule rule;

    @Size(max = 36)
    @NotNull
    @Column(name = "ref_id", nullable = false, length = 36)
    private String refId;

    @Column(name = "effective_start")
    private LocalDateTime effectiveStart;

    @Column(name = "effective_end")
    private LocalDateTime effectiveEnd;

    @ColumnDefault("1")
    @Column(name = "priority")
    private Byte priority;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ref_type")
    private SysDictItem refType;

}