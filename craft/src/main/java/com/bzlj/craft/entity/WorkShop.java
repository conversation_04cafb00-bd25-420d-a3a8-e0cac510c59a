package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "work_shop")
public class WorkShop implements Serializable {
    @Id
    @Size(max = 20)
    @Column(name = "work_shop_id", nullable = false, length = 20)
    private String workShopId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "plant_code", nullable = false)
    private Plant plantCode;

    @Size(max = 50)
    @NotNull
    @Column(name = "workshop_name", nullable = false, length = 50)
    private String workshopName;

    @NotNull
    @Lob
    @Column(name = "workshop_type", nullable = false)
    private String workshopType;

    @Column(name = "floor_area", precision = 12, scale = 2)
    private BigDecimal floorArea;

    
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

}