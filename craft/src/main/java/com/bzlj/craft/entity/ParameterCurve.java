package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "parameter_curve")
public class ParameterCurve {
    @Id
    @Column(name = "curve_id", nullable = false)
    private Long id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "param_id", nullable = false)
    private StepParameter param;

    @NotNull
    @Column(name = "sequence", nullable = false)
    private Integer sequence;

    @NotNull
    @Column(name = "time_offset", nullable = false, precision = 10, scale = 2)
    private BigDecimal timeOffset;

    @NotNull
    @Column(name = "target_value", nullable = false, precision = 20, scale = 6)
    private BigDecimal targetValue;

    @NotNull
    @Column(name = "upper_limit", nullable = false, precision = 20, scale = 6)
    private BigDecimal upperLimit;

    @NotNull
    @Column(name = "lower_limit", nullable = false, precision = 20, scale = 6)
    private BigDecimal lowerLimit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "interpolation_method")
    private SysDictItem interpolationMethod;

}