package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "change_audit")
public class ChangeAudit {
    @Id
    @Column(name = "audit_id", nullable = false)
    private Long id;

    @Size(max = 36)
    @NotNull
    @Column(name = "ref_id", nullable = false, length = 36)
    private String refId;

    @Column(name = "old_values")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> oldValues;

    @Column(name = "new_values")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> newValues;

    @Size(max = 50)
    @NotNull
    @Column(name = "changed_by", nullable = false, length = 50)
    private String changedBy;

    @Lob
    @Column(name = "change_reason")
    private String changeReason;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "change_time")
    private LocalDateTime changeTime;

    @NotNull
    @Column(name = "craft_version", nullable = false)
    private Integer craftVersion;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ref_type")
    private SysDictItem refType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "change_type")
    private SysDictItem changeType;

}