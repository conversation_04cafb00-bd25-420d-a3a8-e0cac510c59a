package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "craft_history")
public class CraftHistory {
    @Id
    @Column(name = "history_id", nullable = false)
    private Long id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "craft_id", nullable = false)
    private CraftMain craft;

    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    @NotNull
    @Column(name = "snapshot_data", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> snapshotData;

    @Size(max = 50)
    @NotNull
    @Column(name = "modified_by", nullable = false, length = 50)
    private String modifiedBy;

    @NotNull
    @Column(name = "modified_time", nullable = false)
    private LocalDateTime modifiedTime;

    @NotNull
    @Lob
    @Column(name = "change_reason", nullable = false)
    private String changeReason;

}