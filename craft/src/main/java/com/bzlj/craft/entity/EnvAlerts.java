package com.bzlj.craft.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "env_alerts")
public class EnvAlerts implements Serializable {
    @Id
    @Size(max = 36)
    @Column(name = "alert_id", nullable = false, length = 36)
    private String alertId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "inspection_id", nullable = false)
    private QualityInspection inspection;

    @Column(name = "trigger_time")
    private LocalDateTime triggerTime;

    @Size(max = 255)
    @Column(name = "alert_type")
    private String alertType;

    @Size(max = 255)
    @Column(name = "current_value")
    private String currentValue;

    @Size(max = 255)
    @Column(name = "threshold_value")
    private String thresholdValue;

    @Size(max = 255)
    @Column(name = "alert_level")
    private String alertLevel;

}