package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "production_task_extend")
public class ProductionTaskExtend {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "task_id", nullable = false)
    private ProductionTask task;

    @Column(name = "extend_attr")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> extendAttr;

}