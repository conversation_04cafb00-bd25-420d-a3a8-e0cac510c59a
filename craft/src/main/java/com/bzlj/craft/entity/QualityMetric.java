package com.bzlj.craft.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "quality_metric")
public class QualityMetric {
    @Id
    @Size(max = 36)
    @Column(name = "metric_id", nullable = false, length = 36)
    private String metricId;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "rule_id", nullable = false)
    private QualityRule rule;

    @Size(max = 255)
    @NotNull
    @Column(name = "metric_name", nullable = false)
    private String metricName;

    @NotNull
    @Column(name = "config", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> config;

    @ColumnDefault("1")
    @Column(name = "is_mandatory")
    private Boolean isMandatory;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "metric_type")
    private SysDictItem metricType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "evaluation_logic")
    private SysDictItem evaluationLogic;

    @OneToOne(mappedBy = "qualityMetric", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private MetricNumeric metricNumeric;

    @OneToOne(mappedBy = "qualityMetric", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private MetricEnum metricEnum;

    @OneToOne(mappedBy = "qualityMetric", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private MetricAttach metricAttach;

}