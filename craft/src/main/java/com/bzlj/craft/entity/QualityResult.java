package com.bzlj.craft.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "quality_result")
public class QualityResult {
    @Id
    @Column(name = "result_id", nullable = false)
    private Long id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "binding_id", nullable = false)
    private RuleBinding binding;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "metric_id", nullable = false)
    private QualityMetric metric;

    @NotNull
    @Column(name = "is_passed", nullable = false)
    private Boolean isPassed = false;

    @Size(max = 50)
    @NotNull
    @Column(name = "evaluated_by", nullable = false, length = 50)
    private String evaluatedBy;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "evaluated_time")
    private LocalDateTime evaluatedTime;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "inspection_id", nullable = false)
    private QualityInspection inspection;

    @Lob
    @Column(name = "remarks")
    private String remarks;

    @OneToOne(mappedBy = "qualityResult", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private QualityNumericResult qualityNumericResult;

    @OneToOne(mappedBy = "qualityResult", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private QualityEnumResult qualityEnumResult;

    @OneToOne(mappedBy = "qualityResult", cascade = {CascadeType.ALL},orphanRemoval = true,fetch = FetchType.LAZY)
    @JsonIgnore
    @ToString.Exclude
    private QualityAttachResult qualityAttachResult;

}