package com.bzlj.craft.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 虚拟线程配置类
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Configuration
public class VirtualThreadConfig {

    /**
     * 创建虚拟线程执行器
     * 用于并行查询任务相关数据
     */
    @Bean("virtualThreadExecutor")
    public Executor virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
}