package com.bzlj.craft.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步事件处理配置
 * <p>
 * 配置异步事件处理的线程池，用于处理任务状态变更等事件。
 * 通过异步处理可以避免事件处理逻辑阻塞主业务流程。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncEventConfig {

    /**
     * 配置事件处理专用的线程池
     * <p>
     * 为事件处理创建专门的线程池，避免与其他异步任务竞争资源。
     * 线程池参数可以根据实际业务需求进行调整。
     * </p>
     *
     * @return 事件处理线程池执行器
     */
    @Bean("eventTaskExecutor")
    public Executor eventTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：保持活跃的最小线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数：线程池允许的最大线程数
        executor.setMaxPoolSize(8);
        
        // 队列容量：当核心线程都在工作时，新任务会放入队列等待
        executor.setQueueCapacity(100);
        
        // 线程名称前缀：便于日志追踪和问题排查
        executor.setThreadNamePrefix("event-task-");
        
        // 线程空闲时间：超过核心线程数的线程在空闲指定时间后会被回收
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：当线程池和队列都满时的处理策略
        // CallerRunsPolicy：由调用线程执行任务，这样可以减缓新任务的提交速度
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：应用关闭时等待任务完成的最大时间
        executor.setAwaitTerminationSeconds(30);
        
        // 初始化线程池
        executor.initialize();
        
        log.info("事件处理线程池初始化完成 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
