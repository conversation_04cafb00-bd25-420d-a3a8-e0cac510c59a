package com.bzlj.craft.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 事务管理配置类
 * <p>
 * 该配置类用于启用Spring的事务管理功能，并提供事务相关的配置。
 * 主要功能包括：
 * 1. 启用基于注解的事务管理
 * 2. 配置事务管理器
 * 3. 设置事务传播行为
 * 4. 处理事务回滚策略
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Configuration
@EnableTransactionManagement(
    // 启用基于代理的事务管理
    proxyTargetClass = true,
    // 设置事务管理器的顺序，确保在其他AOP之前执行
    order = 1
)
public class TransactionConfig {
    
    /**
     * 事务配置说明：
     * 
     * 1. proxyTargetClass = true
     *    - 强制使用CGLIB代理而不是JDK动态代理
     *    - 确保对类方法的事务注解生效
     *    - 解决某些情况下事务不生效的问题
     * 
     * 2. order = 1
     *    - 设置事务管理器的执行顺序
     *    - 确保事务在其他切面之前执行
     *    - 避免事务边界问题
     * 
     * 3. 事务传播行为建议：
     *    - REQUIRED: 默认传播行为，适用于大多数业务方法
     *    - REQUIRES_NEW: 需要独立事务时使用，如日志记录、消息发送
     *    - SUPPORTS: 支持当前事务，但不强制要求事务
     *    - NOT_SUPPORTED: 以非事务方式执行，暂停当前事务
     * 
     * 4. 回滚策略建议：
     *    - rollbackFor = Exception.class: 所有异常都回滚
     *    - rollbackFor = RuntimeException.class: 只有运行时异常回滚（默认）
     *    - noRollbackFor: 指定不回滚的异常类型
     * 
     * 5. 事务超时设置：
     *    - timeout: 设置事务超时时间（秒）
     *    - 防止长时间运行的事务占用资源
     * 
     * 6. 只读事务：
     *    - readOnly = true: 标记为只读事务
     *    - 提高查询性能，防止意外修改
     */
}
