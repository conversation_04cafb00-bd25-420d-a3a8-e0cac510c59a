package com.bzlj.craft.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 测控方法枚举
 * <AUTHOR>
 * @description:
 * @date 2025-03-18 16:53
 */
@AllArgsConstructor
@Getter
public enum PointMethodType {
    batch_point("batch_point", "批量查询数据点历史数据"),
    single_point("single_point", "查询数据点历史数据"),
    batch_concurrent_point("batch_concurrent_point", "批量并发查询点位历史数据"),
    first_point_in_range_date("first_point_in_range_date", "查询时间范围内的第一个值"),
    latest_point_in_range_date("latest_point_in_range_date", "查询时间范围内的最后一个值"),
    max_point_in_range_date("max_point_in_range_date", "查询时间范围内的最大值"),

    min_point_in_range_date("min_point_in_range_date", "查询时间范围内的最小值"),
    mean_point_in_range_date("mean_point_in_range_date", "查询时间范围内的平均值"),
    sum_point_in_range_date("sum_point_in_range_date", "查询时间范围内的求和"),
    end_subtract_start_point("end_subtract_start_point", "时间范围内结束值减去开始值"),
    spread_point_in_range_date("spread_point_in_range_date", "查询时间范围内的最大值与最小值的差值"),

    upper_lower_point_in_range_date("upper_lower_point_in_range_date", "查询时间范围内满足上下限的值"),

    status_statistics_boolean("status_statistics_boolean", "用于统计布尔值状态时长"),
    end_subtract_start_point_2("end_subtract_start_point_2", "时间范围内结束值减去开始值,翻表可用"),
    calculator_point_in_range_date("calculator_point_in_range_date", "查询时间范围内简单计算值"),
    ;
    private final String code;
    private final String msg;
}
