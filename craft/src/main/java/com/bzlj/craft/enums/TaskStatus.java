package com.bzlj.craft.enums;

import com.bzlj.craft.entity.TaskMaterialId;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.hibernate.Hibernate;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-18 16:53
 */
@AllArgsConstructor
@Getter
public enum TaskStatus {
    not_start("not_start", "未开始"),
    in_progress("in_progress", "进行中"),
    completed("completed", "已完成"),;

    private final String code;
    private final String msg;

}
