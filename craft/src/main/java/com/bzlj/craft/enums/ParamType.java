package com.bzlj.craft.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-18 16:53
 */
@AllArgsConstructor
@Getter
public enum ParamType {
    NUMBER("NUMBER", "数值型"),
    ATTACHMENT("ATTACHMENT", "附件型"),
    TEXT("TEXT", "文本型"),
    DATE("DATE", "日期型"),

    CURVE("CURVE", "曲线");
    private final String code;
    private final String msg;
}
