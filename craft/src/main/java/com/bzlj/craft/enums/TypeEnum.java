package com.bzlj.craft.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TypeEnum {
    string(1, "string"),
    number(2, "number"),
    enums(3, "enum"),
    datetime(4, "datetime"),
    bool(5, "bool"),
    obj(6, "obj"),
    ;

    /**
     * code编码
     */
    final String code;
    /**
     * 获取名称
     */
    final String value;

    TypeEnum(int code, String value) {
        this.code = String.format("%04d", code);
        this.value = value;
    }

}