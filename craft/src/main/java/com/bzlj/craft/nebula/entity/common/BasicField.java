package com.bzlj.craft.nebula.entity.common;

import com.bzlj.craft.nebula.annotation.NebulaField;
import com.bzlj.craft.nebula.enums.NebulaFieldEnum;
import com.bzlj.craft.nebula.enums.NebulaFieldTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 基础类
 *
 * <AUTHOR>
 * @date 2025/3/20 17:59
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasicField {
    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.DATETIME,
            comment = "创建时间", defaultValue = "DATETIME()")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createItemTime = LocalDateTime.now();

    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.DATETIME,
            comment = "更新时间", defaultValue = "DATETIME()")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateItemTime;

    public void create() {
        this.createItemTime = LocalDateTime.now();
    }

    public void update() {
        this.updateItemTime = LocalDateTime.now();
    }
}
