package com.bzlj.craft.nebula.init;

import com.bzlj.craft.nebula.annotation.NebulaField;
import com.bzlj.craft.nebula.annotation.NebulaIndex;
import com.bzlj.craft.nebula.cache.ReflectionCache;
import com.bzlj.craft.nebula.config.NebulaConfig;
import com.bzlj.craft.nebula.enums.NebulaFieldEnum;
import com.bzlj.craft.nebula.util.NebulaUtil;
import com.bzlj.craft.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
@Component
public class NebulaInitializer implements SmartLifecycle, InitializingBean {
    private boolean running = false;
    private final NebulaConfig config;

    @Override
    public void start() {
        running = true;
        // 执行关键初始化操作
        try {
            log.info("Nebula初始化开始");
            // 初始化逻辑
            initSpace();
            initTags();
            initEdges();
            initIndex();
            log.info("Nebula初始化结束");
        } catch (Exception e) {
            throw new RuntimeException("Nebula初始化失败，程序无法启动", e);
        }
    }

    /**
     * 初始化索引
     *
     * <AUTHOR>
     * @date 2025/3/25 14:22
     */
    private void initIndex() {
        Map<String, Class<?>> allTagBean = ReflectionCache.getAllTagBean();
        for (Class<?> clazz : allTagBean.values()) {
            String clazzName = clazz.getName();
            String tagName = ReflectionCache.getFromTagCache(clazzName);
            createIndex(clazz, tagName);
        }
    }

    /**
     * 初始化索引
     *
     * <AUTHOR>
     * @date 2025/3/25 14:22
     */
    private void createIndex(Class<?> clazz, String tagName) {
        Field[] allField = getAllField(clazz);
        for (Field field : allField) {
            if (field.isAnnotationPresent(NebulaIndex.class) && field.isAnnotationPresent(NebulaField.class)) {
                NebulaIndex nebulaIndex = field.getAnnotation(NebulaIndex.class);
                NebulaField nebulaField = field.getAnnotation(NebulaField.class);
                String fieldName = nebulaField.name().isEmpty() ? field.getName() : nebulaField.name();
                NebulaUtil.execute("CREATE TAG INDEX IF NOT EXISTS " + tagName + "_index_" + fieldName + " on " + tagName + "(" + fieldName + "(" + nebulaIndex.length() + "))");
            }
        }
    }

    /**
     * 初始化空间
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void initSpace() {
        List<Map<String, Object>> spaces = NebulaUtil.execute("show spaces");
        if (!spaces.isEmpty()) {
            List<String> spaceValues = toStringList(spaces);
            log.info("[Nebula]======> Space has {}.", JsonUtils.toJson(spaceValues));
            if (spaceValues.contains(config.getSpace().getSpaceName())) {
                return;
            }
        }
        String createSpaceQuery = String.format("create space if not exists %s (vid_type=%s) comment = '%s'",
                config.getSpace().getSpaceName(), config.getSpace().getVidType(), config.getSpace().getComment());
        NebulaUtil.execute(createSpaceQuery);
        // 检查空间是否创建成功
        log.info("[Nebula]======> Waiting for Space {} to be created...", config.getSpace().getSpaceName());
        try {
            Thread.sleep(1000);
            spaces = NebulaUtil.execute("show spaces");
            List<String> spaceValues = toStringList(spaces);
            if (spaceValues.contains(config.getSpace().getSpaceName())) {
                changeUseSpace(0);
                log.info("[Nebula]======> Space {} created successfully.", config.getSpace().getSpaceName());
            }

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 切换空间
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void changeUseSpace(int retryCount) {
        try {
            NebulaUtil.execute("use " + config.getSpace().getSpaceName());
        } catch (Exception e) {
            if (e.getMessage().contains("SpaceNotFound: SpaceName `" + config.getSpace().getSpaceName() + "`")) {
                if (retryCount < 10) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ex) {
                        throw new RuntimeException(ex);
                    }
                    changeUseSpace(retryCount + 1);
                } else {
                    throw new RuntimeException(e);
                }
            } else {
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * 初始化标签
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void initTags() {
        List<Map<String, Object>> tags = NebulaUtil.execute("show tags");
        Map<String, Class<?>> allTagBean = ReflectionCache.getAllTagBean();
        if (!tags.isEmpty()) {
            List<String> tagValues = toStringList(tags);
            allTagBean.values().parallelStream().forEach(tag -> {
                if (tagValues.contains(ReflectionCache.getFromTagCache(tag.getName()))) {
                    updateTag(tag);
                } else {
                    createTag(tag);
                }
            });
        } else {
            allTagBean.values().parallelStream().forEach(this::createTag);
        }

    }

    /**
     * 通用字段更新方法
     *
     * @param nebulaField 注解
     * @param field       类对象
     * @param builder     StringBuilder
     */
    private void updateField(NebulaField nebulaField,
                             Field field,
                             StringBuilder builder) {
        String name = nebulaField.name().isEmpty() ? field.getName() : nebulaField.name();
        String dataType = String.valueOf(nebulaField.dataType());
        builder.append(name).append(" ").append(dataType);
        commonAppend(builder, nebulaField);
        builder.append(",\n");
        builder.deleteCharAt(builder.lastIndexOf(","));
        builder.append(");");

    }

    /**
     * 通用字段更新方法
     *
     * @param builder     StringBuilder
     * @param nebulaField 注解
     */
    private static void commonAppend(StringBuilder builder, NebulaField nebulaField) {
        if (nebulaField.required() == NebulaFieldEnum.NOT_NULL) {
            builder.append(" NOT NULL");
        }
        if (StringUtils.isNotBlank(nebulaField.defaultValue())) {
            builder.append(" DEFAULT ").append(nebulaField.defaultValue());
        }
        if (StringUtils.isNotBlank(nebulaField.comment())) {
            builder.append(" COMMENT \"").append(nebulaField.comment()).append("\"");
        }
    }

    /**
     * 更新标签
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void updateTag(Class<?> tag) {
        String nGQL = getDescNGQL("TAG", ReflectionCache.getFromTagCache(tag.getName()));
        List<Map<String, Object>> tags = NebulaUtil.execute(nGQL);
        Map<String, Map<String, Object>> fieldMap = tags.stream()
                .collect(Collectors.toMap(map -> (String) map.get("Field"),
                        Function.identity()));
        String tagName = ReflectionCache.getFromTagCache(tag.getName());
        Field[] fields = getAllField(tag);
        for (Field field : fields) {
            NebulaField nebulaField = field.getAnnotation(NebulaField.class);
            String name = nebulaField.name().isEmpty() ? field.getName() : nebulaField.name();
            if (fieldMap.containsKey(name)) {
                continue;
            }
            StringBuilder tagBuilder = new StringBuilder("ALTER TAG ").append(tagName).append(" ADD ").append(" (").append("\n");
            updateField(nebulaField, field, tagBuilder);
            NebulaUtil.execute(tagBuilder.toString());
        }
        log.info("[Nebula]======> Tag {} updated successfully.", tagName);
    }

    /**
     * 获取所有字段
     *
     * @param clazz 类对象
     */
    private static Field[] getAllField(Class<?> clazz) {
        return Stream.concat(Arrays.stream(clazz.getDeclaredFields()),
                Arrays.stream(clazz.getSuperclass().getDeclaredFields())).toArray(Field[]::new);
    }

    /**
     * 初始化标签
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void createTag(Class<?> clazz) {
        String tagName = ReflectionCache.getFromTagCache(clazz.getName());
        StringBuilder tagBuilder = new StringBuilder("CREATE TAG IF NOT EXISTS ").append(tagName).append(" (").append("\n");
        parseField(clazz, tagBuilder);
        NebulaUtil.execute(tagBuilder.toString());
        log.info("[Nebula]======> Tag {} created successfully.", tagName);
    }

    /**
     * 初始化边
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void initEdges() {
        List<Map<String, Object>> edges = NebulaUtil.execute("show edges");
        Map<String, Class<?>> allEdgeBean = ReflectionCache.getAllEdgeBean();
        if (!edges.isEmpty()) {
            List<String> tagValues = toStringList(edges);
            allEdgeBean.values().parallelStream().forEach(edge -> {
                if (tagValues.contains(ReflectionCache.getFromEdgeCache(edge.getName()))) {
                    updateEdge(edge);
                } else {
                    createEdge(edge);
                }
            });
        } else {
            allEdgeBean.values().parallelStream().forEach(this::createEdge);
        }

    }

    /**
     * 更新边
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void updateEdge(Class<?> edge) {
        String nGQL = getDescNGQL("EDGE", ReflectionCache.getFromEdgeCache(edge.getName()));
        List<Map<String, Object>> edges = NebulaUtil.execute(nGQL);
        Map<String, Map<String, Object>> fieldMap = edges.stream()
                .collect(Collectors.toMap(map -> (String) map.get("Field"),
                        Function.identity()));
        String edgeName = ReflectionCache.getFromEdgeCache(edge.getName());
        Field[] fields = Stream.concat(Arrays.stream(edge.getDeclaredFields()),
                Arrays.stream(edge.getSuperclass().getDeclaredFields())).toArray(Field[]::new);
        for (Field field : fields) {
            NebulaField nebulaField = field.getAnnotation(NebulaField.class);
            String name = nebulaField.name().isEmpty() ? field.getName() : nebulaField.name();
            if (fieldMap.containsKey(name)) {
                continue;
            }
            StringBuilder edgeBuilder = new StringBuilder("ALTER EDGE ").append(edgeName).append(" ADD ").append(" (").append("\n");
            updateField(nebulaField, field, edgeBuilder);
            NebulaUtil.execute(edgeBuilder.toString());
        }
        log.info("[Nebula]======> Edge {} updated successfully.", edgeName);
    }

    /**
     * 获取描述NGQL
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private String getDescNGQL(String type, String name) {
        return String.format("DESCRIBE %s %s",
                type, name);
    }

    /**
     * 初始化边
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void createEdge(Class<?> clazz) {
        String edgeName = ReflectionCache.getFromEdgeCache(clazz.getName());
        StringBuilder edgeBuilder = new StringBuilder("CREATE EDGE IF NOT EXISTS ").append(edgeName).append(" (").append("\n");
        parseField(clazz, edgeBuilder);
        NebulaUtil.execute(edgeBuilder.toString());
        log.info("[Nebula]======> Edge {} created successfully.", edgeName);
    }

    /**
     * 解析字段
     *
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private void parseField(Class<?> clazz, StringBuilder edgeBuilder) {
        Class<?> currentClass = clazz;
        while (currentClass != null) {
            Field[] fields = currentClass.getDeclaredFields();
            for (Field field : fields) {
                NebulaField nebulaField = field.getAnnotation(NebulaField.class);
                if (nebulaField != null) {
                    String name = nebulaField.name().isEmpty() ? field.getName() : nebulaField.name();
                    String dataType = String.valueOf(nebulaField.dataType());
                    edgeBuilder.append("    ").append(name).append(" ").append(dataType);
                    commonAppend(edgeBuilder, nebulaField);
                    edgeBuilder.append(",\n");
                }
            }
            currentClass = currentClass.getSuperclass();
        }
        edgeBuilder.deleteCharAt(edgeBuilder.lastIndexOf(","));
        edgeBuilder.append(");");
    }


    /**
     * 将List<Map<String, Object>> 转换为List<String>
     *
     * @param spaces List<Map<String, Object>>
     * @return List<String>
     * <AUTHOR>
     * @date 2025/3/21 14:51
     */
    private List<String> toStringList(List<Map<String, Object>> spaces) {
        return spaces.stream()
                .flatMap(map -> map.values().stream())
                .map(Object::toString)
                .toList();
    }

    @Override
    public void stop() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return running;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 由SmartLifecycle控制启动
    }

    @Override
    public int getPhase() {
        return Integer.MAX_VALUE;
    }
}