package com.bzlj.craft.nebula.util;

import com.bici.graphops.nebula.clause.NebulaCreateClause;
import com.bici.graphops.nebula.clause.NebulaUpdateClause;
import com.bici.graphops.nebula.connection.NebulaConnection;
import com.bici.graphops.nebula.connection.NebulaConnectionPool;
import com.bici.graphops.nebula.result.NebulaGraphResult;
import com.bici.graphops.nebula.util.NebulaValueConverter;
import com.bzlj.craft.nebula.annotation.NebulaField;
import com.bzlj.craft.nebula.cache.ReflectionCache;
import com.bzlj.craft.nebula.entity.common.BasicField;
import com.bzlj.craft.nebula.enums.NebulaFieldTypeEnum;
import com.bzlj.craft.util.JsonUtils;
import com.vesoft.nebula.DateTime;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 图数据库工具类
 *
 * <AUTHOR>
 * @date 2025/3/21 11:19
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Slf4j
public class NebulaUtil {
    private static NebulaConnectionPool pool;
    private static final NebulaValueConverter valueConverter = new NebulaValueConverter();
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    public NebulaUtil(NebulaConnectionPool pool) {
        NebulaUtil.pool = pool;
    }

    private static NebulaConnection getConnection() {
        return pool.getConnection();
    }

    private static NebulaConnection getConnection(String key) {
        return pool.getConnection(key);
    }

    /**
     * 插入
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean insert(T t) {
        insertBefore(t);
        NebulaConnection connection = getNebulaConnection(null);
        String s = buildCreateNGQL(t);
        return executeStatus(connection, s);
    }


    /**
     * 插入
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean insert(T t, String key) {
        insertBefore(t);
        NebulaConnection connection = getNebulaConnection(key);
        return executeStatus(connection, buildCreateNGQL(t));
    }

    /**
     * 更新
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean update(T t) {
        updateBefore(t);
        NebulaConnection connection = getNebulaConnection(null);
        return executeStatus(connection, buildUpdateNGQL(t));
    }

    /**
     * 更新之前
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> void updateBefore(T t) {
        t.update();
    }


    /**
     * 更新
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean update(T t, String key) {
        updateBefore(t);
        NebulaConnection connection = getNebulaConnection(key);
        return executeStatus(connection, buildUpdateNGQL(t));
    }

    /**
     * 执行
     *
     * @param nGQL NGQL
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static List<Map<String, Object>> execute(String nGQL) {
        NebulaConnection connection = getNebulaConnection(null);
        try {
            ResultSet result = execute(nGQL, connection);
            if (result.isSucceeded()) {
                NebulaGraphResult maps = new NebulaGraphResult(result);
                return maps.getRowData();
            } else {
                throw new RuntimeException("Nebula 查询异常:" + result.getErrorMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 执行
     *
     * @param nGQL       NGQL
     * @param connection 连接
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static ResultSet execute(String nGQL, NebulaConnection connection) {
        log.info("[Nebula]======> Execute NGQL: {}", nGQL);
        try {

            return connection.getSession().execute(nGQL);
        } catch (IOErrorException e) {
            throw new RuntimeException(e);
        } finally {
            if (connection != null) {
                pool.releaseConnection(connection);
            }
        }
    }

    /**
     * 执行
     *
     * @param nGQL NGQL
     * @param key  key
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static List<Map<String, Object>> execute(String nGQL, String key) {
        NebulaConnection connection = getNebulaConnection(key);
        try {
            ResultSet result = execute(nGQL, connection);
            if (result.isSucceeded()) {
                NebulaGraphResult maps = new NebulaGraphResult(result);
                return maps.getRowData();
            } else {
                throw new RuntimeException("Nebula 查询异常:" + result.getErrorMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 返回执行状态
     *
     * @param connection 连接
     * @param nGQL       NGQL
     * <AUTHOR>
     * @date 2025/3/21 14:44
     */
    private static Boolean executeStatus(NebulaConnection connection, String nGQL) {
        ResultSet execute = execute(nGQL, connection);
        if (!execute.isSucceeded()) {
            throw new RuntimeException("Nebula 执行异常:" + execute.getErrorMessage());
        }
        return execute.isSucceeded();
    }

    /**
     * 插入前
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> void insertBefore(T t) {
        if (ObjectUtils.isEmpty(t.getCreateItemTime())) {
            t.create();
        }
        if (ObjectUtils.isEmpty(t.getUpdateItemTime())) {
            t.setUpdateItemTime(t.getCreateItemTime());
        }
    }

    /**
     * 获取连接
     *
     * @param key key
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static NebulaConnection getNebulaConnection(String key) {
        return StringUtils.isBlank(key) ? getConnection() : getConnection(key);
    }

    /**
     * 构建更新图数据库的NGQL
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> String buildUpdateNGQL(T t) {
        String nGQL = "";
        Map<String, Object> properties = getProperties(t);
        if (ObjectUtils.isEmpty(properties)) {
            return nGQL;
        }
        NebulaUpdateClause updateClause = new NebulaUpdateClause();
        updateClause.node(getLabelFromClass(t), properties);
        List<String> createStatements = updateClause.getUpdateStatements();
        if (CollectionUtils.isEmpty(createStatements)) {
            return nGQL;
        }
        return createStatements.getFirst();
    }

    /**
     * 构建创建图数据库的NGQL
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> String buildCreateNGQL(T t) {
        String nGQL = "";
        Map<String, Object> properties = getProperties(t);
        if (ObjectUtils.isEmpty(properties)) {
            return nGQL;
        }
        NebulaCreateClause createClause = new NebulaCreateClause();
        createClause.node(getLabelFromClass(t), properties);
        List<String> createStatements = createClause.getCreateStatements();
        if (CollectionUtils.isEmpty(createStatements)) {
            return nGQL;
        }
        return createStatements.getFirst();
    }

    /**
     * 获取属性
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> Map<String, Object> getProperties(T t) {
        Map<String, Object> properties = JsonUtils.toMap(t);
        String idCache = ReflectionCache.getFromIdCache(t.getClass().getName());
        properties.put("id", properties.getOrDefault(idCache, NebulaValueConverter.generateSnowflakeId() + ""));
        Field[] fields = Stream.concat(Arrays.stream(t.getClass().getDeclaredFields()),
                Arrays.stream(t.getClass().getSuperclass().getDeclaredFields())).toArray(Field[]::new);
        Map<String, Field> FieldMap = Arrays.stream(fields).collect(Collectors.toMap(Field::getName, Function.identity()));
        FieldMap.forEach((key, field) -> {
            if (!properties.containsKey(key) || ObjectUtils.isEmpty(properties.get(key))) {
                return;
            }
            NebulaField nebulaField = field.getAnnotation(NebulaField.class);
            NebulaFieldTypeEnum nebulaFieldTypeEnum = nebulaField.dataType();
            switch (nebulaFieldTypeEnum) {
                case DATETIME ->
                        properties.put(key, LocalDateTime.parse(properties.get(key).toString(), DATETIME_FORMATTER));
                case DATE -> properties.put(key, LocalDate.parse(properties.get(key).toString(), DATE_FORMATTER));
            }
            String fieldName = nebulaField.name();
            if (!key.equals(fieldName) && StringUtils.isNotBlank(fieldName)) {
                properties.put(fieldName, properties.get(key));
                properties.remove(key);
            }
        });
        return properties;
    }

    /**
     * 获取标签
     *
     * @param t class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> String getLabelFromClass(T t) {
        Class<? extends BasicField> clazz = t.getClass();
        String name = clazz.getName();
        return StringUtils.isBlank(ReflectionCache.getFromEdgeCache(name)) ?
                ReflectionCache.getFromTagCache(name) : ReflectionCache.getFromEdgeCache(name);
    }


    /**
     * 提取第一个字段列表的正则表达式
     * 匹配模式：material(字段列表)
     */
    private static final Pattern DYNAMIC_PATTERN = Pattern.compile(
            "INSERT\\s+VERTEX\\s+(\\w+)\\s*\\(\\s*([^)]+?)\\s*\\)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );


    /**
     * 批量插入
     *
     * @param entities 实体列表
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean batchInsert(List<T> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return false;
        }
        StringBuilder nGQL = null;
        NebulaConnection connection = getNebulaConnection(null);
        List<String> cloumns = new ArrayList<>();
        for (T entity : entities) {
            insertBefore(entity);
            if (ObjectUtils.isEmpty(nGQL)) {
                nGQL = new StringBuilder(buildCreateNGQL(entity));
                String[] result = parseInsertSQL(nGQL.toString());
                System.out.println("表名: " + result[0]);
                cloumns = Arrays.asList(result[1].split(","));
            } else {
                nGQL.append(", ").append(buildAppendCreateNGQL(entity, cloumns));
            }
        }
        if (ObjectUtils.isEmpty(nGQL)) {
            return false;
        }
        return executeStatus(connection, nGQL.toString());
    }

    /**
     * 解析SQL语句获取表名和字段列表
     *
     * @return 包含表名和字段列表的字符串数组
     */
    public static String[] parseInsertSQL(String sql) {

        Matcher matcher = DYNAMIC_PATTERN.matcher(sql);
        if (matcher.find()) {
            return new String[]{
                    matcher.group(1),  // 表名
                    matcher.group(2)   // 字段列表
            };
        }
        throw new IllegalArgumentException("无效的INSERT语句格式");
    }


    /**
     * 构建追加创建图数据库的NGQL
     *
     * @param entity class
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> String buildAppendCreateNGQL(T entity, List<String> cloumns) {
        Map<String, Object> properties = new LinkedHashMap<>();
        Map<String, Object> data = getProperties(entity);
        for (String cloumn : cloumns) {
            properties.put(cloumn, valueConverter.convertToNebulaValue(data.get(cloumn.trim())));
        }
        String id = valueConverter.convertToNebulaValue(data.get("id"));
        return String.format("%s:(%s)", id, StringUtils.join(properties.values(), ","));
    }

    /**
     * 批量更新
     *
     * @param entities 实体列表
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean batchUpdate(List<T> entities) throws IOErrorException {
        if (CollectionUtils.isEmpty(entities)) {
            return false;
        }
        NebulaConnection connection = getNebulaConnection(null);
        for (T entity : entities) {
            entity.setUpdateItemTime(LocalDateTime.now());
            if (!executeStatus(connection, buildUpdateNGQL(entity))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 创建边
     *
     * @param leftTag  左标签
     * @param rightTag 右标签
     * @param edge     边
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean insertEdge(T leftTag, T rightTag, T edge) throws IOErrorException {
        if (ObjectUtils.isEmpty(leftTag) || ObjectUtils.isEmpty(rightTag) || ObjectUtils.isEmpty(edge)) {
            return false;
        }
        String leftLabel = getLabelFromClass(leftTag);
        String rightLabel = getLabelFromClass(rightTag);
        return createEdge(edge, new NebulaCreateClause(), leftLabel, rightLabel);
    }

    /**
     * 创建边
     *
     * @param leftTag  左标签
     * @param rightTag 右标签
     * @param edge     边
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    public static <T extends BasicField> Boolean insertEdge(String leftTag, String rightTag, T edge) {
        if (StringUtils.isBlank(leftTag) || StringUtils.isBlank(rightTag) || ObjectUtils.isEmpty(edge)) {
            return false;
        }
        return createEdge(edge, new NebulaCreateClause(), leftTag, rightTag);
    }

    /**
     * 创建边
     *
     * @param edge         边
     * @param createClause 创建语句
     * @param leftLabel    左标签
     * @param rightLabel   右标签
     * <AUTHOR>
     * @date 2025/3/21 13:15
     */
    private static <T extends BasicField> Boolean createEdge(T edge, NebulaCreateClause createClause, String leftLabel, String rightLabel) {
        insertBefore(edge);
        String edgeLabel = getLabelFromClass(edge);
        Map<String, Object> properties = getProperties(edge);
        properties.remove("id");
        createClause.relationship(leftLabel, rightLabel, edgeLabel, properties);
        String nGQL = createClause.getCreateStatements().getFirst();
        NebulaConnection connection = getNebulaConnection(null);
        return executeStatus(connection, nGQL);
    }


    /**
     * 将NebulaGraph值转换为Java对象
     *
     * @param value NebulaGraph值包装器
     * @return Java对象
     */
    public static Object convertNebulaValue(ValueWrapper value) {
        if (value == null || value.isNull()) {
            return null;
        }

        try {
            // 根据值类型进行转换
            switch (descType(value.getValue().getSetField())) {
                case "BOOL":
                    return value.asBoolean();
                case "INT":
                    return value.asLong();
                case "FLOAT":
                    return value.asDouble();
                case "STRING":
                    return value.asString();
                case "DATE":
                    return value.asDate();
                case "TIME":
                    return value.asTime().getLocalTimeStr();
                case "DATETIME":
                    DateTime dateTime = value.asDateTime().getLocalDateTime();
                    return String.format("%d-%02d-%02d %02d:%02d:%02d",
                            dateTime.year, dateTime.month, dateTime.day,
                            dateTime.hour, dateTime.minute, dateTime.sec);
                case "VERTEX":
                    // 将顶点转换为Map
                    Map<String, Object> vertexMap = new HashMap<>();
                    vertexMap.put("id", value.asNode().getId());
                    vertexMap.put("tags", value.asNode().tagNames());
                    HashMap<String, HashMap<String, ValueWrapper>> properties = new HashMap<>();
                    for (String tag : value.asNode().tagNames()) {
                        properties.put(tag, value.asNode().properties(tag));
                    }
                    vertexMap.put("properties", properties);
                    return vertexMap;
                case "EDGE":
                    // 将边转换为Map
                    Map<String, Object> edgeMap = new HashMap<>();
                    edgeMap.put("src", value.asRelationship().srcId());
                    edgeMap.put("dst", value.asRelationship().dstId());
                    edgeMap.put("type", value.asRelationship().edgeName());
                    edgeMap.put("properties", value.asRelationship().properties());
                    return edgeMap;
                case "PATH":
                    // 将路径转换为Map列表
                    return value.asPath().toString();
                case "LIST":
                    // 递归转换列表中的每个元素
                    List<Object> list = new ArrayList<>();
                    for (ValueWrapper item : value.asList()) {
                        list.add(convertNebulaValue(item));
                    }
                    return list;
                case "MAP":
                    // 递归转换Map中的每个值
                    Map<String, Object> map = new HashMap<>();
                    for (Map.Entry<String, ValueWrapper> entry : value.asMap().entrySet()) {
                        map.put(entry.getKey(), convertNebulaValue(entry.getValue()));
                    }
                    return map;
                default:
                    return value.toString();
            }
        } catch (Exception e) {
            log.warn("转换NebulaGraph值时发生错误: {}", e.getMessage());
            return value.toString();
        }
    }

    private static String descType(int fieldId) {
        return switch (fieldId) {
            case 1 -> "NULL";
            case 2 -> "BOOLEAN";
            case 3 -> "INT";
            case 4 -> "FLOAT";
            case 5 -> "STRING";
            case 6 -> "DATE";
            case 7 -> "TIME";
            case 8 -> "DATETIME";
            case 9 -> "VERTEX";
            case 10 -> "EDGE";
            case 11 -> "PATH";
            case 12 -> "LIST";
            case 13 -> "MAP";
            case 14 -> "SET";
            case 15 -> "DATASET";
            case 16 -> "GEOGRAPHY";
            case 17 -> "DURATION";
            default -> throw new IllegalArgumentException("Unknown field id: " + fieldId);
        };
    }
}
