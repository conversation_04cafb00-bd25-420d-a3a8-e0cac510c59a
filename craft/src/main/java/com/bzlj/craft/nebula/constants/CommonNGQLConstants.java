package com.bzlj.craft.nebula.constants;

/**
 * 常用的查询NGQL
 *
 * <AUTHOR>
 * @date 2025/3/25 11:11
 * @motto I know I\"m not smart, but I want to be a good architect. Come on
 */
public class CommonNGQLConstants {
    public static final String QUERY_TASK_BY_TASK_CODE = "MATCH (t:task{taskCode:'%s'}) return t";
    public static final String QUERY_TASK_BY_TASK_ID = "MATCH (t:task) where id(t) == \"%s\" return t";
    public static final String QUERY_EQUIP_BY_EQUIP_CODE = "MATCH (e:equip{equipCode:'%s'})  return e";
    public static final String QUERY_EQUIP_BY_EQUIP_ID = "MATCH (e:equip) where id(e) == \"%s\" return e";
    public static final String QUERY_MATERIAL_BY_MATERIAL_CODE = "MATCH (m:material{materialCode:'%s'}) return m";
    public static final String QUERY_MATERIAL_BY_MATERIAL_ID = "MATCH (m:material) where id(m) == \"%s\" return m";

    public static final String QUERY_TASK_BY_OUTPUT_MATERIAL_CODE = "MATCH (t:task)-[r:outputMaterial]->(m:material{materialCode:'%s'})  return t";
    public static final String QUERY_TASK_BY_INPUT_MATERIAL_CODE = "MATCH (t:task)<-[r:inputMaterial]-(m:material{materialCode:'%s'})  return t";

    public static final String QUERY_TASK_BY_OUTPUT_MATERIAL_CODE_AND_PLANT_ID = "MATCH (t:task{plantId:'%s'})-[r:outputMaterial]->(m:material{materialCode:'%s'})  return t";
    public static final String QUERY_TASK_BY_INPUT_MATERIAL_CODE_AND_PLANT_ID = "MATCH (t:task{plantId:'%s'})<-[r:inputMaterial]-(m:material{materialCode:'%s'})  return t";

    public static final String QUERY_TASK_BY_EQUIP_CODE = "MATCH (t:task)-[r:useEquip]->(e:equip{equipCode:'%s'}) return t";

    public static final String QUERY_TASK_INPUT_MATERIAL_BY_TASK_ID = "MATCH (t:task)<-[r:inputMaterial]->(m:material) where id(t) == '%s' return m";
    public static final String QUERY_TASK_INPUT_MATERIAL_BY_TASK_CODE = "MATCH (t:task)<-[r:inputMaterial]->(m:material{taskCode:'%s'}) return m";
    public static final String QUERY_TASK_OUTPUT_MATERIAL_BY_TASK_ID = "MATCH (t:task)-[r:outputMaterial]->(m:material) where id(t) == '%s' return m";
    public static final String QUERY_TASK_OUTPUT_MATERIAL_BY_TASK_CODE = "MATCH (t:task)-[r:outputMaterial]->(m:material{taskCode:'%s'}) return m";

    public static final String QUERY_TASK_EQUIP_BY_TASK_CODE = "MATCH (t:task)-[r:useEquip]->(e:equip{taskCode:'%s'})  return e";
    public static final String QUERY_TASK_EQUIP_BY_TASK_ID = "MATCH (t:task)-[r:useEquip]->(e:equip) where id(t) == '%s' return e";

    public static final String QUERY_QUALITY_TRACE_PRE_PATH_BY_TASK_ID = "MATCH (e:task)<-[r1:inputMaterial]-(m:material)<-[r2:outputMaterial]-(s:task) where id(e) == '%s' return s,e";
    public static final String QUERY_QUALITY_TRACE_NEXT_PATH_BY_TASK_ID = "MATCH (s:task)-[r1:outputMaterial]->(m:material)-[r2:inputMaterial]->(e:task) where id(s) == '%s' return s,e";

    public static final String QUERY_QUALITY_TRACE_PRE_PATH_BY_TASK_ID_AND_PLANT_ID = "MATCH (e:task{plantId:'%s'})<-[r1:inputMaterial]-(m:material)<-[r2:outputMaterial]-(s:task{plantId:'%s'}) where id(e) == '%s' return s,e";
    public static final String QUERY_QUALITY_TRACE_NEXT_PATH_BY_TASK_ID_AND_PLANT_ID = "MATCH (s:task{plantId:'%s'})-[r1:outputMaterial]->(m:material)-[r2:inputMaterial]->(e:task{plantId:'%s'}) where id(s) == '%s' return s,e";

    /**
     * 获取根据任务编号查询任务的NGQL
     *
     * @param taskCode 任务编号
     * @return 任务
     */
    public static String getQueryTaskByTaskCodeNGQL(String taskCode) {
        return String.format(QUERY_TASK_BY_TASK_CODE, taskCode);
    }

    /**
     * 获取根据任务ID查询任务的NGQL
     *
     * @param taskId 任务ID
     * @return 任务
     */
    public static String getQueryTaskByTaskIdNGQL(String taskId) {
        return String.format(QUERY_TASK_BY_TASK_ID, taskId);
    }

    /**
     * 获取根据设备编号查询设备的NGQL
     *
     * @param equipCode 设备编号
     * @return 设备
     */
    public static String getQueryEquipByEquipCodeNGQL(String equipCode) {
        return String.format(QUERY_EQUIP_BY_EQUIP_CODE, equipCode);
    }

    /**
     * 获取根据设备ID查询设备的NGQL
     *
     * @param equipId 设备ID
     * @return 设备
     */
    public static String getQueryEquipByEquipIdNGQL(String equipId) {
        return String.format(QUERY_EQUIP_BY_EQUIP_ID, equipId);
    }

    /**
     * 获取根据物料编号查询物料的NGQL
     *
     * @param materialCode 物料编号
     * @return 物料
     */
    public static String getQueryMaterialByMaterialCodeNGQL(String materialCode) {
        return String.format(QUERY_MATERIAL_BY_MATERIAL_CODE, materialCode);
    }

    /**
     * 获取根据物料ID查询物料的NGQL
     *
     * @param materialId 物料ID
     * @return 物料
     */
    public static String getQueryMaterialByMaterialIdNGQL(String materialId) {
        return String.format(QUERY_MATERIAL_BY_MATERIAL_ID, materialId);
    }

    /**
     * 获取根据物料编号模糊查询产出物料的任务的NGQL
     *
     * @param materialCode 物料编号
     * @return 物料
     */
    public static String getQueryTaskByOutputMaterialCodeNGQL(String materialCode) {
        return String.format(QUERY_TASK_BY_OUTPUT_MATERIAL_CODE, materialCode);
    }

    /**
     * 获取根据物料编号模糊查询输入物料的任务的NGQL
     *
     * @param materialCode 物料编号
     * @return 物料
     */
    public static String getQueryTaskByInputMaterialCodeNGQL(String materialCode) {
        return String.format(QUERY_TASK_BY_INPUT_MATERIAL_CODE, materialCode);
    }


    /**
     * 获取根据物料编号模糊查询产出物料的任务的NGQL
     *
     * @param materialCode 物料编号
     * @return 物料
     */
    public static String getQueryTaskByOutputMaterialCodeAndPlantIdNGQL(String plantId, String materialCode) {
        return String.format(QUERY_TASK_BY_OUTPUT_MATERIAL_CODE_AND_PLANT_ID, plantId, materialCode);
    }

    /**
     * 获取根据物料编号模糊查询输入物料的任务的NGQL
     *
     * @param materialCode 物料编号
     * @return 物料
     */
    public static String getQueryTaskByInputMaterialCodeAndPlantIdNGQL(String plantId, String materialCode) {
        return String.format(QUERY_TASK_BY_INPUT_MATERIAL_CODE_AND_PLANT_ID, plantId, materialCode);
    }

    /**
     * 获取根据设备编号模糊查询使用设备的任务的NGQL
     *
     * @param equipCode 设备编号
     * @return 设备
     */
    public static String getQueryTaskByEquipCodeNGQL(String equipCode) {
        return String.format(QUERY_TASK_BY_EQUIP_CODE, equipCode);
    }


    /**
     * 获取根据任务ID查询输入物料的NGQL
     *
     * @param taskId 任务ID
     * @return 任务
     */
    public static String getQueryTaskInputMaterialByTaskIdNGQL(String taskId) {
        return String.format(QUERY_TASK_INPUT_MATERIAL_BY_TASK_ID, taskId);
    }

    /**
     * 获取根据任务编号查询输入物料的NGQL
     *
     * @param taskCode 任务编号
     * @return 任务
     */
    public static String getQueryTaskInputMaterialByTaskCodeNGQL(String taskCode) {
        return String.format(QUERY_TASK_INPUT_MATERIAL_BY_TASK_CODE, taskCode);
    }

    /**
     * 获取根据任务ID查询产出物料的NGQL
     *
     * @param taskId 任务ID
     * @return 任务
     */
    public static String getQueryTaskOutputMaterialByTaskIdNGQL(String taskId) {
        return String.format(QUERY_TASK_OUTPUT_MATERIAL_BY_TASK_ID, taskId);
    }

    /**
     * 获取根据任务编号查询产出物料的NGQL
     *
     * @param taskCode 任务编号
     * @return 任务
     */
    public static String getQueryTaskOutputMaterialByTaskCodeNGQL(String taskCode) {
        return String.format(QUERY_TASK_OUTPUT_MATERIAL_BY_TASK_CODE, taskCode);
    }

    /**
     * 根据任务编号查询任务使用的设备的NGQL
     *
     * @param taskCode 任务编号
     * <AUTHOR>
     * @date 2025/3/25 11:37
     */
    public static String getQueryTaskEquipByTaskCodeNGQL(String taskCode) {
        return String.format(QUERY_TASK_EQUIP_BY_TASK_CODE, taskCode);
    }

    /**
     * 根据任务ID查询任务使用的设备的NGQL
     *
     * @param taskId 任务ID
     * <AUTHOR>
     * @date 2025/3/25 11:37
     */
    public static String getQueryTaskEquipByTaskIdNGQL(String taskId) {
        return String.format(QUERY_TASK_EQUIP_BY_TASK_ID, taskId);
    }

    /**
     * 根据任务ID查询质量追踪的前驱路径的NGQL
     *
     * @param taskId 任务ID
     * <AUTHOR>
     * @date 2025/3/25 11:37
     */
    public static String getQueryQualityTracePrePathByTaskIdNGQL(String taskId) {
        return String.format(QUERY_QUALITY_TRACE_PRE_PATH_BY_TASK_ID, taskId);
    }

    /**
     * 根据任务ID查询质量追踪的后继路径的NGQL
     *
     * @param taskId 任务ID
     * <AUTHOR>
     * @date 2025/3/25 11:37
     */
    public static String getQueryQualityTraceNextPathByTaskIdNGQL(String taskId) {
        return String.format(QUERY_QUALITY_TRACE_NEXT_PATH_BY_TASK_ID, taskId);
    }


    /**
     * 根据任务ID查询质量追踪的前驱路径的NGQL
     *
     * @param taskId 任务ID
     * <AUTHOR>
     * @date 2025/3/25 11:37
     */
    public static String getQueryQualityTracePrePathByTaskIdAndPlantIdNGQL(String plantId, String taskId) {
        return String.format(QUERY_QUALITY_TRACE_PRE_PATH_BY_TASK_ID_AND_PLANT_ID, plantId, plantId, taskId);
    }

    /**
     * 根据任务ID查询质量追踪的后继路径的NGQL
     *
     * @param taskId 任务ID
     * <AUTHOR>
     * @date 2025/3/25 11:37
     */
    public static String getQueryQualityTraceNextPathByTaskIdAndPlantIdNGQL(String plantId, String taskId) {
        return String.format(QUERY_QUALITY_TRACE_NEXT_PATH_BY_TASK_ID_AND_PLANT_ID, plantId, plantId, taskId);
    }


}
