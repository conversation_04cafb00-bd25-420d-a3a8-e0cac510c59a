package com.bzlj.craft.nebula.config;

import com.bzlj.craft.nebula.annotation.NebulaEdge;
import com.bzlj.craft.nebula.annotation.NebulaId;
import com.bzlj.craft.nebula.annotation.NebulaTag;
import com.bzlj.craft.nebula.cache.ReflectionCache;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

@Component
public class NebulaAnnotationCollector implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Class<?> clazz = bean.getClass();
        
        // 处理NebulaTag注解
        if (clazz.isAnnotationPresent(NebulaTag.class)) {
            NebulaTag nebulaTag = clazz.getAnnotation(NebulaTag.class);
            String tagName = nebulaTag.name().isEmpty() ? clazz.getSimpleName().toLowerCase() : nebulaTag.name();
            ReflectionCache.addToTagCache(clazz.getName(), tagName);
            ReflectionCache.addToTagBeanCache(clazz.getName(), clazz); // 新增：存储Class对象
            processNebulaIdFields(clazz);
        }
        
        // 处理NebulaEdge注解
        if (clazz.isAnnotationPresent(NebulaEdge.class)) {
            NebulaEdge nebulaEdge = clazz.getAnnotation(NebulaEdge.class);
            String edgeName = nebulaEdge.name().isEmpty() ? clazz.getSimpleName().toLowerCase() : nebulaEdge.name();
            ReflectionCache.addToEdgeCache(clazz.getName(), edgeName);
            ReflectionCache.addToEdgeBeanCache(clazz.getName(), clazz); // 新增：存储Class对象
            processNebulaIdFields(clazz);
        }
        
        return bean;
    }

    private void processNebulaIdFields(Class<?> clazz) {
        for (Field field : clazz.getDeclaredFields()) {
            if (field.isAnnotationPresent(NebulaId.class)) {
                ReflectionCache.addToIdCache(clazz.getName(), field.getName());
            }
        }
    }
}