package com.bzlj.craft.nebula.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * NebulaField 字段非空枚举
 *
 * <AUTHOR>
 * @date 2025/3/20 17:32
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@RequiredArgsConstructor
@Getter
public enum NebulaFieldEnum {
    /**
     * NULL
     */
    NULL(" NULL "),
    /**
     * NULL
     */
    NOT_NULL(" NOT NULL "),
    ;
    private final String type;
}
