package com.bzlj.craft.nebula.entity.tag;

import com.bzlj.craft.nebula.annotation.NebulaField;
import com.bzlj.craft.nebula.annotation.NebulaId;
import com.bzlj.craft.nebula.annotation.NebulaIndex;
import com.bzlj.craft.nebula.annotation.NebulaTag;
import com.bzlj.craft.nebula.entity.common.BasicField;
import com.bzlj.craft.nebula.enums.NebulaFieldEnum;
import com.bzlj.craft.nebula.enums.NebulaFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 图数据库设备模型
 *
 * <AUTHOR>
 * @date 2025/3/21 10:42
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@NebulaTag(name = "equip")
public class Equip extends BasicField {
    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "设备Id")
    @NebulaId
    private String equipId;

    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "设备名称")
    @NebulaIndex
    private String equipName;

    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "设备编码")
    @NebulaIndex
    private String equipCode;

}
