package com.bzlj.craft.nebula.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * NebulaField 字段非空枚举
 *
 * <AUTHOR>
 * @date 2025/3/20 17:32
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@RequiredArgsConstructor
@Getter
public enum NebulaFieldTypeEnum {
    /**
     * 布尔型
     */
    BOOL(" BOOL "),
    /**
     * 数值型
     */
    INT(" INT "),
    /**
     * 字符型
     */
    STRING(" STRING "),
    /**
     * 时间类型1=>DATE
     */
    DATE(" DATE "),
    /**
     * 时间类型2=>TIME
     */
    TIME(" TIME "),
    /**
     * 时间类型3=>DATETIME
     */
    DATETIME(" DATETIME "),
    /**
     * 时间类型4=>TIMESTAMP
     */
    TIMESTAMP(" TIMESTAMP "),
    /**
     * 时间类型5=>DURATION
     */
    DURATION(" DURATION "),
    /**
     * 地理位置类型
     */
    GEOGRAPHY(" GEOGRAPHY ");
    private final String dataType;
}
