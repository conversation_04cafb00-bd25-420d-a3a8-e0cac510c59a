package com.bzlj.craft.nebula.config;

import com.bici.graphops.nebula.connection.NebulaConnectionConfig;
import com.bici.graphops.nebula.connection.NebulaConnectionPool;
import com.bzlj.craft.nebula.init.NebulaInitializer;
import com.bzlj.craft.nebula.util.NebulaUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Nebula 图数据库配置类
 *
 * <AUTHOR>
 * @date 2025/3/20 17:17
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
@AutoConfigureBefore(NebulaInitializer.class)
public class NebulaConfiguration {
    private final NebulaConfig config;

    @Bean
    public NebulaConnectionConfig getNebulaConnectionConfig() {
        return new NebulaConnectionConfig(config.getUrl(),
                config.getUsername(),
                config.getPassword(),
                config.getSpace().getSpaceName());
    }

    @Bean
    public NebulaConnectionPool getNebulaConnectionPool(NebulaConnectionConfig nebulaConnectionConfig) {
        NebulaConnectionPool nebulaConnectionPool = new NebulaConnectionPool(nebulaConnectionConfig, config.getMaxPoolSize());
        // 设置优雅的关闭
        Runtime.getRuntime().addShutdownHook(new Thread(nebulaConnectionPool::close));
        return nebulaConnectionPool;
    }

    @Bean
    public NebulaUtil getNebulaUtil(NebulaConnectionPool pool) {
        return new NebulaUtil(pool);
    }
}
