package com.bzlj.craft.nebula.entity.edge;

import com.bzlj.craft.nebula.annotation.NebulaEdge;
import com.bzlj.craft.nebula.entity.common.BasicField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 边: 产出物料
 *
 * <AUTHOR>
 * @date 2025/3/20 17:52
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NebulaEdge(name = "outputMaterial")
public class OutputMaterial extends BasicField {
}
