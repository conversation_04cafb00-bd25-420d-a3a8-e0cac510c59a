package com.bzlj.craft.nebula.entity.tag;

import com.bzlj.craft.nebula.annotation.NebulaField;
import com.bzlj.craft.nebula.annotation.NebulaId;
import com.bzlj.craft.nebula.annotation.NebulaIndex;
import com.bzlj.craft.nebula.annotation.NebulaTag;
import com.bzlj.craft.nebula.entity.common.BasicField;
import com.bzlj.craft.nebula.enums.NebulaFieldEnum;
import com.bzlj.craft.nebula.enums.NebulaFieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 图数据库物料模型
 *
 * <AUTHOR>
 * @date 2025/3/20 17:51
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@NebulaTag(name = "material")
public class Material extends BasicField {
    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "物料Id")
    @NebulaId
    private String materialId;

    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "物料编码")
    @NebulaIndex
    private String materialCode;

    @NebulaField(dataType = NebulaFieldTypeEnum.STRING,
            comment = "物料名称")
    @NebulaIndex
    private String materialName;

}
