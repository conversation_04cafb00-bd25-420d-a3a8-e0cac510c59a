package com.bzlj.craft.nebula.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Nebula 图数据库配置类
 *
 * <AUTHOR>
 * @date 2025/3/20 17:17
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NebulaSpaceConfig {
    /**
     * 数据库空间
     */
    private String spaceName;
    /**
     * ID的数据类型 可选值为FIXED_STRING(<N>)和INT64。INT等同于INT64。
     */
    private String vidType;

    /**
     * 描述
     */
    private String comment;
}
