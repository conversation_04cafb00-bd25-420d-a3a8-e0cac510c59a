package com.bzlj.craft.nebula.annotation;

import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * Nebula 数据库 Edge 注解
 *
 * <AUTHOR>
 * @date 2025/3/20 17:22
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME) // 注解在运行时保留
@Documented
@Component
public @interface NebulaEdge {
    /**
     * 边/关系名称,默认类名小写
     */
    String name() default "";
}
