package com.bzlj.craft.nebula.entity.tag;

import com.bzlj.craft.nebula.annotation.NebulaField;
import com.bzlj.craft.nebula.annotation.NebulaId;
import com.bzlj.craft.nebula.annotation.NebulaIndex;
import com.bzlj.craft.nebula.annotation.NebulaTag;
import com.bzlj.craft.nebula.entity.common.BasicField;
import com.bzlj.craft.nebula.enums.NebulaFieldEnum;
import com.bzlj.craft.nebula.enums.NebulaFieldTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 图数据库任务模型
 *
 * <AUTHOR>
 * @date 2025/3/20 17:51
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@NebulaTag(name = "task")
public class Task extends BasicField {
    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "任务Id")
    @NebulaId
    private String taskId;

    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "任务编号")
    @NebulaIndex
    private String taskCode;

    @NebulaField(required = NebulaFieldEnum.NOT_NULL,
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "任务名称")
    @NebulaIndex
    private String taskName;

    @NebulaField(
            dataType = NebulaFieldTypeEnum.DATETIME,
            comment = "任务开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @NebulaField(
            dataType = NebulaFieldTypeEnum.DATETIME,
            comment = "任务结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @NebulaField(
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "车间Id")
    @NebulaIndex
    private String workshopId;

    @NebulaField(
            dataType = NebulaFieldTypeEnum.STRING,
            comment = "厂区Id")
    @NebulaIndex
    private String plantId ;
}
