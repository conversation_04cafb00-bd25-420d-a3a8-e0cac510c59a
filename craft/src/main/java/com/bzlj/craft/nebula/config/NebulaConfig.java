package com.bzlj.craft.nebula.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Nebula 图数据库配置类
 *
 * <AUTHOR>
 * @date 2025/3/20 17:17
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Configuration
@ConfigurationProperties(prefix = "nebula.db")
public class NebulaConfig {
    /**
     * 地址拼接
     */
    private String url;
    /**
     * 数据库用户名
     */
    private String username;
    /**
     * 数据库密码
     */
    private String password;
    /**
     * 数据库空间
     */
    private NebulaSpaceConfig space;
    /**
     * 连接池最大连接数
     */
    private Integer maxPoolSize = 100;
}
