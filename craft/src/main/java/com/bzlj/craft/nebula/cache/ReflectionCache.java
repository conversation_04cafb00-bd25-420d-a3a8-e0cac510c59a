package com.bzlj.craft.nebula.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 反射缓存
 *
 * <AUTHOR>
 * @date 2025/3/21 13:37
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class ReflectionCache {
    /**
     * Id 字段缓存
     */
    private static final Map<String, String> ID_CACHE = new ConcurrentHashMap<>();
    /**
     * 标签缓存
     */
    private static final Map<String, String> TAG_CACHE = new ConcurrentHashMap<>();
    /**
     * 边缓存
     */
    private static final Map<String, String> EDGE_CACHE = new ConcurrentHashMap<>();
    /**
     * 标签class缓存
     */
    private static final Map<String, Class<?>> TAG_BEAN_CACHE = new ConcurrentHashMap<>();
    /**
     * 边class缓存
     */
    private static final Map<String, Class<?>> EDGE_BEAN_CACHE = new ConcurrentHashMap<>();

    // ID_CACHE 的 CRUD 操作
    public static void addToIdCache(String key, String value) {
        ID_CACHE.put(key, value);
    }

    public static String getFromIdCache(String key) {
        return ID_CACHE.get(key);
    }

    public static void updateIdCache(String key, String value) {
        ID_CACHE.put(key, value);
    }

    public static void removeFromIdCache(String key) {
        ID_CACHE.remove(key);
    }

    // TAG_CACHE 的 CRUD 操作
    public static void addToTagCache(String key, String value) {
        TAG_CACHE.put(key, value);
    }

    public static String getFromTagCache(String key) {
        return TAG_CACHE.get(key);
    }

    public static void updateTagCache(String key, String value) {
        TAG_CACHE.put(key, value);
    }

    public static void removeFromTagCache(String key) {
        TAG_CACHE.remove(key);
    }

    // EDGE_CACHE 的 CRUD 操作
    public static void addToEdgeCache(String key, String value) {
        EDGE_CACHE.put(key, value);
    }

    public static String getFromEdgeCache(String key) {
        return EDGE_CACHE.get(key);
    }

    public static void updateEdgeCache(String key, String value) {
        EDGE_CACHE.put(key, value);
    }

    public static void removeFromEdgeCache(String key) {
        EDGE_CACHE.remove(key);
    }


    public static void addToTagBeanCache(String key, Class<?> clazz) {
        TAG_BEAN_CACHE.put(key, clazz);
    }

    public static void addToEdgeBeanCache(String key, Class<?> clazz) {
        EDGE_BEAN_CACHE.put(key, clazz);
    }

    public static Class<?> getTagBean(String key) {
        return TAG_BEAN_CACHE.get(key);
    }

    public static Class<?> getEdgeBean(String key) {
        return EDGE_BEAN_CACHE.get(key);
    }

    public static Map<String, Class<?>> getAllTagBean() {
        return TAG_BEAN_CACHE;
    }

    public static Map<String, Class<?>> getAllEdgeBean() {
        return EDGE_BEAN_CACHE;
    }

    public static Boolean hasTagBeanCache() {
        return !TAG_BEAN_CACHE.isEmpty();
    }

    public static Boolean hasEdgeBeanCache() {
        return !EDGE_BEAN_CACHE.isEmpty();
    }

    public static void clearTagBeanCache() {
        TAG_BEAN_CACHE.clear();
    }

    public static void clearEdgeBeanCache() {
        EDGE_BEAN_CACHE.clear();
    }
}

