package com.bzlj.craft.nebula.annotation;

import com.bzlj.craft.nebula.enums.NebulaFieldEnum;
import com.bzlj.craft.nebula.enums.NebulaFieldTypeEnum;

import java.lang.annotation.*;

/**
 * Nebula 数据库 Field 注解
 *
 * <AUTHOR>
 * @date 2025/3/20 17:21
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME) // 注解在运行时保留
@Documented
public @interface NebulaField {
    /**
     * 字段名称,默认字段名
     */
    String name() default "";

    /**
     * 字段是否非空类型
     */
    NebulaFieldEnum required() default NebulaFieldEnum.NULL;

    /**
     * 字段类型
     */
    NebulaFieldTypeEnum dataType();

    /**
     * 字段备注
     */
    String comment() default "";

    /**
     * 默认值
     */
    String defaultValue() default "";
}
