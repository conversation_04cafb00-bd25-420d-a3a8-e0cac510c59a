package com.bzlj.craft.exception;


/**
 * <AUTHOR>
 * @description:
 * @date 2024-12-02 15:18
 */
public enum BiciErrorData {
    FETCH_BEAN_ERROR(001,"无法获取组件相关bean"),
    FETCH_COMPONENT_METHOD_ERROR(002,"获取组件绑定方法失败"),
    ERROR_RESPONSE(003,"非法的方法返回值"),
    TASK_ID_IS_NULL(004, "任务ID为空"),
    Metric_Type_ERROR(005,"质检指标数据类型出错"),
    SCORING_DATA_ERROR(006,"请检查打分规则"),
    TASK_NOT_EXIST(007,"%s:任务不存在"),
    STATUS_CHANGE_ERROR(8,"任务状态更新失败，状态不能从:%s->%s"),

    ;
    /**
     * 标记此异常来自哪个服务以及哪个模块: 服务名_模块_
     */
    public final static String PREFIX = "Craft";

    final String code;
    final String value;

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    BiciErrorData(int code, String value) {
        this.code = PREFIX + String.format("%04d", code);
        this.value = value;
    }

    public RuntimeException buildException(Object... args){
        String msg = String.format(this.getValue(),args);
        String code = this.code;

        return new RuntimeException(String.format("%s:%s",code,msg));
    }
}
