package com.bzlj.craft.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-18 17:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataPointSearchCommand {
    private String dataCode;

    private Boolean createEmpty = false;

    private String functionName;

    private String label;

    private Long start;

    private Long stop;

    private String windowFrequency;

    private String timeUnit;

}
