package com.bzlj.craft.websocket.pool;


import com.bzlj.craft.websocket.bean.Channel;
import com.bzlj.craft.websocket.bean.RealTimeRtData;
import com.bzlj.craft.websocket.bean.SubscriptionPoint;
import com.bzlj.craft.websocket.container.ChannelRtContainer;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
public class WebsocketPushRunnable implements Runnable {
    private final RealTimeRtData baseData;
    private final ChannelRtContainer channelRtContainer;

    public WebsocketPushRunnable(RealTimeRtData baseData, ChannelRtContainer channelRtContainer) {
        this.baseData = baseData;
        this.channelRtContainer = channelRtContainer;
    }

    /**
     * websocket推送订阅点得数据操作
     */
    @Override
    public void run() {
        ConcurrentHashMap<String, Map<String, Session>> subscriberMap = channelRtContainer.getSubscriberMap();
        ConcurrentHashMap<String, Channel> channelUserConcurrentHashMap = channelRtContainer.getSessionMap();
        Map<String, Session> sessionMap = subscriberMap.getOrDefault(baseData.getDc(), null);
        if (MapUtils.isNotEmpty(sessionMap)) {
            sessionMap.forEach((dc, session) -> {
                channelRtContainer.sendMessage(session, baseData);
                Channel channel = channelUserConcurrentHashMap.get(dc);
                Map<String, SubscriptionPoint> subscriptionMap = channel.getSubscriptionMap();
                SubscriptionPoint subscriptionPoint = subscriptionMap.get(baseData.getDc());
                if (Objects.nonNull(subscriptionPoint)) {
                    subscriptionPoint.initCount();
                }
            });
        }
    }
}
