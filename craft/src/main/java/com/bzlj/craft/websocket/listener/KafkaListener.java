package com.bzlj.craft.websocket.listener;

import com.bzlj.craft.websocket.bean.RealTimeRtData;
import com.bzlj.craft.websocket.container.ChannelRtContainer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Consumer;

@Configuration
@Slf4j
public class KafkaListener {


    /**
     * kafka消费实时数据
     *
     * @param channelRtContainer 容器实现类
     * @return Consumer<List < RealTimeRtData>>
     */
    @Bean
    public Consumer<List<RealTimeRtData>> realtimeDataConsumer(ChannelRtContainer channelRtContainer) {

        return (realTimeRtDataList -> realTimeRtDataList.forEach(realTimeRtData -> {
            try {
                if (MapUtils.isEmpty(channelRtContainer.getSubscriberMap())) {
                    return;
                }
                if (channelRtContainer.getSubscriberMap().containsKey(realTimeRtData.getDc())) {
                    channelRtContainer.publishMessageToSubscriber(realTimeRtData);
                }
            } catch (Exception e) {
                log.error("消费数据失败:", e);
            }

        }));
    }

}
