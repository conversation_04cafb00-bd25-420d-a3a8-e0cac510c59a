package com.bzlj.craft.websocket.pool;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadSendPool {
    private static ThreadPoolExecutor executor;
    private static final String SEND_WEB_SOCKET = "sendWebSocket";
    /**
     * cpu数量
     * 核心线程数量io任务CPU*2+1
     */
    private final static int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors();

    /**
     * 私有构造
     */
    private ThreadSendPool() {
    }

    /**
     * 单例懒汉获取线程池
     * LinkedBlockingQueue有内存溢出的风险，最大值为Integer的maxValue
     *
     * @return ThreadPoolExecutor
     */
    public static synchronized ThreadPoolExecutor getThreadPool() {
        if (null == executor) {
            executor = new ThreadPoolExecutor(CORE_POOL_SIZE * 2 + 1, CORE_POOL_SIZE * 2 + 1, 10,
                    TimeUnit.SECONDS, new LinkedBlockingQueue<>(10000),
                    r -> new Thread(r, SEND_WEB_SOCKET + r.hashCode()), new ThreadPoolExecutor.CallerRunsPolicy());
            //设置拒绝策略,由如果队列已满调用线程处理
        }
        return executor;
    }
}
