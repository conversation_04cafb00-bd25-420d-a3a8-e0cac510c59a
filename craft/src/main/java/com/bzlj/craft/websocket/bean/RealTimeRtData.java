package com.bzlj.craft.websocket.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-24 9:44
 */
@Data
public class RealTimeRtData implements Serializable {
    /**
     * 数据指标编码
     */
    private String dc;

    /**
     * 数据采集时间，13位毫秒精度的时间戳，示例：1636602194458，对应Date格式为：yyyy-MM-dd HH:mm:ss.SSS
     */
    private Long ts;

    /**
     * 瞬时值
     */
    private Object v;

    /**
     * 点位数据类型 1.数值类型 2.布尔类型 3.字符类型 4.双浮点数  5.长整形
     */
    private Integer vt;

    /**
     * 采集间隔时间
     */
    private Integer intervalTime;

    /**
     * tags
     */
    private Map<String, String> tags;
}
