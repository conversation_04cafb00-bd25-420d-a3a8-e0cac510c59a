package com.bzlj.craft.websocket.server;

import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.websocket.command.SubscriberCommand;
import com.bzlj.craft.websocket.container.ChannelRtContainer;
import jakarta.annotation.Resource;
import jakarta.websocket.OnClose;
import jakarta.websocket.OnMessage;
import jakarta.websocket.OnOpen;
import jakarta.websocket.Session;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-21 17:21
 */

@Component
//定义websocket服务器端，它的功能主要是将目前的类定义成一个websocket服务器端。注解的值将被用于监听用户连接的终端访问URL地址
@ServerEndpoint(value = "/dataPoint/webSocket/")
@Slf4j
public class DataPointWebSocketServer {
    //实例一个session，这个session是websocket的session

    private final static String HEART_BEAT = "HeartBeat";

    @Resource
    private ChannelRtContainer container;

    //前端请求时一个websocket时
    @OnOpen
    public void onOpen( Session session) {
        session.setMaxIdleTimeout(60000);
        container.addChannel(session);
    }

    //前端关闭时一个websocket时
    @OnClose
    public void onClose(Session session) {
        container.removeUserAndAllSubscriber(session);
        log.info("有一连接关闭！当前连接数为:{}", container.connectionCount());
    }

    //前端向后端发送消息
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            if (HEART_BEAT.equals(message)) {
                //   session.getAsyncRemote().sendPong(ByteBuffer.allocate(0));
                //心跳
                return;
            }

            SubscriberCommand command = JsonUtils.fromJson(message, SubscriberCommand.class);
            container.updateSubscriptionPoint(session, command);
        } catch (Exception e) {
            log.error("未知消息，导致websocket处理异常", e);
        }
    }

    /**
     * 发生错误时调用此方法
     *
     * @param session
     * @param error
     */
    public final void onError(Session session, Throwable error) {
        log.error("websocket连接发生错误", error);
        container.removeUserAndAllSubscriber(session);
        if (Objects.nonNull(session)) {
            try {
                session.close();
            } catch (IOException e) {
                log.error("session关闭异常", e);
            }
        }

    }
}
