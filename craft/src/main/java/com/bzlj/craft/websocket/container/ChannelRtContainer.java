package com.bzlj.craft.websocket.container;


import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.websocket.bean.Channel;
import com.bzlj.craft.websocket.bean.RealTimeRtData;
import com.bzlj.craft.websocket.bean.SubscriptionPoint;
import com.bzlj.craft.websocket.command.SubscriberCommand;
import com.bzlj.craft.websocket.pool.ThreadSendPool;
import com.bzlj.craft.websocket.pool.WebsocketPushRunnable;
import com.bzlj.craft.websocket.util.CodeHelper;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class ChannelRtContainer {
    /**
     * 用户连接map管理，key是sessionId
     */
    private static final ConcurrentHashMap<String, Channel> RT_SESSION_MAP = new ConcurrentHashMap<>();
    /**
     * 用户订阅得点位，key是dataCode,key是sessionId
     */
    private static final ConcurrentHashMap<String, Map<String, Session>> RT_SUBSCRIBER_MAP = new ConcurrentHashMap<>();
    private static ScheduledExecutorService timer;

    public ConcurrentHashMap<String, Channel> getSessionMap() {
        return RT_SESSION_MAP;
    }

    public ConcurrentHashMap<String, Map<String, Session>> getSubscriberMap() {
        return RT_SUBSCRIBER_MAP;
    }

    public int connectionCount() {
        return RT_SESSION_MAP.size();
    }

    public ChannelRtContainer() {
        init();
    }

    private void init() {
        timer = new ScheduledThreadPoolExecutor(1, new BasicThreadFactory.Builder().namingPattern("dataStatus-schedule-pool-%d").daemon(true).build());
        timer.scheduleAtFixedRate(this::push, 0, 1000, TimeUnit.MILLISECONDS);
        Runtime.getRuntime().addShutdownHook(new Thread(this::stop));
    }

    private void stop() {
        if (timer != null) {
            timer.shutdownNow();
            timer = null;
        }
    }

    /**
     * 定时推送空数据点数据，让x轴动起来
     */
    private void push() {
        RT_SESSION_MAP.forEach((k, v) -> v.getSubscriptionMap().forEach((k1, v1) -> {
            if (v1.incCount() > v1.getIntervalTime()) {
                RealTimeRtData realTimeRtData = new RealTimeRtData();
                realTimeRtData.setDc(v1.getDc());
                realTimeRtData.setV(null);
                realTimeRtData.setTs(System.currentTimeMillis());
                sendMessage(v.getSession(), realTimeRtData);
                v1.initCount();
            }
        }));
    }

    /**
     * 更新连接用户得订阅得点位
     *
     * @param session           连接对象
     * @param subscriberCommand 订阅参数
     */
    public void updateSubscriptionPoint(Session session, SubscriberCommand subscriberCommand) {
        if (Objects.nonNull(subscriberCommand) && Objects.nonNull(subscriberCommand.getSubscribe())
                && CollectionUtils.isNotEmpty(subscriberCommand.getDcList())) {
            Channel channel = RT_SESSION_MAP.get(session.getId());
            if (Objects.isNull(channel)) {
                return;
            }
            Map<String, SubscriptionPoint> subscriptionMap = channel.getSubscriptionMap();
            List<SubscriptionPoint> subscriptionPointList = subscriberCommand.getDcList();
            CodeHelper.trueOrFalse(subscriberCommand.getSubscribe()).trueOrFalseHandle(() -> {
                        //订阅
                        subscriptionPointList.forEach(subscriptionPoint -> {
                            //String pointKey = getSubPointKey(subscriptionPoint);
                            String dataCode = subscriptionPoint.getDc();
                            subscriptionMap.put(dataCode, subscriptionPoint);
                            Map<String, Session> sessionMap = RT_SUBSCRIBER_MAP.getOrDefault(dataCode, new ConcurrentHashMap<>());
                            sessionMap.put(session.getId(), session);
                            RT_SUBSCRIBER_MAP.put(dataCode, sessionMap);
                        });

                    },
                    //取消订阅
                    () -> subscriptionPointList.forEach(subscriptionPoint -> {
                        String dataCode = subscriptionPoint.getDc();
                        subscriptionMap.remove(dataCode);
                        Map<String, Session> sessionMap = RT_SUBSCRIBER_MAP.getOrDefault(dataCode, new ConcurrentHashMap<>());
                        sessionMap.remove(session.getId());
                        if (MapUtils.isEmpty(sessionMap)) {
                            RT_SUBSCRIBER_MAP.remove(dataCode);
                        }

                    })
            );
        }

    }

    public void removeUserAndAllSubscriber(Session session) {
        RT_SESSION_MAP.remove(session.getId());
        RT_SUBSCRIBER_MAP.forEach((k, v) -> {
            Map<String, Session> sessionMap = RT_SUBSCRIBER_MAP.getOrDefault(k, new ConcurrentHashMap<>());
            sessionMap.remove(session.getId());
            if (MapUtils.isEmpty(sessionMap)) {
                RT_SUBSCRIBER_MAP.remove(k);
            }
        });

    }

    public void publishMessageToSubscriber(RealTimeRtData data) {
        ThreadSendPool.getThreadPool().execute(new WebsocketPushRunnable(data, this));
    }

    public void sendMessage(Session session, RealTimeRtData baseData) {
        try {
            session.getBasicRemote().sendText(JsonUtils.toJson(baseData));
        } catch (IOException e) {
            log.error("消息推送异常", e);
        }
    }



    /**
     * 添加连接用户
     *
     * @param session 连接对象
     */
    public void addChannel(Session session) {
        Channel channel = new Channel();
        channel.setSession(session);
        RT_SESSION_MAP.put(session.getId(), channel);
    }


}
