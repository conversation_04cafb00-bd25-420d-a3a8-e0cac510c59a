package com.bzlj.craft.websocket.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-24 9:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionPoint {
    /**
     * 订阅点编码,可以是id业务编码等
     */
    private String dc;
    /**
     * 采集间隔
     */
    private int intervalTime;
    /**
     * 扩展字段
     */
    private Object ext;
    /**
     * 暂存一些参数
     */
    Map<String, Object> map = new HashMap<>();

    private AtomicInteger count = new AtomicInteger(0);

    public int incCount() {
        return count.getAndIncrement();
    }

    public void initCount() {
        count.set(0);
    }
}
