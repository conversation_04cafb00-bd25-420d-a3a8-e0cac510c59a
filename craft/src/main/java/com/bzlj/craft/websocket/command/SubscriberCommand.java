package com.bzlj.craft.websocket.command;

import com.bzlj.craft.websocket.bean.SubscriptionPoint;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-24 9:48
 */
@Data
public class SubscriberCommand {

    /**
     * true:需要这些数据点,false:不需要这些数据点
     */
    private Boolean subscribe;

    private String userId;

    /**
     * 订阅点集合
     */
    List<SubscriptionPoint> dcList;
}
