package com.bzlj.craft.websocket.bean;


import jakarta.websocket.Session;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;



@Data
public class Channel {

    /**
     * websocket session
     */
    private Session session;

    /**
     * 该用户订阅得点位
     */
    private List<SubscriptionPoint> subscriptionPointList;
    private Map<String, SubscriptionPoint> subscriptionMap = new ConcurrentHashMap<>();

}
