package com.bzlj.craft.controller;

import com.bzlj.base.controller.BaseController;
import com.bzlj.base.response.UnifyResponse;
import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.ComponentDTO;
import com.bzlj.craft.entity.Component;
import com.bzlj.craft.service.IComponentService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 组件相关接口
 * <AUTHOR>
 * @description: 组件
 * @date 2025-03-10 13:35
 */
@RestController
@RequestMapping("/component")
@RequiredArgsConstructor
public class ComponentController extends BaseController<Component, ComponentDTO, String> {
    @Autowired
    private IComponentService componentService;

    @Override
    public IBaseService<Component, ComponentDTO, String> getService() {
        return componentService;
    }

    /**
     * 根据组件id集合查询组件详情
     * @param componentIds
     * @return
     */
    @GetMapping(value = "/findComponentByComponentIds")
    public UnifyResponse<List<ComponentDTO>> findComponentByComponentIds(@RequestParam("componentIds") List<String> componentIds, @RequestParam(value = "groupCode",required = false) String groupCode) {
        List<ComponentDTO> components = componentService.findComponentByComponentIds(componentIds,groupCode);
        return UnifyResponse.success(components);
    }

}
