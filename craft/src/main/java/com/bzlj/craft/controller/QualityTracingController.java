package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.query.QualityTraceQuery;
import com.bzlj.craft.service.IQualityTracingService;
import com.bzlj.craft.vo.qualitytrace.QualityTraceVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 质量追溯
 *
 * <AUTHOR>
 * @date 2025/3/24 11:13
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@RestController
@RequestMapping("/qualityTracing")
@RequiredArgsConstructor
@Slf4j
public class QualityTracingController {
    private final IQualityTracingService qualityTracingService;

    /**
     * 初始化nebula数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    @GetMapping("/initNebulaData")
    public UnifyResponse<Boolean> initNebulaData() {
        return UnifyResponse.success(qualityTracingService.initNebulaData());
    }

    /**
     * 获取质量追溯信息
     *
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    @PostMapping("/getQualityTracing")
    public UnifyResponse<QualityTraceVO> getQualityTracing(@RequestBody @Validated QualityTraceQuery query) {
        return UnifyResponse.success(qualityTracingService.getQualityTracing(query));
    }

}
