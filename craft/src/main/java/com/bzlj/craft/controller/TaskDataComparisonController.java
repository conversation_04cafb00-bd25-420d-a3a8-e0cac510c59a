package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.ContinuousStepDTO;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.dto.TaskDetailDTO;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.service.ITaskDataComparisonService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务对比分析
 * <AUTHOR>
 * @description:
 * @date 2025-06-10 9:16
 */
@Slf4j
@RestController
@RequestMapping("/comparison")
@RequiredArgsConstructor
public class TaskDataComparisonController {

    @Autowired
    private ITaskDataComparisonService taskDataComparisonService;

    /**
     * 参数对比
     * @param taskIds
     * @return
     */
    @PostMapping(value = "/param")
    public UnifyResponse<List<TaskDetailDTO>> paramComparison(@RequestBody List<String> taskIds) {
        List<TaskDetailDTO> taskDetailDTOS = taskDataComparisonService.paramComparison(taskIds);
        return UnifyResponse.success(taskDetailDTOS);
    }

    /**
     *获取连续型工艺参数
     * @param taskId
     * @return
     */
    @GetMapping(value = "/continuous/param")
    public UnifyResponse<List<ContinuousStepDTO>> continuousParams(@RequestParam(value = "taskId") String taskId) {
        List<ContinuousStepDTO> continuousStepDTOS = taskDataComparisonService.continuousParams(taskId);
        return UnifyResponse.success(continuousStepDTOS);
    }

    /**
     * 获取连续型数据并进行对齐
     * 以baseTaskId的初始点位信息为原点，对其他任务的点位数据进行对齐
     *
     * @param request 连续数据查询请求
     * @return 对齐后的点位数据
     */
    @PostMapping(value = "/continuous/data/aligned")
    public UnifyResponse<Map<String, List<DataPointInfoDTO>>> getContinuousDataAligned(
            @RequestBody ContinuousDataRequest request) {
        log.info("获取对齐的连续数据，任务数量: {}, 基准任务: {}", request.getTaskIds().size(), request.getBaseTaskId());

        try {
            // 验证请求参数
            validateContinuousDataRequest(request);

            // 执行连续数据查询和对齐
            Map<String, List<DataPointInfoDTO>> alignedData = taskDataComparisonService.getContinuousData(
                    request.getCommand(),
                    request.getTaskIds(),
                    request.getBaseTaskId(),
                    request.getWorkStepName(),
                    request.getPointMethodType());

            log.info("连续数据对齐完成，返回任务数: {}", alignedData.size());
            return UnifyResponse.success(alignedData);

        } catch (Exception e) {
            log.error("获取对齐的连续数据失败", e);
            return UnifyResponse.failed("获取连续数据失败: " + e.getMessage());
        }
    }

    /**
     * 验证连续数据请求参数
     */
    private void validateContinuousDataRequest(ContinuousDataRequest request) {
        if (request.getTaskIds() == null || request.getTaskIds().isEmpty()) {
            throw new IllegalArgumentException("任务ID列表不能为空");
        }

        if (request.getBaseTaskId() == null || request.getBaseTaskId().trim().isEmpty()) {
            throw new IllegalArgumentException("基准任务ID不能为空");
        }

        if (!request.getTaskIds().contains(request.getBaseTaskId())) {
            throw new IllegalArgumentException("基准任务ID必须在任务ID列表中");
        }

        if (request.getTaskIds().size() > 6) {
            throw new IllegalArgumentException("单次查询任务数量不能超过6个");
        }

        if (request.getCommand() == null) {
            throw new IllegalArgumentException("查询命令不能为空");
        }

        if (request.getWorkStepName() == null) {
            throw new IllegalArgumentException("执行工步不能为空");
        }

        if (request.getPointMethodType() == null) {
            request.setPointMethodType(PointMethodType.batch_point); // 设置默认值
        }
    }


    /**
     * 连续数据查询请求类
     */
    @Data
    public static class ContinuousDataRequest {
        private DataPointSearchCommand command;
        private List<String> taskIds;
        private String baseTaskId;
        private PointMethodType pointMethodType;
        private String workStepName;
    }
}
