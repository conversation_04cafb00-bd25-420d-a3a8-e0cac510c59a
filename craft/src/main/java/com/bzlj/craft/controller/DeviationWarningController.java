package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.base.result.DataResult;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.enums.PageType;
import com.bzlj.craft.service.IDeviationWarningService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 偏离预警
 *
 * <AUTHOR>
 * @description: 偏离预警
 * @date 2025-03-10 13:35
 */
@RestController
@RequestMapping("/deviationWarning")
@RequiredArgsConstructor
public class DeviationWarningController {
    @Autowired
    private final IDeviationWarningService deviationWarningService;

    @PostMapping("/findAlarmList")
    public UnifyResponse<DataResult> findAlarmList(@RequestBody String json) {
        return UnifyResponse.success(deviationWarningService.findAlarmList(json));
    }

    @PostMapping("/findDeviationTracing")
    public UnifyResponse<DataResult> findDeviationTracing(@RequestBody String json) {
        return UnifyResponse.success(deviationWarningService.findDeviationTracing(json));
    }

    @GetMapping("/deviationTracingDetail")
    public UnifyResponse<Map<String, List<DataPointInfoDTO>>> deviationTracingDetail(@RequestParam("deviationTracingId") String deviationTracingId) {
        return UnifyResponse.success(deviationWarningService.deviationTracingDetail(deviationTracingId));
    }

    @GetMapping("/getDeviationTracingId")
    public UnifyResponse<String> getDeviationTracingId(@RequestParam("deviationTracingId") String deviationTracingId, @RequestParam("pageType") PageType pageType) {
        return UnifyResponse.success(deviationWarningService.getDeviationTracingId(deviationTracingId, pageType));
    }
}
