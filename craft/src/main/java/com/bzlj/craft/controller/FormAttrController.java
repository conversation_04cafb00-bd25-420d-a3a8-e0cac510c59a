package com.bzlj.craft.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.craft.dto.ImportResultDTO;
import com.bzlj.craft.service.IFormAttrImportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * FormAttr控制器
 * <AUTHOR>
 * @description: FormAttr相关接口
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/formAttr")
@Tag(name = "FormAttr管理", description = "表单属性管理相关接口")
@Slf4j
public class FormAttrController {
    
    @Autowired
    private IFormAttrImportService formAttrImportService;
    
    /**
     * 下载FormAttr导入模板
     */
    @GetMapping("/downloadTemplate")
    @Operation(summary = "下载FormAttr导入模板", description = "下载Excel格式的FormAttr导入模板")
    public ResponseEntity<byte[]> downloadTemplate() {
        try {
            byte[] template = formAttrImportService.generateTemplate();
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "FormAttr导入模板.xlsx");
            headers.setContentLength(template.length);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(template);
                    
        } catch (IOException e) {
            log.error("生成FormAttr导入模板失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 导入FormAttr数据
     */
    @PostMapping("/import")
    @Operation(summary = "导入FormAttr数据", description = "通过Excel文件批量导入FormAttr数据")
    public UnifyResponse<ImportResultDTO> importFormAttr(
            @Parameter(description = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            // 验证文件
            if (file.isEmpty()) {
                return UnifyResponse.failed("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return UnifyResponse.failed("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
            }
            
            // 执行导入
            ImportResultDTO result = formAttrImportService.importFormAttr(file);

            if (result.isAllSuccess()) {
                return UnifyResponse.success(result);
            } else {
                return UnifyResponse.success(result);
            }
            
        } catch (IOException e) {
            log.error("导入FormAttr数据失败", e);
            return UnifyResponse.failed("导入失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("导入FormAttr数据时发生未知错误", e);
            return UnifyResponse.failed("导入失败：系统内部错误");
        }
    }
}
