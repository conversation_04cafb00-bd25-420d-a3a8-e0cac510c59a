package com.bzlj.craft.service.impl;

import com.bzlj.craft.dto.FormAttrImportDTO;
import com.bzlj.craft.dto.ImportResultDTO;
import com.bzlj.craft.mongo.entity.FormAttr;
import com.bzlj.craft.mongo.repository.FormAttrRepository;
import com.bzlj.craft.service.IFormAttrImportService;
import com.bzlj.craft.util.ExcelTemplateUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * FormAttr导入服务实现
 * <AUTHOR>
 * @description: FormAttr Excel导入服务实现
 * @date 2025-07-25
 */
@Service
@Slf4j
public class FormAttrImportServiceImpl implements IFormAttrImportService {
    
    @Autowired
    private FormAttrRepository formAttrRepository;
    
    @Override
    public ImportResultDTO importFormAttr(MultipartFile file) throws IOException {
        List<ImportResultDTO.ErrorInfo> errors = new ArrayList<>();
        int totalCount = 0;
        int successCount = 0;
        int updateCount = 0;
        int insertCount = 0;
        
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            // 跳过标题行和说明行，从第3行开始读取数据（索引为2）
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }
                
                totalCount++;
                
                try {
                    FormAttrImportDTO importDTO = parseRowToDTO(row, i + 1);
                    
                    // 验证必填字段
                    List<ImportResultDTO.ErrorInfo> validationErrors = validateImportDTO(importDTO, i + 1);
                    if (!validationErrors.isEmpty()) {
                        errors.addAll(validationErrors);
                        continue;
                    }
                    
                    // 转换并保存
                    FormAttr formAttr = convertToEntity(importDTO);
                    
                    // 检查是否已存在（根据componentId和dataIndex）
                    Optional<FormAttr> existingOpt = formAttrRepository.findByComponentKeyOrderByOrder(formAttr.getComponentKey())
                            .stream()
                            .filter(attr -> attr.getDataIndex().equals(formAttr.getDataIndex()))
                            .findFirst();
                    
                    if (existingOpt.isPresent()) {
                        // 更新现有记录
                        FormAttr existing = existingOpt.get();
                        formAttr.setId(existing.getId());
                        updateCount++;
                    } else {
                        // 新增记录
                        insertCount++;
                    }
                    
                    formAttrRepository.save(formAttr);
                    successCount++;
                    
                } catch (Exception e) {
                    log.error("处理第{}行数据时发生错误: {}", i + 1, e.getMessage(), e);
                    errors.add(ImportResultDTO.ErrorInfo.builder()
                            .rowIndex(i + 1)
                            .field("整行")
                            .message("处理数据时发生错误: " + e.getMessage())
                            .build());
                }
            }
        }
        
        int failureCount = totalCount - successCount;
        
        return ImportResultDTO.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .failureCount(failureCount)
                .updateCount(updateCount)
                .insertCount(insertCount)
                .allSuccess(failureCount == 0)
                .errors(errors)
                .build();
    }
    
    @Override
    public byte[] generateTemplate() throws IOException {
        return ExcelTemplateUtil.generateFormAttrTemplate();
    }
    
    /**
     * 判断行是否为空
     */
    private boolean isEmptyRow(Row row) {
        for (int i = 0; i < 4; i++) { // 检查前4个必填列
            Cell cell = row.getCell(i);
            if (cell != null && StringUtils.isNotBlank(getCellValue(cell))) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 解析行数据为DTO
     */
    private FormAttrImportDTO parseRowToDTO(Row row, int rowIndex) {
        FormAttrImportDTO dto = new FormAttrImportDTO();
        
        dto.setComponentKey(getCellValue(row.getCell(0)));
        dto.setDataIndex(getCellValue(row.getCell(1)));
        dto.setLabel(getCellValue(row.getCell(2)));
        dto.setType(getCellValue(row.getCell(3)));
        dto.setSearchable(getCellValue(row.getCell(4)));
        
        // 处理数字类型的order字段
        String orderStr = getCellValue(row.getCell(5));
        if (StringUtils.isNotBlank(orderStr)) {
            try {
                dto.setOrder(Integer.parseInt(orderStr));
            } catch (NumberFormatException e) {
                dto.setOrder(0);
            }
        } else {
            dto.setOrder(0);
        }
        
        dto.setDisplay(getCellValue(row.getCell(6)));
        dto.setRange(getCellValue(row.getCell(7)));
        dto.setDictCode(getCellValue(row.getCell(8)));
        dto.setFormat(getCellValue(row.getCell(9)));
        dto.setAlias(getCellValue(row.getCell(10)));
        dto.setMultiple(getCellValue(row.getCell(11)));
        dto.setInitialRequest(getCellValue(row.getCell(12)));
        dto.setDataSourceType(getCellValue(row.getCell(13)));
        dto.setApiUrl(getCellValue(row.getCell(14)));
        dto.setHttpMethod(getCellValue(row.getCell(15)));
        dto.setApiParams(getCellValue(row.getCell(16)));
        dto.setValueKey(getCellValue(row.getCell(17)));
        dto.setLabelKey(getCellValue(row.getCell(18)));
        
        return dto;
    }
    
    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().trim();
            case NUMERIC -> String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> cell.getCellFormula();
            default -> "";
        };
    }

    /**
     * 验证导入DTO
     */
    private List<ImportResultDTO.ErrorInfo> validateImportDTO(FormAttrImportDTO dto, int rowIndex) {
        List<ImportResultDTO.ErrorInfo> errors = new ArrayList<>();

        // 验证必填字段
        if (StringUtils.isBlank(dto.getComponentKey())) {
            errors.add(ImportResultDTO.ErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .field("组件key")
                    .message("组件Introduce local variable不能为空")
                    .originalData(dto.getComponentKey())
                    .build());
        }

        if (StringUtils.isBlank(dto.getDataIndex())) {
            errors.add(ImportResultDTO.ErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .field("属性名称")
                    .message("属性名称不能为空")
                    .originalData(dto.getDataIndex())
                    .build());
        }

        if (StringUtils.isBlank(dto.getLabel())) {
            errors.add(ImportResultDTO.ErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .field("属性标签")
                    .message("属性标签不能为空")
                    .originalData(dto.getLabel())
                    .build());
        }

        if (StringUtils.isBlank(dto.getType())) {
            errors.add(ImportResultDTO.ErrorInfo.builder()
                    .rowIndex(rowIndex)
                    .field("字段类型")
                    .message("字段类型不能为空")
                    .originalData(dto.getType())
                    .build());
        } else {
            // 验证字段类型是否有效
            try {
                dto.getTypeEnum();
            } catch (Exception e) {
                errors.add(ImportResultDTO.ErrorInfo.builder()
                        .rowIndex(rowIndex)
                        .field("字段类型")
                        .message("字段类型无效，支持的类型：string/number/enum/datetime/bool/obj")
                        .originalData(dto.getType())
                        .build());
            }
        }

        // 验证数据源类型
        if (StringUtils.isNotBlank(dto.getDataSourceType())) {
            try {
                dto.getDataSourceTypeEnum();
            } catch (Exception e) {
                errors.add(ImportResultDTO.ErrorInfo.builder()
                        .rowIndex(rowIndex)
                        .field("数据源类型")
                        .message("数据源类型无效，支持的类型：STATIC/DICT/API")
                        .originalData(dto.getDataSourceType())
                        .build());
            }
        }

        // 验证HTTP方法
        if (StringUtils.isNotBlank(dto.getHttpMethod())) {
            try {
                dto.getHttpMethodEnum();
            } catch (Exception e) {
                errors.add(ImportResultDTO.ErrorInfo.builder()
                        .rowIndex(rowIndex)
                        .field("HTTP方法")
                        .message("HTTP方法无效，支持的方法：GET/POST/PUT/DELETE")
                        .originalData(dto.getHttpMethod())
                        .build());
            }
        }

        return errors;
    }

    /**
     * 转换DTO为实体
     */
    private FormAttr convertToEntity(FormAttrImportDTO dto) {
        FormAttr formAttr = new FormAttr();

        formAttr.setComponentKey(dto.getComponentKey());
        formAttr.setDataIndex(dto.getDataIndex());
        formAttr.setLabel(dto.getLabel());
        formAttr.setType(dto.getTypeEnum());
        formAttr.setSearchable(dto.getBooleanValue(dto.getSearchable()));
        formAttr.setOrder(dto.getOrder() != null ? dto.getOrder() : 0);
        formAttr.setDisplay(dto.getBooleanValue(dto.getDisplay()));
        formAttr.setRange(dto.getBooleanValue(dto.getRange()));
        formAttr.setDictCode(StringUtils.isNotBlank(dto.getDictCode()) ? dto.getDictCode() : null);
        formAttr.setFormat(StringUtils.isNotBlank(dto.getFormat()) ? dto.getFormat() : null);
        formAttr.setAlias(StringUtils.isNotBlank(dto.getAlias()) ? dto.getAlias() : null);
        formAttr.setMultiple(StringUtils.isNotBlank(dto.getMultiple()) ? dto.getBooleanValue(dto.getMultiple()) : null);
        formAttr.setInitialRequest(StringUtils.isNotBlank(dto.getInitialRequest()) ? dto.getBooleanValue(dto.getInitialRequest()) : null);
        formAttr.setDataSourceType(dto.getDataSourceTypeEnum());

        // 处理数据源
        if (dto.getDataSourceTypeEnum() == com.bzlj.base.enums.DataSourceType.API &&
            StringUtils.isNotBlank(dto.getApiUrl())) {

            // 解析API参数
            Map<String, Object> paramsMap = new HashMap<>();
            if (StringUtils.isNotBlank(dto.getApiParams())) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    paramsMap = objectMapper.readValue(dto.getApiParams(), new TypeReference<Map<String, Object>>() {});
                } catch (Exception e) {
                    log.warn("解析API参数失败: {}", dto.getApiParams(), e);
                    // 如果解析失败，将原始字符串作为params参数
                    Map<String, String> defaultParams = new HashMap<>();
                    defaultParams.put("params", dto.getApiParams());
                    paramsMap.put("params", defaultParams);
                }
            }

            FormAttr.DataSource dataSource = FormAttr.DataSource.builder()
                    .api(FormAttr.DataSource.Api.builder()
                            .url(dto.getApiUrl())
                            .method(dto.getHttpMethodEnum())
                            .params(paramsMap)
                            .build())
                    .mapping(FormAttr.DataSource.Mapping.builder()
                            .valueKey(StringUtils.isNotBlank(dto.getValueKey()) ? dto.getValueKey() : null)
                            .labelKey(StringUtils.isNotBlank(dto.getLabelKey()) ? dto.getLabelKey() : null)
                            .build())
                    .build();

            formAttr.setDataSource(dataSource);
        }

        return formAttr;
    }
}
