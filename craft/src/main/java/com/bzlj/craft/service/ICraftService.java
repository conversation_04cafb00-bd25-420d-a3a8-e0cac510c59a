package com.bzlj.craft.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.CraftProcessDTO;
import com.bzlj.craft.dto.PlantDTO;
import com.bzlj.craft.dto.ProcessStepDTO;
import com.bzlj.craft.dto.StepParameterDTO;
import com.bzlj.craft.entity.CraftProcess;
import com.bzlj.craft.entity.WorkStep;

import java.util.List;

/**
 * 工艺接口
 * <AUTHOR>
 * @description:
 * @date 2025-04-21 14:39
 */
public interface ICraftService  extends IBaseService<CraftProcess, CraftProcessDTO,String> {

    /**
     * 查询分厂信息
     * @return
     */
    List<PlantDTO> findPlantList();

    /**
     * 根据分厂查询工序
     * @param plantCode
     * @return
     */
    List<CraftProcessDTO> findProcessByPlantCode(String plantCode);

    /**
     * 根据工序查询工步
     * @param processId
     * @return
     */
    List<ProcessStepDTO> findProcessStepByProcessId(String processId);

    /**
     * 根据工步查询工艺参数
     * @param stepId
     * @return
     */
    List<StepParameterDTO> findStepParameterByStepId(String stepId);

    /**
     * 根据任务更新执行工步的开始结束时间
     * @param taskId
     * @return
     */
    List<WorkStep> updateStepExecTime(String taskId);
}
