package com.bzlj.craft.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.SysDictItemDTO;
import com.bzlj.craft.entity.SysDictItem;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface ISysDictItemService extends IBaseService<SysDictItem, SysDictItemDTO, String> {


    List<SysDictItemDTO> findByDictCode(String dictCode);

    List<SysDictItem> findEntityByDictCode(String dictCode);

    List<String> findItemCodesByDictCode(String dictCode);
}
