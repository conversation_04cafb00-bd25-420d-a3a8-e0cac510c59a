package com.bzlj.craft.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.search.SortItem;
import com.bzlj.craft.dto.CraftProcessDTO;
import com.bzlj.craft.dto.PlantDTO;
import com.bzlj.craft.dto.ProcessStepDTO;
import com.bzlj.craft.dto.StepParameterDTO;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.service.ICraftService;
import com.bzlj.craft.service.ISurveillanceService;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 工序评分
 * @date 2025-03-10 13:38
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CraftServiceImpl implements ICraftService {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public BaseRepository<CraftProcess, String> getRepository() {
        return craftProcessRepository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<CraftProcessDTO> getDTOClass() {
        return CraftProcessDTO.class;
    }

    @Override
    public Class<CraftProcess> getPOClass() {
        return CraftProcess.class;
    }

    @Autowired
    private PlantRepository plantRepository;

    @Autowired
    private CraftProcessRepository craftProcessRepository;

    @Autowired
    private ProcessStepRepository processStepRepository;

    @Autowired
    private StepParameterRepository stepParameterRepository;

    @Autowired
    private ISurveillanceService surveillanceService;

    @Autowired
    private WorkStepRepository workStepRepository;

    @Override
    public List<PlantDTO> findPlantList() {
        List<Plant> plants = plantRepository.findAll(Sort.by(Sort.Order.desc("updatedAt")));
        if (!CollectionUtils.isEmpty(plants)) {
            return plants.stream().map(plant -> {
                PlantDTO plantDTO = new PlantDTO();
                BeanUtils.copyProperties(plant, plantDTO);
                return plantDTO;
            }).toList();
        }
        return List.of();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CraftProcessDTO> findProcessByPlantCode(String plantCode) {
        SearchCondition searchCondition = new SearchCondition();
        if(StringUtils.isNotBlank(plantCode)){
            searchCondition.setSearchItems(SearchItems.builder()
                    .item(new SearchItem("plant.plantCode", plantCode, null, SearchItem.Operator.EQ))
                    .build());
        }
        searchCondition.setSortItems(Lists.newArrayList(
                SortItem.builder().fieldName("processOrder").sortOrder(SortItem.ASC_ORDER_NAME).build()
        ));
        return this.findAllWithCondition(searchCondition);
    }

    @Override
    public List<ProcessStepDTO> findProcessStepByProcessId(String processId) {
        List<ProcessStep> processSteps = processStepRepository.findByProcessIdOrderByStepOrderAsc(processId);
        if (!CollectionUtils.isEmpty(processSteps)) {
            return processSteps.stream().map(processStep -> {
                ProcessStepDTO processStepDTO = new ProcessStepDTO();
                BeanUtils.copyProperties(processStep, processStepDTO);
                return processStepDTO;
            }).toList();
        }
        return List.of();
    }

    @Override
    public List<StepParameterDTO> findStepParameterByStepId(String stepId) {
        List<StepParameter> stepParameters = stepParameterRepository.findByStepIdOrderByCreatedTime(stepId);
        if (!CollectionUtils.isEmpty(stepParameters)) {
            return stepParameters.stream().map(stepParameter -> {
                StepParameterDTO stepParameterDTO = new StepParameterDTO();
                BeanUtils.copyProperties(stepParameter, stepParameterDTO);
                return stepParameterDTO;
            }).toList();
        }
        return List.of();
    }

    @Override
    public List<WorkStep> updateStepExecTime(String taskId) {
        SearchCondition searchCondition = new SearchCondition();
        searchCondition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskId", taskId, null, SearchItem.Operator.EQ))
                .build());
        ProductionTask productionTask = surveillanceService.findOne(searchCondition);
        if (Objects.isNull(productionTask)) {
            throw new RuntimeException("任务不存在");
        }
        List<WorkStep> workSteps = workStepRepository.findByTaskTaskId(taskId);
        workSteps.forEach(workStep -> {
            workStep.setEndTime(productionTask.getEndTime());
            workStep.setStartTime(productionTask.getStartTime());
        });
        return workStepRepository.saveAll(workSteps);
    }

}



