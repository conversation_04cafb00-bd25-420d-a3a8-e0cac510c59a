package com.bzlj.craft.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.dto.SysDictItemDTO;
import com.bzlj.craft.entity.SysDictItem;
import com.bzlj.craft.repository.SysDictItemRepository;
import com.bzlj.craft.service.ISysDictItemService;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SysDictItemServiceImpl implements ISysDictItemService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private SysDictItemRepository sysDictItemRepository;

    @Override
    public BaseRepository<SysDictItem, String> getRepository() {
        return sysDictItemRepository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<SysDictItemDTO> getDTOClass() {
        return SysDictItemDTO.class;
    }

    @Override
    public Class<SysDictItem> getPOClass() {
        return SysDictItem.class;
    }

    @Override
    public List<SysDictItemDTO> findByDictCode(String dictCode) {
        List<SysDictItem> dictItems = findEntityByDictCode(dictCode);
        if(CollectionUtils.isEmpty(dictItems)) return Lists.newArrayList();
        return convertToDtoList(dictItems, SysDictItemDTO.class);
    }

    @Override
    public List<SysDictItem> findEntityByDictCode(String dictCode) {
        SearchCondition condition = new SearchCondition();
        SearchItems searchItems = SearchItems.builder()
                .item(new SearchItem("dictCode.dictCode", dictCode, null, SearchItem.Operator.EQ))
                .build();
        condition.setSearchItems(searchItems);
        return this.findEntityWithCondition(condition);
    }

    @Override
    public List<String> findItemCodesByDictCode(String dictCode) {
        List<SysDictItemDTO> dictItems = findByDictCode(dictCode);
        if(!CollectionUtils.isEmpty(dictItems)) {
            return dictItems.stream().map(SysDictItemDTO::getItemCode).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
