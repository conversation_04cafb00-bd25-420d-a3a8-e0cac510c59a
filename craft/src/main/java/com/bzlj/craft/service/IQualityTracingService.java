package com.bzlj.craft.service;

import com.bzlj.craft.query.QualityTraceQuery;
import com.bzlj.craft.vo.qualitytrace.QualityTraceVO;

/**
 * 质量追溯接口
 *
 * <AUTHOR>
 * @date 2025/3/24 11:15
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public interface IQualityTracingService {
    /**
     * 初始化nebula数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    Boolean initNebulaData();

    /**
     * 获取质量追溯信息
     *
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    QualityTraceVO getQualityTracing(QualityTraceQuery query);
}
