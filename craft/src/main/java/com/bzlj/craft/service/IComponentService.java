package com.bzlj.craft.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.ComponentDTO;
import com.bzlj.craft.entity.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IComponentService extends IBaseService<Component, ComponentDTO, String> {

    List<ComponentDTO> findComponentByComponentIds(List<String> componentIds,String groupCode);
}
