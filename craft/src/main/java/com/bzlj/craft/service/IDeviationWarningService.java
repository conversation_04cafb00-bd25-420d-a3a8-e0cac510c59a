package com.bzlj.craft.service;


import com.bzlj.base.result.DataResult;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.enums.PageType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 偏离预警
 * @date 2025-03-10 13:38
 */
public interface IDeviationWarningService {
    /**
     * 偏离预警列表
     * @param json
     * @return
     */
    DataResult findAlarmList(String json);

    DataResult findDeviationTracing(String json);

    String getDeviationTracingId(String id, PageType pageType);

    Map<String, List<DataPointInfoDTO>> deviationTracingDetail(String deviationTracingId);
}
