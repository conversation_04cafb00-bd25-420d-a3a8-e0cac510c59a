package com.bzlj.craft.service;

import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.ContinuousStepDTO;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.dto.TaskDetailDTO;
import com.bzlj.craft.enums.PointMethodType;

import java.util.List;
import java.util.Map;

/**
 * 任务对比
 * <AUTHOR>
 * @description:
 * @date 2025-06-10 14:04
 */
public interface ITaskDataComparisonService {
    /**
     * 参数对比
     * @param taskIds
     * @return
     */
    List<TaskDetailDTO> paramComparison(List<String> taskIds);

    /**
     * 获取连续型工艺参数
     * @param taskId
     * @return
     */
    List<ContinuousStepDTO> continuousParams(String taskId);

    /**
     * 获取连续型数据，以baseTaskId的初始点位信息为原点进行对齐
     * @param command 查询命令
     * @param taskIds 任务ID列表
     * @param baseTaskId 基准任务ID
     * @param pointMethodType 点位方法类型
     * @return 对齐后的点位数据，key为任务ID，value为对齐后的点位数据列表
     */
    Map<String, List<DataPointInfoDTO>> getContinuousData(DataPointSearchCommand command, List<String> taskIds, String baseTaskId,String workStepName, PointMethodType pointMethodType);

}
