package com.bzlj.craft.service.impl;

import com.bzlj.base.result.DataResult;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.search.SortItem;
import com.bzlj.base.util.DTOConverter;
import com.bzlj.craft.api.service.IDataPointInfoService;
import com.bzlj.craft.command.DataPointSearchCommand;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.dto.DeviationWarningDTO;
import com.bzlj.craft.entity.DataPointInfo;
import com.bzlj.craft.enums.LimitType;
import com.bzlj.craft.enums.PageType;
import com.bzlj.craft.enums.PointMethodType;
import com.bzlj.craft.enums.PointType;
import com.bzlj.craft.mongo.entity.AbnormalRecord;
import com.bzlj.craft.mongo.entity.DeviationTracing;
import com.bzlj.craft.service.IDeviationWarningService;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.vo.surveillance.DeviationTracingVO;
import com.bzlj.craft.vo.surveillance.DeviationWarningVO;
import com.bzlj.dynamic.mongo.template.DynamicMongoTemplate;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.CriteriaDefinition;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.bzlj.craft.enums.AlertType.manual;
import static com.bzlj.craft.enums.AlertType.system;

/**
 * <AUTHOR>
 * @description: 偏离预警
 * @date 2025-03-10 13:38
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DeviationWarningServiceImpl implements IDeviationWarningService {
    private final DynamicMongoTemplate dynamicMongoTemplate;

    private final IDataPointInfoService dataPointInfoService;

    private final ISurveillanceService surveillanceService;

    private final Executor virtualThreadExecutor;

    public MongoResult findBySearchCondition(SearchCondition searchCondition, Class entityClass) {
        Query query = new Query();
        SearchItems searchItems = searchCondition.getSearchItems();
        List<SortItem> sortItems = searchCondition.getSortItems();
        // 分页,PageNumber()-1是因为第一页的下标为0 ，入参PageNumber最小值为1
        Pageable pageRequest = PageRequest.of(searchCondition.getPageCurrent() - 1, searchCondition.getPageSize());

        if (!CollectionUtils.isEmpty(sortItems)) {
            SortItem sortItem = sortItems.getFirst();
            Sort sort = Sort.by(SortItem.ASC_ORDER_NAME.equals(sortItem.getSortOrder()) ? Sort.Direction.ASC : Sort.Direction.DESC, sortItem.getFieldName());
            pageRequest = PageRequest.of(searchCondition.getPageCurrent() - 1, searchCondition.getPageSize(), sort);
        }

        if (Objects.nonNull(searchItems)) {
            for (SearchItem item : searchItems.getItems()) {
                analysisSearchItemValue(item, entityClass);
                query.addCriteria(buildCriteria(item));
            }
        }
        long count = dynamicMongoTemplate.count(query, entityClass);
        query.with(pageRequest);
        List records = dynamicMongoTemplate.find(query, entityClass);
        return new MongoResult(records, count);
    }

    @Data
    @AllArgsConstructor
    public static class MongoResult {
        private List result;

        private Long count = 0L;
    }

    @Override
    public DataResult findAlarmList(String json) {
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        MongoResult records = this.findBySearchCondition(searchCondition, AbnormalRecord.class);

        DataResult dataResult = new DataResult();
        dataResult.setTotal(records.getCount());
        dataResult.setPageSize(searchCondition.getPageSize());
        dataResult.setPageCurrent(searchCondition.getPageCurrent());
        if (CollectionUtils.isEmpty(records.getResult())) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        List<DeviationWarningDTO> deviationWarningDTOS = convertToDtoList(records.getResult());
        List<List<Object>> value = DTOConverter.convert(deviationWarningDTOS, DeviationWarningVO.class);
        dataResult.setList(value);
        return dataResult;
    }


    @Override
    public DataResult findDeviationTracing(String json) {
        SearchCondition searchCondition = JsonUtils.fromJson(json, SearchCondition.class);
        MongoResult records = this.findBySearchCondition(searchCondition, DeviationTracing.class);
        DataResult dataResult = new DataResult();
        dataResult.setTotal(records.getCount());
        dataResult.setPageSize(searchCondition.getPageSize());
        dataResult.setPageCurrent(searchCondition.getPageCurrent());
        if (CollectionUtils.isEmpty(records.getResult())) {
            dataResult.setList(Lists.newArrayList());
            return dataResult;
        }
        List<List<Object>> value = DTOConverter.convert(records.getResult(), DeviationTracingVO.class);
        dataResult.setList(value);
        return dataResult;
    }

    @Override
    public Map<String, List<DataPointInfoDTO>> deviationTracingDetail(String deviationTracingId) {
        DeviationTracing tracing = dynamicMongoTemplate.findById(deviationTracingId, DeviationTracing.class);
        if (Objects.isNull(tracing)) return Map.of();
        List<DataPointInfo> dataPointInfos = dataPointInfoService.findByParameterIdAndEquipmentId(tracing.getParameterId(), tracing.getEquipmentId());
        if (CollectionUtils.isEmpty(dataPointInfos)) {
            throw new RuntimeException("缺少点位信息");
        }
        Map<String, List<DataPointInfo>> map =
                dataPointInfos.stream().collect(Collectors.groupingBy(dataPointInfo -> dataPointInfo.getPointType().getItemCode()));
        List<CompletableFuture<Map<String, List<DataPointInfoDTO>>>> futures = map.keySet().stream()
                .map(pointType -> CompletableFuture.supplyAsync(() -> {
                    try {
                        DataPointInfo dataPointInfo = map.get(pointType).stream().findFirst().get();
                        return getTracingData(pointType, tracing, dataPointInfo);
                    } catch (Exception e) {
                        return null;
                    }
                }, virtualThreadExecutor))
                .collect(Collectors.toList());
        // 等待所有查询完成并收集结果
        Map<String, List<DataPointInfoDTO>> result = futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .flatMap(dataMap -> dataMap.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.flatMapping(
                                entry -> entry.getValue().stream(),
                                Collectors.toList()
                        )
                ));
        List<DataPointInfoDTO> dataPointInfoDTOS = result.get(PointType.standard_point.getCode());
        if (CollectionUtils.isEmpty(dataPointInfoDTOS)) return result;
        result.put(LimitType.upper.getCode(),getLimitsData(dataPointInfoDTOS, LimitType.upper));
        result.put(LimitType.lower.getCode(),getLimitsData(dataPointInfoDTOS, LimitType.lower));
        return result;
    }

    private List<DataPointInfoDTO> getLimitsData(List<DataPointInfoDTO> dataPointInfoDTOS, LimitType limitType) {
        BigDecimal multiple = switch (limitType) {
            case upper -> BigDecimal.valueOf(5);
            case lower -> BigDecimal.valueOf(-5);
            default -> BigDecimal.valueOf(1);
        };
        return dataPointInfoDTOS.stream()
                .map(dataPointInfoDTO -> {
                    DataPointInfoDTO pointInfo = new DataPointInfoDTO();
                    BeanUtils.copyProperties(dataPointInfoDTO, pointInfo);
                    if(Objects.nonNull(dataPointInfoDTO.getV())){
                        pointInfo.setV(dataPointInfoDTO.getV().add(multiple));
                    }
                    return pointInfo;
                })
                .toList();
    }

    private Map<String, List<DataPointInfoDTO>> getTracingData(String pointType, DeviationTracing tracing, DataPointInfo dataPointInfo) {
        Long startTime = tracing.getStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTime = tracing.getEndTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        DataPointSearchCommand command = new DataPointSearchCommand(dataPointInfo.getDataPoint(), false, "MEAN", tracing.getParameterName(), startTime, endTime, "1", "Seconds");

        Map<String, Object> result = (Map<String, Object>) surveillanceService.fetchPointData(Lists.newArrayList(command), PointMethodType.batch_concurrent_point);
        if (result != null && result.containsKey("data")) {
            Map<String, List<LinkedHashMap<String, Object>>> pointDataMap = (Map<String, List<LinkedHashMap<String, Object>>>) result.get("data");
            List<LinkedHashMap<String, Object>> linkedHashMaps = pointDataMap.values().stream().findFirst().get();
            return Map.of(pointType, JsonUtils.fromJson(JsonUtils.toJson(linkedHashMaps), new TypeReference<List<DataPointInfoDTO>>() {
            }));
        } else {
            return Map.of(pointType, Lists.newArrayList());
        }
    }

    private void analysisSearchItemValue(SearchItem item, Class clazz) {
        try {
            Field field = clazz.getDeclaredField(item.fieldName);
            Class<?> fieldType = field.getType();
            switch (fieldType.getSimpleName()) {
                case "LocalDateTime" -> {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    item.value = LocalDateTime.parse((String) item.value, formatter);
                    item.value1 = LocalDateTime.parse((String) item.value1, formatter);
                }
                default -> {
                }
            }
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将异常记录转换为偏差警告DTO
     * 此方法的作用是将数据库中的异常记录转换为偏差警告的DTO（数据传输对象），
     * 以便于在系统中传递或展示相关信息
     *
     * @param record 异常记录对象，包含异常记录的详细信息
     * @return DeviationWarningDTO 偏差警告DTO对象，封装了转换后的异常记录信息
     */
    private DeviationWarningDTO convertToDto(AbnormalRecord record) {
        DeviationWarningDTO deviationWarningDTO = new DeviationWarningDTO();
        deviationWarningDTO.setId(record.getId());
        deviationWarningDTO.setAlertTime(record.getAlertTime());
        deviationWarningDTO.setAlertType(BooleanUtils.isTrue(record.getAlertType()) ? manual.getMsg() : system.getMsg());
        deviationWarningDTO.setTaskId(record.getTaskId());
        deviationWarningDTO.setTaskCode(record.getTaskCode());
        deviationWarningDTO.setProcessType(record.getProcessType());
        deviationWarningDTO.setWorkStepName(record.getAlarmContent().getWorkStepName());
        deviationWarningDTO.setAlarmContent(String.format("“%s”%s", record.getAlarmContent().getParameterName(), record.getAlarmContent().getAlarmContent()));
        deviationWarningDTO.setSuggestion(record.getAlarmContent().getSuggestion());
        return deviationWarningDTO;
    }

    private List<DeviationWarningDTO> convertToDtoList(List<AbnormalRecord> entities) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyList();
        }
        return entities.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据SearchItem对象构建查询条件
     * 此方法解释了如何根据搜索项中的不同操作符来构建复杂的查询条件
     * 它使用了switch语句来处理不同的操作符，并根据操作符的类型应用相应的查询条件
     *
     * @param item 搜索项，包含字段名、操作符和值等信息
     * @return CriteriaDefinition 查询条件对象，根据搜索项构建的查询条件
     */
    private CriteriaDefinition buildCriteria(SearchItem item) {
        // 初始化查询条件，指定字段名
        Criteria where = Criteria.where(item.fieldName);
        // 根据搜索项的操作符类型，应用相应的查询条件
        switch (item.operator) {
            case EQ:
                // 等于条件
                where.is(item.value);
                break;
            case NEQ:
                // 不等于条件
                where.ne(item.value);
                break;
            case LIKE:
                // 模糊查询条件，item.value应为字符串类型
                where.regex((String) item.value);
                break;
            case GT:
                // 大于条件
                where.gt(item.value);
                break;
            case LT:
                // 小于条件
                where.lt(item.value);
                break;
            case GTE:
                // 大于等于条件
                where.gte(item.value);
                break;
            case LTE:
                // 小于等于条件
                where.lte(item.value);
                break;
            case BTWN:
                // 介于条件，需要两个值，item.value和item.value1
                where.gte(item.value).lte(item.value1);
                break;
            case IN:
                // 在...之中条件
                where.in((List)item.value);
                break;
            case ISNULL:
                // 是空条件
                where.isNull();
                break;
            case NOTNULL:
                // 不为空条件
                where.not().isNull();
                break;
            case NOTIN:
                // 不在...之中条件
                where.not().in((List)item.value);
                break;
            default:
                // 默认情况下不做任何操作
                break;
        }
        // 返回构建完成的查询条件对象
        return where;
    }

    @Override
    public String getDeviationTracingId(String id, PageType pageType) {
        DeviationTracing tracing = dynamicMongoTemplate.findById(id, DeviationTracing.class);
        Query query = new Query();
        query.addCriteria(Criteria.where("taskId").is(tracing.getTaskId())
                .and("equipmentId").is(tracing.getEquipmentId())
                .and("stepId").is(tracing.getStepId())
                .and("parameterId").is(tracing.getParameterId())
        );
        switch (pageType){
            case previous:
                query.addCriteria(Criteria.where("startTime").lt(tracing.getStartTime()));
                query.with(Sort.by(Sort.Direction.DESC, "startTime"));
                break;
            case next:
                query.addCriteria(Criteria.where("startTime").gt(tracing.getStartTime()));
                query.with(Sort.by(Sort.Direction.ASC, "startTime"));
                break;
        }
        DeviationTracing deviationTracing = dynamicMongoTemplate.findOne(query, DeviationTracing.class);
        if(Objects.nonNull(deviationTracing)) return deviationTracing.getId();
        return null;
    }
}
