package com.bzlj.craft.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.component.def.FormAttrDTO;
import com.bzlj.craft.component.service.ComponentDealService;
import com.bzlj.craft.dto.ComponentDTO;
import com.bzlj.craft.entity.Component;
import com.bzlj.craft.repository.ComponentRepository;
import com.bzlj.craft.service.IComponentService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ComponentServiceImpl implements IComponentService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ComponentRepository componentRepository;

    @Autowired
    private ComponentDealService componentDealService;

    @Override
    public BaseRepository<Component, String> getRepository() {
        return componentRepository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ComponentDTO> getDTOClass() {
        return ComponentDTO.class;
    }

    @Override
    public Class<Component> getPOClass() {
        return Component.class;
    }

    @Override
    public List<ComponentDTO> findComponentByComponentIds(List<String> componentIds,String groupCode) {
        List<ComponentDTO> components = findByComponentIds(componentIds);

        // 根据groupCode进行过滤和分组逻辑
        List<ComponentDTO> filteredComponents = filterComponentsByGroupCode(components, groupCode);

        filteredComponents.forEach(component -> {
            List<FormAttrDTO> formAttrDTOS = componentDealService.modelConvertFormAttr(component.getModel(),component.getComponentKey());
            component.setFormAttrs(formAttrDTOS);
        });
        return filteredComponents;
    }

    private List<ComponentDTO> findByComponentIds(List<String> componentIds) {
        List<Component> components = componentRepository.findAllByComponentIdIn(componentIds);
        return convertToDtoList(components, ComponentDTO.class);
    }

    /**
     * 根据groupCode过滤组件
     * 如果groupCode为空，过滤掉所有groupCode不为空的数据
     * 如果groupCode不为空，按ComponentId分组，每个分组中有与传入groupCode相同的数据则取相同的，若没有则取groupCode为空的数据
     */
    private List<ComponentDTO> filterComponentsByGroupCode(List<ComponentDTO> components, String groupCode) {
        if (components == null || components.isEmpty()) {
            return components;
        }

        if (groupCode == null || groupCode.trim().isEmpty()) {
            // 如果groupCode为空，过滤掉所有groupCode不为空的数据
            return components.stream()
                    .filter(component -> component.getGroupCode() == null || component.getGroupCode().trim().isEmpty())
                    .collect(Collectors.toList());
        } else {
            // 如果groupCode不为空，按ComponentId分组
            Map<String, List<ComponentDTO>> groupedByKey = components.stream()
                    .collect(Collectors.groupingBy(ComponentDTO::getComponentId));

            List<ComponentDTO> result = new ArrayList<>();

            for (List<ComponentDTO> group : groupedByKey.values()) {
                // 在每个分组中，优先选择匹配groupCode的数据
                ComponentDTO matchingComponent = group.stream()
                        .filter(component -> component.getGroupCode() != null &&
                                Arrays.asList(component.getGroupCode().split(",")).contains(groupCode))
                        .findFirst()
                        .orElse(null);

                if (matchingComponent != null) {
                    // 找到匹配的groupCode，使用它
                    result.add(matchingComponent);
                } else {
                    // 没有找到匹配的groupCode，选择groupCode为空的数据
                    ComponentDTO emptyGroupCodeComponent = group.stream()
                            .filter(component -> component.getGroupCode() == null || component.getGroupCode().trim().isEmpty())
                            .findFirst()
                            .orElse(null);

                    if (emptyGroupCodeComponent != null) {
                        result.add(emptyGroupCodeComponent);
                    }
                }
            }

            return result;
        }
    }
}
