package com.bzlj.craft.service;

import com.bzlj.craft.dto.ImportResultDTO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * FormAttr导入服务接口
 * <AUTHOR>
 * @description: FormAttr Excel导入服务
 * @date 2025-07-25
 */
public interface IFormAttrImportService {
    
    /**
     * 导入FormAttr数据
     * @param file Excel文件
     * @return 导入结果
     * @throws IOException IO异常
     */
    ImportResultDTO importFormAttr(MultipartFile file) throws IOException;
    
    /**
     * 生成导入模板
     * @return Excel模板字节数组
     * @throws IOException IO异常
     */
    byte[] generateTemplate() throws IOException;
}
