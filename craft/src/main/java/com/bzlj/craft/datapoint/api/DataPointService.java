package com.bzlj.craft.datapoint.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-21 16:01
 */
@FeignClient(path = "/data-server/service",url = "${dataPoint.provider.url}",name = "${dataPoint.provider.name}")
public interface DataPointService {

    @PostMapping(value = "/batch/findHistory", consumes = "application/json")
    Object batchFindHistory(@RequestBody Object json);

    @PostMapping(value = "/batch/list", consumes = "application/json")
    Object batchList(@RequestBody Object json);

    @PostMapping(value = "/first/detail", consumes = "application/json")
    Object firstDetail(@RequestBody Object json);

    @PostMapping(value = "/last/detail", consumes = "application/json")
    Object lastDetail(@RequestBody Object json);


    @PostMapping(value = "/max/detail", consumes = "application/json")
    Object maxDetail(@RequestBody Object json);

    @PostMapping(value = "/min/detail", consumes = "application/json")
    Object minDetail(@RequestBody Object json);

    @PostMapping(value = "/mean/detail", consumes = "application/json")
    Object meanDetail(@RequestBody Object json);


    @PostMapping(value = "/sum/detail", consumes = "application/json")
    Object sumDetail(@RequestBody Object json);

    @PostMapping(value = "/endSubtractStart/detail", consumes = "application/json")
    Object endSubtractStartDetail(@RequestBody Object json);



    @PostMapping(value = "/spread/detail", consumes = "application/json")
    Object spreadDetail(@RequestBody Object json);

    @PostMapping(value = "/upperLower/detail", consumes = "application/json")
    Object upperLowerDetail(@RequestBody Object json);

    @PostMapping(value = "/statusStatisticsBoolean/detail", consumes = "application/json")
    Object statusStatisticsBooleanDetail(@RequestBody Object json);


    @PostMapping(value = "/endSubtractStart2/detail", consumes = "application/json")
    Object endSubtractStart2Detail(@RequestBody Object json);

    @PostMapping(value = "/calculator", consumes = "application/json")
    Object calculator(@RequestBody Object json);
}
