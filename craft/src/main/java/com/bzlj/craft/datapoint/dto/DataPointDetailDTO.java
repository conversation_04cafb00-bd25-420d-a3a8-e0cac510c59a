package com.bzlj.craft.datapoint.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-21 16:12
 */
@AllArgsConstructor
@Data
@NoArgsConstructor
public class DataPointDetailDTO {
    /**
     * 数据点ID
     */
    private String id;
    /**
     * 公司ID
     */
    private String companyId;
    /**
     * 数据来源 1:传感器;2:OPC数据源;3:二维码
     */
    private Integer channel;
    /**
     * 关联对象（opc数据源ID/传感器ID/二维码ID）
     */
    private String channelId;
    /**
     * 传感器名称
     */
    private String sensorName;
    /**
     * 数据源名称
     */
    private String sourceName;
    /**
     * OPC编号
     */
    private String opcCode;
    /**
     * 二维码编号
     */
    private String qrCode;
    /**
     * 二维码名称
     */
    private String qrName;
    /**
     * 数据编号
     */
    private String dataCode;
    /**
     * 数据名称
     */
    private String dataName;
    /**
     * 字符类型数据格式
     */
    private Integer pointStringType;
    /**
     * 单位
     */
    private String unit;
    /**
     * 单位Id
     */
    private String unitId;
    /**
     * 单位翻译值
     */
    private String unitValue;
    /**
     * 数据点的历史曲线查询的聚合方式
     */
    private String function;
    /**
     * 采集间隔时间
     */
    private Integer intervalTime;
    /**
     * 数据转换规则
     */
    private String convertRule;
    /**
     * 范围下限
     */
    private Double scopeMin;
    /**
     * 范围上限
     */
    private Double scopeMax;
    /**
     * 位置
     */
    private String position;
    /**
     * 设备Id
     */
    private String deviceId;
    /**
     * 设备名字
     */
    private String deviceName;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 异常时间
     */
    @JsonFormat(
            timezone = "GMT+8",
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date abnormalTime;
    /**
     * 状态  -1:无数据  1：正常 2：超过上线 3：低于下限
     */
    private Integer status;
    /**
     * 关注
     */
    private Integer attention;
    /**
     * 有效上限
     */
    private Double effectiveUpperLimit;
    /**
     * 有效下限
     */
    private Double effectiveLowerLimit;
    /**
     * 精度
     */
    private Integer precision;
    /**
     * 是否被使用 true:被使用 false 没有被使用
     */
    private Boolean isUsed;
    /**
     * 不可修改原因
     */
    private String reason;
    /**
     * 标签信息集合
     */
    private List<TagDTO> tagList;
    /**
     * 数据类型:  1-数值型;2-布尔型;3-时间点;4-字符串
     */
    private Integer dataType;
    /**
     * true展示为
     */
    private String trueDisplay;
    /**
     * false展示为
     */
    private String falseDisplay;
    /**
     * 数值全路径
     */
    private String valuePath;
    /**
     * 时间全路径
     */
    private String timePath;
    /**
     * 点位类型，1复杂感知点，2数据反应堆，3模型数据,4数据融合,5实测数据点
     */
    private Integer pointType;
    /**
     * 三方数据源编码(2022年4月12日新增，用于三方通讯置换编码，三方主要用这个通讯)
     */
    private String thirdDataSourceCode;
    /**
     * 相同命令下的数据点
     */
    private List<SameCommandDataDTO> sameCommandDataList;
}
