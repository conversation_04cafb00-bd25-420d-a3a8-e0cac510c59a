package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.CraftProcess;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CraftProcessRepository extends BaseRepository<CraftProcess, String> {
    List<CraftProcess> findByPlantPlantCodeOrderByProcessOrderAsc(String plantCode);

    @Query("SELECT c.processCode FROM CraftProcess c WHERE c.processCode IN :codes")
    List<String> findExistingCodes(@Param("codes") List<String> codes);

    CraftProcess findFirstByProcessCode(String processCode);
}