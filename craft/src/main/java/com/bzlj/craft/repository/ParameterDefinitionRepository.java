package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ParameterDefinition;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ParameterDefinitionRepository extends BaseRepository<ParameterDefinition, String> {
    /**
     * @deprecated
     */
    List<ParameterDefinition> findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(List<String> workStepIds,List<String> paramType);

    /**
     * @deprecated
     */
    List<ParameterDefinition> findByWorkStepWorkStepId(String workStepId);


    /**
     * 根据工步ID列表查询参数定义（未删除的）
     * @param workStepIds 工步ID列表
     * @return 参数定义列表
     */
    List<ParameterDefinition> findByWorkStepWorkStepIdIn(List<String> workStepIds);

}