package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.StepParameter;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StepParameterRepository extends BaseRepository<StepParameter, String> {
    List<StepParameter> findByStepIdOrderByCreatedTime(String stepId);

    /**
     * 批量查询多个工步的参数
     * @param stepIds 工步ID列表
     * @return 参数列表，按工步ID和创建时间排序
     */
    List<StepParameter> findByStepIdInOrderByStepIdAscCreatedTimeAsc(List<String> stepIds);

    /**
     * 统计指定工步下的参数数量
     * @param stepId 工步ID
     * @return 参数数量
     */
    int countByStepId(String stepId);

    /**
     * 根据工步ID和参数代码查询参数
     * @param stepId 工步ID
     * @param paramCode 参数代码
     * @return 参数对象，如果不存在则返回null
     */
    StepParameter findByStepIdAndParamCode(String stepId, String paramCode);
}