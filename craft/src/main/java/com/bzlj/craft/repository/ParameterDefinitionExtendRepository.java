package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ParameterDefinitionExtend;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ParameterDefinitionExtendRepository extends BaseRepository<ParameterDefinitionExtend, String> {

    /**
     * 根据参数定义ID列表查询扩展信息（未删除的）
     * @param paramDefIds 参数定义ID列表
     * @return 参数定义扩展列表
     */
    List<ParameterDefinitionExtend> findByParamDefIdIn(List<String> paramDefIds);

    void deleteAllByParamDefIdIn(List<String> paramDefIds);
}