package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.WorkStep;

import java.util.List;

public interface WorkStepRepository extends BaseRepository<WorkStep, String> {

    List<WorkStep> findByStepIdInAndTaskTaskIdAndDeleted(List<String> stepIds, String taskId,boolean deleted);

    List<WorkStep> findByTaskTaskIdAndDeleted(String taskId,  boolean deleted);

    WorkStep findFirstByStepIdAndTaskTaskIdAndDeleted(String stepId, String taskId, boolean deleted);

    WorkStep findFirstByTaskTaskIdAndWorkStepNameAndDeleted(String taskId, String workStepName, Boolean deleted);

    List<WorkStep> findByTaskTaskIdInAndWorkStepNameInAndDeleted(List<String> taskTaskIds, List<String> workStepNames, Boolean deleted);
}