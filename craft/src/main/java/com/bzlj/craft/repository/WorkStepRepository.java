package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.WorkStep;

import java.util.List;

public interface WorkStepRepository extends BaseRepository<WorkStep, String> {

    List<WorkStep> findByStepIdInAndTaskTaskId(List<String> stepIds, String taskId);

    List<WorkStep> findByTaskTaskId(String taskId);

    void deleteAllByTaskTaskIdIn(List<String> taskIds);

    WorkStep findFirstByStepIdAndTaskTaskId(String stepId, String taskId);

    WorkStep findFirstByTaskTaskIdAndWorkStepName(String taskId, String workStepName);

    List<WorkStep> findByTaskTaskIdInAndWorkStepNameIn(List<String> taskTaskIds, List<String> workStepNames);
}