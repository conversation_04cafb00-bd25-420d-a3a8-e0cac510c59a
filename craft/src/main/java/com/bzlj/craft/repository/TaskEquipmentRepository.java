package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.TaskEquipment;
import com.bzlj.craft.entity.TaskEquipmentId;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TaskEquipmentRepository extends BaseRepository<TaskEquipment, TaskEquipmentId> {
    List<TaskEquipment> findByTaskTaskId(String taskId);

    TaskEquipment findFirstByTaskTaskId(String taskId);

    void deleteAllByTaskTaskIdIn(List<String> taskIds);
}