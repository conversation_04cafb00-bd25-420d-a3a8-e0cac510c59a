package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.Equipment;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EquipmentRepository extends BaseRepository<Equipment, String> {

    Equipment findFirstByCode(String equipmentCode);

    @Query("SELECT c.code FROM Equipment c WHERE c.code IN :codes")
    List<String> findExistingCodes(@Param("codes") List<String> codes);
}