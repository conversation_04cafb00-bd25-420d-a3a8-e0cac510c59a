package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ProcessParameter;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProcessParameterRepository extends BaseRepository<ProcessParameter, String> {

    List<ProcessParameter> findByParamDefParamDefIdInAndTaskTaskId(List<String> paramDefIds, String taskId);

    /**
     * 根据参数定义ID列表查询工艺参数
     * @param paramDefIds 参数定义ID列表
     * @return 工艺参数列表
     */
    List<ProcessParameter> findByParamDefParamDefIdIn(List<String> paramDefIds);

    void deleteAllByParamDefParamDefIdIn(List<String> paramDefIds);
}