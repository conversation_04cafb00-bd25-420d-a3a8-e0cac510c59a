package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.EmployeeTeam;
import com.bzlj.craft.entity.EmployeeTeamId;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeTeamRepository extends BaseRepository<EmployeeTeam, EmployeeTeamId> {

    List<EmployeeTeam> findByTeamTeamIdIn(List<String> teamIds);
}