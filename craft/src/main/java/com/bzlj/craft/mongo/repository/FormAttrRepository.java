package com.bzlj.craft.mongo.repository;

import com.bzlj.craft.mongo.entity.FormAttr;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

/**
 *
 */
public interface FormAttrRepository extends MongoRepository<FormAttr, String> {

    /**
     * 根据组件ID查询表单属性
     * @param componentKey 组件ID
     * @return 表单属性列表
     */
    List<FormAttr> findByComponentKeyOrderByOrder(String componentKey);

}
