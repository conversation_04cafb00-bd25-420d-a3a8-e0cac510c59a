package com.bzlj.craft.mongo.entity;

import jakarta.persistence.Id;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-23 16:08
 */
@Document(collection = "deviation_tracing")
@Data
@EqualsAndHashCode(callSuper = false)
public class DeviationTracing implements Serializable {
    @Id
    private String id;

    private String taskId;

    private String taskCode;

    private String processType;

    private String plantCode;

    private String equipmentCode;

    private String equipmentId;

    private String equipmentName;

    private String stepName;

    private String stepId;

    private String parameterName;

    private String parameterId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private BigDecimal duration;

    /**
     * 最大偏离程度
     */
    private BigDecimal maxDegree;

    /**
     * 平均偏离程度
     */
    private BigDecimal aveDegree;
}
