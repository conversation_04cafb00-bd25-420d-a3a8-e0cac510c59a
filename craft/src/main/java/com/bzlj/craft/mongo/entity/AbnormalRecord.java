package com.bzlj.craft.mongo.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Unwrapped;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-21 14:04
 */
@Document(collection = "abnormal_record")
@Data
@EqualsAndHashCode(callSuper = false)
public class AbnormalRecord implements Serializable {

    @Id
    private String id;

    /**
     * 报警时间
     */
    private LocalDateTime alertTime;

    /**
     * 报警类型 false:系统上报 true人工上报
     */
    private Boolean alertType;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务code
     */
    private String taskCode;

    /**
     * 工序类型，关联字典
     */
    private String processType;

    /**
     * 报警等级
     */
    private Integer level;

    @Unwrapped(onEmpty= Unwrapped.OnEmpty.USE_EMPTY)
    private AlarmContent alarmContent;
}
