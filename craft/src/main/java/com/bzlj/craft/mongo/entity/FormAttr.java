package com.bzlj.craft.mongo.entity;

import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import com.bzlj.craft.enums.TypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.HashMap;
import java.util.Map;

/**
 * 表单属性
 * <AUTHOR>
 * @description:
 * @date 2025-03-07 11:15
 */
@Document(collection = "form_attr")
@Data
@EqualsAndHashCode(callSuper = false)
public class FormAttr {
    @Id
    private String id;

    /**
     * 组件id
     */
    private String componentKey;

    /**
     * 属性名称
     */
    private String dataIndex;

    /**
     * 属性标签
     */
    private String label;

    /**
     * 字段类型
     */
    private TypeEnum type;

    /**
     * 当前属性是否支持搜索
     */
    private boolean searchable;

    /**
     * 属性排序
     */
    private int order;

    /**
     * 是否展示
     */
    private boolean display;

    /**
     * 是否范围
     */
    private boolean range;

    /**
     * 属性字典项
     */
    private String dictCode;

    /**
     * 属性格式
     */
    private String format;

    /**
     * 属性别名
     */
    private String alias;

    /**
     * 是否支持多选
     */
    private Boolean multiple;

    private Boolean initialRequest;


    /**
     * 数据源类型
     */
    private DataSourceType dataSourceType;

    private DataSource dataSource;

    @Data
    @Builder
    public static class DataSource {
        private Api api;
        private Mapping mapping;
        @Data
        @Builder
        public static class Mapping {
            private String valueKey;
            private String labelKey;
        }

        @Data
        @Builder
        public static class Api {
            private String url;
            private HttpMethod method;

            /**
             * 额外的参数字段，用于存储dependencyParams、apiParams等
             */
            @Builder.Default
            private Map<String, Object> params = new HashMap<>();
        }
    }


}
