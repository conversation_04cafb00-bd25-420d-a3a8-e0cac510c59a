package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.util.DTOConverter;
import com.bzlj.base.util.DistinctUtil;
import com.bzlj.craft.api.service.IQualityInspectionService;
import com.bzlj.craft.api.service.IQualityMetricService;
import com.bzlj.craft.api.service.IQualityResultService;
import com.bzlj.craft.dto.QualityInspectionDTO;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.enums.MetricType;
import com.bzlj.craft.repository.QualityInspectionRepository;
import com.bzlj.craft.vo.surveillance.QualityInspectionVO;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.bzlj.craft.exception.BiciErrorData.Metric_Type_ERROR;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class QualityInspectionServiceImpl implements IQualityInspectionService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private QualityInspectionRepository repository;

    @Autowired
    private IQualityMetricService qualityMetricService;

    @Override
    public BaseRepository<QualityInspection, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<QualityInspectionDTO> getDTOClass() {
        return QualityInspectionDTO.class;
    }

    @Override
    public Class<QualityInspection> getPOClass() {
        return QualityInspection.class;
    }

    @Autowired
    private IQualityResultService qualityResultService;

    @Override
    public QualityInspectionDTO convertToDto(QualityInspection entity, Class<QualityInspectionDTO> dtoClass, String... ignoreProperties) {
        QualityInspectionDTO qualityInspectionDTO = new QualityInspectionDTO();
        BeanUtils.copyProperties(entity, qualityInspectionDTO);
        if(Objects.nonNull(entity.getMaterial())){
            qualityInspectionDTO.setMaterialCode(entity.getMaterial().getMaterialCode());
            qualityInspectionDTO.setHeatNumber(entity.getMaterial().getHeatNumber());

        }
        if(Objects.nonNull(entity.getMaterialSample())){
            qualityInspectionDTO.setSampleNumber(entity.getMaterialSample().getSampleCode());
        }
        return qualityInspectionDTO;
    }

    @Override
    public Map<String, Object> findInspectionData(SearchCondition searchCondition){
        Map<String,Object> map = Maps.newHashMap();
        searchCondition.setOpenProps(Lists.newArrayList("material","qualityRule","materialSample"));
        List<QualityInspection> inspections = findEntityWithCondition(searchCondition);
        if(CollectionUtils.isEmpty(inspections)) return map;
        List<String> inspectionIds = inspections.stream().map(QualityInspection::getInspectionId).toList();
        Map<String, List<QualityInspection>> inspectionMap = inspections.stream().collect(Collectors.groupingBy(inspection -> inspection.getQualityRule().getRuleId()));

        List<QualityRule> rules = inspections.stream().map(QualityInspection::getQualityRule)
                .filter(DistinctUtil.distinctByKey(QualityRule::getRuleId))
                .sorted(Comparator.comparing(QualityRule::getCreatedTime)).toList();
        List<String> tabNames = rules.stream().map(QualityRule::getRuleName).toList();
        //放入检验标签页信息
        map.put("tabNames",tabNames);
        List<String> ruleIds = rules.stream().map(QualityRule::getRuleId).toList();
        //查询指标
        List<QualityMetric> metrics = qualityMetricService.findByRuleIds(ruleIds);
        Map<String, List<QualityMetric>> metricMap = metrics.stream().collect(Collectors.groupingBy(metric -> metric.getRule().getRuleId()));
        //查询结果
        List<QualityResult> qualityResults = qualityResultService.findByInspectionIds(inspectionIds);
        Map<String, List<QualityResult>> resultMap = qualityResults.stream().collect(Collectors.groupingBy(qualityResult -> qualityResult.getMetric().getMetricId()));
        List<List<List<Object>>> datas = new ArrayList<>();
        rules.forEach(rule->{
            List<QualityInspection> qualityInspections = inspectionMap.get(rule.getRuleId());
            List<QualityInspectionDTO> qualityInspectionDTOS = convertToDtoList(qualityInspections, getDTOClass());
            List<List<Object>> data = DTOConverter.convertWithKey(qualityInspectionDTOS, QualityInspectionVO.class);
            List<QualityMetric> qualityMetrics = metricMap.get(rule.getRuleId());
            qualityMetrics.forEach(qualityMetric -> {
                List<Object> values = Lists.newArrayList();
                values.add(qualityMetric.getMetricName());
                List<QualityResult> results = resultMap.get(qualityMetric.getMetricId());
                if(CollectionUtils.isEmpty(results)){
                    results = Lists.newArrayList();
                }
                ImmutableMap<String, QualityResult> resultByInspectionMap = Maps.uniqueIndex(results, qualityResult -> qualityResult.getInspection().getInspectionId());
                for (QualityInspection qualityInspection : qualityInspections) {
                    QualityResult qualityResult = resultByInspectionMap.get(qualityInspection.getInspectionId());
                    if(Objects.isNull(qualityResult)) {
                        values.add(null);
                        break;
                    }
                    String metricType = qualityMetric.getMetricType().getItemCode();
                    MetricType type = null;
                    try {
                        type = MetricType.valueOf(metricType);
                    }catch (Exception e){
                        throw Metric_Type_ERROR.buildException();
                    }
                    switch (type) {
                        case numeric:
                            values.add(qualityResult.getQualityNumericResult().getActualValue());
                            //添加数据上下限
                            MetricNumeric metricNumeric = qualityMetric.getMetricNumeric();
                            if(Objects.nonNull(metricNumeric)){
                                values.add(metricNumeric.getMinValue());
                                values.add(metricNumeric.getMaxValue());
                            }
                            break;
                        case attach:
                            values.add(qualityResult.getQualityAttachResult().getFilePath());
                            break;
                        case enums:
                            values.add(qualityResult.getQualityEnumResult().getActualValue());
                            break;
                        default:
                            values.add(null);

                    }
                }
                data.add(values);
            });
            datas.add(data);
        });

        map.put("data",datas);
//        inspections.stream().map
        return map;
    }

}
