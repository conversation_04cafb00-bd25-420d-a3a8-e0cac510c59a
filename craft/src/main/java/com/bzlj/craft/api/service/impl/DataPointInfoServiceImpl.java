package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.base.search.SortItem;
import com.bzlj.craft.api.service.IDataPointInfoService;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.entity.DataPointInfo;
import com.bzlj.craft.enums.PointType;
import com.bzlj.craft.repository.DataPointInfoRepository;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class DataPointInfoServiceImpl implements IDataPointInfoService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private DataPointInfoRepository repository;

    @Override
    public BaseRepository<DataPointInfo, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<DataPointInfoDTO> getDTOClass() {
        return DataPointInfoDTO.class;
    }

    @Override
    public Class<DataPointInfo> getPOClass() {
        return DataPointInfo.class;
    }


    @Override
    public List<DataPointInfo> findByProcessStepIdsAndEquipmentIdAndPointType(List<String> stepIds, String equipId, PointType pointType){
        SearchCondition condition = new SearchCondition();
        condition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("processStep.id", stepIds, null, SearchItem.Operator.IN))
                .item(new SearchItem("equip.id", equipId, null, SearchItem.Operator.EQ))
                .item(new SearchItem("pointType.itemCode", pointType.getCode(), null, SearchItem.Operator.EQ))
                .build());
        condition.setOpenProps(Lists.newArrayList("processStep","stepParameter"));
        condition.setSortItems(Lists.newArrayList(SortItem.builder().fieldName("createTime").sortOrder(SortItem.DESC_ORDER_NAME).build()));
        return findEntityWithCondition(condition);
    }

    @Override
    public List<DataPointInfo> findByParameterIdAndEquipmentId(String stepId, String equipId){
        SearchCondition condition = new SearchCondition();
        condition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("stepParameter.paramId", stepId, null, SearchItem.Operator.EQ))
                .item(new SearchItem("equip.id", equipId, null, SearchItem.Operator.EQ))
                .build());
        condition.setOpenProps(Lists.newArrayList("pointType"));
        condition.setSortItems(Lists.newArrayList(SortItem.builder().fieldName("createTime").sortOrder(SortItem.DESC_ORDER_NAME).build()));
        return findEntityWithCondition(condition);
    }

}
