package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.TaskMaterialDTO;
import com.bzlj.craft.entity.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface ITaskMaterialService extends IBaseService<TaskMaterial, TaskMaterialDTO, TaskMaterialId> {
    List<TaskMaterial> buildTaskMaterialAndSave(List<Material> materials, ProductionTask productionTask, Boolean relationType, Map<String, List<MaterialAttr>> materialAttrMap);


}
