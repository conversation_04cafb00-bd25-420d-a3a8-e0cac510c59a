package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IQualityMetricService;
import com.bzlj.craft.dto.QualityMetricDTO;
import com.bzlj.craft.entity.QualityMetric;
import com.bzlj.craft.repository.QualityMetricRepository;
import com.bzlj.craft.repository.QualityResultRepository;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class QualityMetricServiceImpl implements IQualityMetricService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private QualityMetricRepository repository;


    @Override
    public BaseRepository<QualityMetric, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<QualityMetricDTO> getDTOClass() {
        return QualityMetricDTO.class;
    }

    @Override
    public Class<QualityMetric> getPOClass() {
        return QualityMetric.class;
    }

    @Autowired
    private QualityResultRepository qualityResultRepository;

    @Override
    public List<QualityMetric> findByRuleIds(List<String> ruleIds) {
        SearchCondition metricSearchCondition = new SearchCondition();
        metricSearchCondition.setSearchItems(SearchItems.builder().item(new SearchItem("rule.ruleId", ruleIds, null, SearchItem.Operator.IN)).build());
        metricSearchCondition.setOpenProps(Lists.newArrayList("metricType","rule","metricNumeric","metricAttach","metricEnum"));
        return findEntityWithCondition(metricSearchCondition);
    }
}
