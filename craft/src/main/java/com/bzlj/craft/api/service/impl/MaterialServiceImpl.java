package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IMaterialService;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.dto.MaterialDTO;
import com.bzlj.craft.dto.TaskMaterialDTO;
import com.bzlj.craft.entity.Material;
import com.bzlj.craft.entity.TaskMaterial;
import com.bzlj.craft.entity.TaskMaterialId;
import com.bzlj.craft.repository.MaterialRepository;
import com.bzlj.craft.repository.TaskMaterialRepository;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class MaterialServiceImpl implements IMaterialService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private MaterialRepository repository;

    @Override
    public BaseRepository<Material, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<MaterialDTO> getDTOClass() {
        return MaterialDTO.class;
    }

    @Override
    public Class<Material> getPOClass() {
        return Material.class;
    }

    @Override
    public List<Material> findByMaterialCodes(List<String> materialCodes) {
        SearchCondition condition = new SearchCondition();
        condition.setSearchItems(SearchItems.builder().item(new SearchItem("materialCode", materialCodes, null, SearchItem.Operator.IN)).build());
        List<Material> materials = findAllEntityWithCondition(condition);
        if (CollectionUtils.isEmpty(materials)) return Lists.newArrayList();
        return materials;
    }
}
