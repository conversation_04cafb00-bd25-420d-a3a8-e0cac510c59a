package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.ProcessParameterDTO;
import com.bzlj.craft.entity.ProcessParameter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IProcessParameterService extends IBaseService<ProcessParameter, ProcessParameterDTO, String> {

    /**
     * 根据参数定义id和任务id查询
     * @param paramDefIds
     * @param taskId
     * @return
     */
    List<ProcessParameter> findByParamDefAndTask(List<String> paramDefIds, String taskId);
}
