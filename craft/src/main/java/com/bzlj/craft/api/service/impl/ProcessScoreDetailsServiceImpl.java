package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.api.service.IProcessScoreDetailsService;
import com.bzlj.craft.dto.ProcessScoreDetailsDTO;
import com.bzlj.craft.entity.ProcessScoreDetails;
import com.bzlj.craft.repository.ProcessScoreDetailsRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ProcessScoreDetailsServiceImpl implements IProcessScoreDetailsService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ProcessScoreDetailsRepository repository;

    @Override
    public BaseRepository<ProcessScoreDetails, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ProcessScoreDetailsDTO> getDTOClass() {
        return ProcessScoreDetailsDTO.class;
    }

    @Override
    public Class<ProcessScoreDetails> getPOClass() {
        return ProcessScoreDetails.class;
    }

    @Override
    public ProcessScoreDetailsDTO convertToDto(ProcessScoreDetails entity, Class<ProcessScoreDetailsDTO> dtoClass, String... ignoreProperties) {
        ProcessScoreDetailsDTO dto = new ProcessScoreDetailsDTO();
        BeanUtils.copyProperties(entity, dto, ignoreProperties);
        if(Objects.isNull(entity.getTask())) return dto;
        dto.setTaskId(entity.getTask().getTaskId());
        return dto;
    }
}
