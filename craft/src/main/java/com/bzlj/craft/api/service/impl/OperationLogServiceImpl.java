package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.api.service.IOperationLogService;
import com.bzlj.craft.dto.OperationLogDTO;
import com.bzlj.craft.entity.OperationLog;
import com.bzlj.craft.repository.OperationLogRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class OperationLogServiceImpl implements IOperationLogService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private OperationLogRepository repository;

    @Override
    public BaseRepository<OperationLog, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<OperationLogDTO> getDTOClass() {
        return OperationLogDTO.class;
    }

    @Override
    public Class<OperationLog> getPOClass() {
        return OperationLog.class;
    }

}
