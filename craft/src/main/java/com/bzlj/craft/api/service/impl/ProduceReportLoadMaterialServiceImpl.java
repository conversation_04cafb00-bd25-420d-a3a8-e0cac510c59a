package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.api.service.IProduceReportLoadMaterialService;
import com.bzlj.craft.dto.ProduceReportLoadMaterialDTO;
import com.bzlj.craft.entity.ProduceReportLoadMaterial;
import com.bzlj.craft.repository.EmployeeRepository;
import com.bzlj.craft.repository.ProduceReportLoadMaterialRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ProduceReportLoadMaterialServiceImpl implements IProduceReportLoadMaterialService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ProduceReportLoadMaterialRepository repository;

    @Override
    public BaseRepository<ProduceReportLoadMaterial, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ProduceReportLoadMaterialDTO> getDTOClass() {
        return ProduceReportLoadMaterialDTO.class;
    }

    @Override
    public Class<ProduceReportLoadMaterial> getPOClass() {
        return ProduceReportLoadMaterial.class;
    }

    @Autowired
    private EmployeeRepository employeeRepository;

    @Override
    public ProduceReportLoadMaterialDTO convertToDto(ProduceReportLoadMaterial entity, Class<ProduceReportLoadMaterialDTO> dtoClass, String... ignoreProperties) {
        ProduceReportLoadMaterialDTO produceReportLoadMaterialDTO = new ProduceReportLoadMaterialDTO();
        BeanUtils.copyProperties(entity, produceReportLoadMaterialDTO);
        if(Objects.nonNull(entity.getMaterial())){
            produceReportLoadMaterialDTO.setMaterialName(entity.getMaterial().getMaterialName());
            produceReportLoadMaterialDTO.setMaterialCode(entity.getMaterial().getMaterialCode());
            produceReportLoadMaterialDTO.setMaterialBatchNo(entity.getMaterial().getMaterialBatchNo());
            produceReportLoadMaterialDTO.setSupplier(entity.getMaterial().getSupplier());
        }
        if(Objects.nonNull(entity.getCreateUser())){
            produceReportLoadMaterialDTO.setCreateByUserName(entity.getCreateUser().getFullName());
        }
        return produceReportLoadMaterialDTO;
    }

}
