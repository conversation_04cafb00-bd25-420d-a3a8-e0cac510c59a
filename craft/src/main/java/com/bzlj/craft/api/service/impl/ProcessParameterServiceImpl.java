package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IProcessParameterService;
import com.bzlj.craft.dto.ProcessParameterDTO;
import com.bzlj.craft.entity.ProcessParameter;
import com.bzlj.craft.repository.ProcessParameterRepository;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ProcessParameterServiceImpl implements IProcessParameterService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ProcessParameterRepository repository;

    @Override
    public BaseRepository<ProcessParameter, String> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<ProcessParameterDTO> getDTOClass() {
        return ProcessParameterDTO.class;
    }

    @Override
    public Class<ProcessParameter> getPOClass() {
        return ProcessParameter.class;
    }


    @Override
    public List<ProcessParameter> findByParamDefAndTask(List<String> paramDefIds, String taskId) {
        SearchCondition resultCondition = new SearchCondition();
        resultCondition.setSearchItems(SearchItems.builder().item(new SearchItem("paramDef.paramDefId", paramDefIds, null, SearchItem.Operator.IN))
                .item(new SearchItem("task.taskId", taskId, null, SearchItem.Operator.EQ)).build());
        resultCondition.setOpenProps(Lists.newArrayList("processTextParameter","processDateParameter","processNumParameter"));

        return findEntityWithCondition(resultCondition);
    }
}
