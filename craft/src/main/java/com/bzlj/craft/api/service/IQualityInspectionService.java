package com.bzlj.craft.api.service;

import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.QualityInspectionDTO;
import com.bzlj.craft.entity.QualityInspection;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IQualityInspectionService extends IBaseService<QualityInspection, QualityInspectionDTO, String> {

    Map<String, Object> findInspectionData(SearchCondition searchCondition);
}
