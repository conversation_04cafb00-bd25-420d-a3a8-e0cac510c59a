package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.QualityResultDTO;
import com.bzlj.craft.entity.QualityResult;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IQualityResultService extends IBaseService<QualityResult, QualityResultDTO, Long> {

    List<QualityResult> findByInspectionIds(List<String> inspectionIds);
}
