package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.api.service.IQualityResultService;
import com.bzlj.craft.dto.QualityResultDTO;
import com.bzlj.craft.entity.QualityResult;
import com.bzlj.craft.repository.QualityResultRepository;
import com.google.common.collect.Lists;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class QualityResultServiceImpl implements IQualityResultService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private QualityResultRepository repository;


    @Override
    public BaseRepository<QualityResult, Long> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<QualityResultDTO> getDTOClass() {
        return QualityResultDTO.class;
    }

    @Override
    public Class<QualityResult> getPOClass() {
        return QualityResult.class;
    }


    @Override
    public List<QualityResult> findByInspectionIds(List<String> inspectionIds) {
        SearchCondition resultCondition = new SearchCondition();
        resultCondition.setSearchItems(SearchItems.builder().item(new SearchItem("inspection.inspectionId", inspectionIds, null, SearchItem.Operator.IN)).build());
        resultCondition.setOpenProps(Lists.newArrayList("metric","qualityNumericResult","qualityEnumResult","qualityAttachResult"));

        return findEntityWithCondition(resultCondition);
    }
}
