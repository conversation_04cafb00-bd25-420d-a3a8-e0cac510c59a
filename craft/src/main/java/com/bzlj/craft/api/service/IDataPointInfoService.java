package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.entity.DataPointInfo;
import com.bzlj.craft.enums.PointType;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IDataPointInfoService extends IBaseService<DataPointInfo, DataPointInfoDTO, String> {

    List<DataPointInfo> findByProcessStepIdsAndEquipmentIdAndPointType(List<String> processStepIds,String equipmentId, PointType pointType);

    List<DataPointInfo> findByParameterIdAndEquipmentId(String processStepId,String equipmentId);
}
