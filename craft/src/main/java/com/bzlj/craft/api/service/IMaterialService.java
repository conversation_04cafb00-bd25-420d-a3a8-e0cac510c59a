package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.DataPointInfoDTO;
import com.bzlj.craft.dto.MaterialDTO;
import com.bzlj.craft.entity.DataPointInfo;
import com.bzlj.craft.entity.Material;

import java.util.List;

public interface IMaterialService  extends IBaseService<Material, MaterialDTO, String> {

    /**
     * 根据物料编码查询物料
     * @param materialCodes
     * @return
     */
    List<Material> findByMaterialCodes(List<String> materialCodes);
}
