package com.bzlj.craft.api.service;

import com.bzlj.base.service.IBaseService;
import com.bzlj.craft.dto.QualityMetricDTO;
import com.bzlj.craft.entity.QualityMetric;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 11:34
 */
public interface IQualityMetricService extends IBaseService<QualityMetric, QualityMetricDTO, String> {

    List<QualityMetric> findByRuleIds(List<String> ruleIds);
}
