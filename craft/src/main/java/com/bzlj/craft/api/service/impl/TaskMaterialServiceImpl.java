package com.bzlj.craft.api.service.impl;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.api.service.ITaskMaterialService;
import com.bzlj.craft.dto.TaskMaterialDTO;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.TaskMaterialRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 13:07
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskMaterialServiceImpl implements ITaskMaterialService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private TaskMaterialRepository repository;

    @Override
    public BaseRepository<TaskMaterial, TaskMaterialId> getRepository() {
        return repository;
    }

    @Override
    public EntityManager getEntityManager() {
        return entityManager;
    }

    @Override
    public Class<TaskMaterialDTO> getDTOClass() {
        return TaskMaterialDTO.class;
    }

    @Override
    public Class<TaskMaterial> getPOClass() {
        return TaskMaterial.class;
    }

    @Override
    public TaskMaterialDTO convertToDto(TaskMaterial entity, Class<TaskMaterialDTO> dtoClass, String... ignoreProperties) {
        TaskMaterialDTO taskMaterialDTO = new TaskMaterialDTO();
        if(Objects.nonNull(entity.getMaterial())){
            taskMaterialDTO.setMaterialId(entity.getMaterial().getMaterialId());
            taskMaterialDTO.setBrand(entity.getMaterial().getBrand());
            taskMaterialDTO.setHeatNumber(entity.getMaterial().getHeatNumber());
            taskMaterialDTO.setIngotNumber(entity.getMaterial().getIngotNumber());
            taskMaterialDTO.setIngotType(entity.getMaterial().getIngotType());
        }
        return taskMaterialDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TaskMaterial> buildTaskMaterialAndSave(List<Material> materials, ProductionTask productionTask, Boolean relationType, Map<String, List<MaterialAttr>> materialAttrMap) {
        //创建产出物料
        List<TaskMaterial> taskMaterials = materials.stream().map(material -> {
            TaskMaterial taskMaterial = new TaskMaterial();
            taskMaterial.setMaterial(material);
            taskMaterial.setTask(productionTask);
            taskMaterial.setRelationType(relationType);
            taskMaterial.setId(new TaskMaterialId(productionTask.getTaskId(), material.getMaterialId()));
            List<MaterialAttr> materialAttrs = materialAttrMap.get(material.getMaterialCode());
            if(Objects.nonNull(materialAttrs)){
                BigDecimal quantity = BigDecimal.ZERO;
                // 遍历materialAttr集合，从attr map中获取quantity并累加
                for(MaterialAttr attr : materialAttrs) {
                    if(Objects.nonNull(attr.getAttr()) && attr.getAttr().containsKey("quantity")) {
                        Object quantityObj = attr.getAttr().get("quantity");
                        if(quantityObj instanceof Number) {
                            quantity = quantity.add(new BigDecimal(quantityObj.toString()));
                        } else if(quantityObj instanceof String) {
                            try {
                                quantity = quantity.add(new BigDecimal((String)quantityObj));
                            } catch(Exception e) {
                                log.warn("Invalid quantity format in materialAttr for material: {}", material.getMaterialCode());
                            }
                        }
                    }
                }
                taskMaterial.setQuantity(quantity);
            }
            return taskMaterial;
        }).collect(Collectors.toList());
        return this.batchInsertEntity(taskMaterials);
    }
}
