package com.bzlj.craft.query;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全流程追溯查询
 *
 * <AUTHOR>
 * @date 2025/3/25 11:01
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QualityTraceQuery {
    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    private String materialCode;
    /**
     * 厂区编码
     */
    private String plantId;
}
