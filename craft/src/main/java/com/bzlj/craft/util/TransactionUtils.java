package com.bzlj.craft.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.function.Supplier;

/**
 * 事务管理工具类
 * <p>
 * 提供编程式事务管理的便捷方法，用于处理复杂的事务场景。
 * 主要功能包括：
 * 1. 在新事务中执行操作
 * 2. 在当前事务中执行操作
 * 3. 以非事务方式执行操作
 * 4. 事务状态检查和管理
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Slf4j
@Component
public class TransactionUtils {

    private final TransactionTemplate transactionTemplate;
    private final PlatformTransactionManager transactionManager;

    public TransactionUtils(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    /**
     * 在新事务中执行操作
     * <p>
     * 无论当前是否存在事务，都会创建一个新的事务来执行操作。
     * 新事务与当前事务相互独立，互不影响。
     * </p>
     *
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeInNewTransaction(Supplier<T> action) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        def.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
        def.setTimeout(30); // 30秒超时
        
        TransactionTemplate newTransactionTemplate = new TransactionTemplate(transactionManager, def);
        
        return newTransactionTemplate.execute(status -> {
            try {
                log.debug("开始执行新事务操作");
                T result = action.get();
                log.debug("新事务操作执行成功");
                return result;
            } catch (Exception e) {
                log.error("新事务操作执行失败: {}", e.getMessage(), e);
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 在新事务中执行操作（无返回值）
     *
     * @param action 要执行的操作
     */
    public void executeInNewTransaction(Runnable action) {
        executeInNewTransaction(() -> {
            action.run();
            return null;
        });
    }

    /**
     * 在当前事务中执行操作
     * <p>
     * 如果当前存在事务，则在当前事务中执行；
     * 如果当前不存在事务，则创建新事务。
     * </p>
     *
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeInCurrentTransaction(Supplier<T> action) {
        return transactionTemplate.execute(status -> {
            try {
                log.debug("开始执行当前事务操作");
                T result = action.get();
                log.debug("当前事务操作执行成功");
                return result;
            } catch (Exception e) {
                log.error("当前事务操作执行失败: {}", e.getMessage(), e);
                status.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 在当前事务中执行操作（无返回值）
     *
     * @param action 要执行的操作
     */
    public void executeInCurrentTransaction(Runnable action) {
        executeInCurrentTransaction(() -> {
            action.run();
            return null;
        });
    }

    /**
     * 以非事务方式执行操作
     * <p>
     * 暂停当前事务（如果存在），以非事务方式执行操作。
     * 适用于不需要事务保护的操作，如日志记录、消息发送等。
     * </p>
     *
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithoutTransaction(Supplier<T> action) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);
        
        TransactionTemplate noTransactionTemplate = new TransactionTemplate(transactionManager, def);
        
        return noTransactionTemplate.execute(status -> {
            try {
                log.debug("开始执行非事务操作");
                T result = action.get();
                log.debug("非事务操作执行成功");
                return result;
            } catch (Exception e) {
                log.error("非事务操作执行失败: {}", e.getMessage(), e);
                // 非事务操作失败不影响其他事务
                throw e;
            }
        });
    }

    /**
     * 以非事务方式执行操作（无返回值）
     *
     * @param action 要执行的操作
     */
    public void executeWithoutTransaction(Runnable action) {
        executeWithoutTransaction(() -> {
            action.run();
            return null;
        });
    }

    /**
     * 检查当前是否在事务中
     *
     * @return 如果当前在事务中返回true，否则返回false
     */
    public boolean isInTransaction() {
        try {
            TransactionStatus status = transactionManager.getTransaction(
                new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_SUPPORTS)
            );
            boolean inTransaction = status != null && !status.isCompleted();
            if (inTransaction) {
                // 如果只是为了检查状态，需要提交这个支持性事务
                transactionManager.commit(status);
            }
            return inTransaction;
        } catch (Exception e) {
            log.debug("检查事务状态时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标记当前事务为回滚状态
     * <p>
     * 将当前事务标记为只能回滚，不能提交。
     * 适用于在检测到业务异常时主动标记事务回滚。
     * </p>
     */
    public void markCurrentTransactionRollbackOnly() {
        try {
            TransactionStatus status = transactionManager.getTransaction(
                new DefaultTransactionDefinition(TransactionDefinition.PROPAGATION_MANDATORY)
            );
            if (status != null && !status.isCompleted()) {
                status.setRollbackOnly();
                log.debug("当前事务已标记为回滚状态");
            }
        } catch (Exception e) {
            log.warn("标记事务回滚失败: {}", e.getMessage(), e);
        }
    }
}
