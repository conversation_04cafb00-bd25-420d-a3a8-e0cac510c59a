package com.bzlj.craft.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateTimeConverter {
    public static LocalDateTime convertToLocalDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            throw new IllegalArgumentException("日期时间字符串不能为空");
        }

        dateTimeStr = dateTimeStr.trim();

        try {
            if (dateTimeStr.length() == 8) {
                // 处理 yyyyMMdd 格式（8位数字）
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                LocalDate date = LocalDate.parse(dateTimeStr, dateFormatter);
                // 时间部分默认为 00:00:00
                return date.atTime(LocalTime.MIDNIGHT);
            } else if (dateTimeStr.length() == 14) {
                // 处理 yyyyMMddHHmmss 格式（14位数字）
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                return LocalDateTime.parse(dateTimeStr, dateTimeFormatter);
            } else {
                throw new IllegalArgumentException("不支持的日期时间格式长度: " + dateTimeStr.length() +
                    "。支持的格式: yyyyMMdd (8位) 或 yyyyMMddHHmmss (14位)");
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期时间格式无效或值不合法: " + dateTimeStr +
                "。支持的格式: yyyyMMdd (8位) 或 yyyyMMddHHmmss (14位)", e);
        }
    }
}