package com.bzlj.craft.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 哈希工具类
 * 提供MD5等哈希算法的工具方法
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
public class HashUtils {

    private static final String MD5_ALGORITHM = "MD5";
    private static final char[] HEX_CHARS = "0123456789abcdef".toCharArray();

    /**
     * 计算字符串的MD5哈希值
     *
     * @param input 输入字符串
     * @return MD5哈希值的十六进制字符串，如果计算失败返回null
     */
    public static String md5(String input) {
        if (input == null) {
            return null;
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance(MD5_ALGORITHM);
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5算法不可用", e);
            return null;
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            result.append(HEX_CHARS[(b >> 4) & 0xF]);
            result.append(HEX_CHARS[b & 0xF]);
        }
        return result.toString();
    }

    /**
     * 验证输入字符串的MD5哈希值是否匹配
     *
     * @param input 输入字符串
     * @param expectedHash 期望的MD5哈希值
     * @return 如果匹配返回true，否则返回false
     */
    public static boolean verifyMd5(String input, String expectedHash) {
        if (input == null || expectedHash == null) {
            return false;
        }
        
        String actualHash = md5(input);
        return expectedHash.equals(actualHash);
    }
}
