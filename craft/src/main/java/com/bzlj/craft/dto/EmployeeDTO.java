package com.bzlj.craft.dto;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class EmployeeDTO {
    private String id;

    private String employeeNo;

    private String fullName;

    private String gender;

    private LocalDate birthDate;

    private String idNumber;

    private String workStatus;

    private String rfidTag;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

}