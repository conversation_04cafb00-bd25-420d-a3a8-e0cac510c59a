package com.bzlj.craft.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-04-15 16:13
 */
@Data
public class DeviationWarningDTO {
    private String id;

    /**
     * 报警时间
     */
    private LocalDateTime alertTime;

    /**
     * 报警类型 false:系统上报 true人工上报
     */
    private String alertType;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 工序类型，关联字典
     */
    private String processType;


    /**
     * 工步名称
     */
    private String workStepName;

    private String alarmContent;

    /**
     * 优化建议
     */
    private String suggestion;
}
