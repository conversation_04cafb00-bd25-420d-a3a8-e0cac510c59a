package com.bzlj.craft.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 字典项
 * DTO for {@link com.bzlj.craft.entity.SysDictItem}
 */
@Data
public class SysDictItemDTO {

    /**
     * 字典项编码
     */
    String itemCode;

    /**
     * 字典编码
     */
    SysDictDTO dictCode;

    /**
     * 字典项名称
     */
    String itemName;

    /**
     * 排序
     */
    Integer sortOrder;

    /**
     * 是否启用
     */
    Boolean isActive;

    /**
     * 字典项元数据
     */
    Map<String, Object> metadata;

    /**
     * 字典项国际化
     */
    Map<String, Object> i18n;

    /**
     * 有效时间
     */
    LocalDateTime validFrom;

    /**
     * 失效时间
     */
    LocalDateTime validTo;
}