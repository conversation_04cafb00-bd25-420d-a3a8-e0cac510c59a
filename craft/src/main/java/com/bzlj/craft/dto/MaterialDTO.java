package com.bzlj.craft.dto;

import com.bzlj.base.generator.SnowflakeId;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MaterialDTO {
    private String materialId;

    private String steelGrade;

    private String materialType;

    private BigDecimal weight;

    private LocalDateTime createTime;

    private String heatNumber;

    private String status;

    private String materialCode;

    private String materialName;

    private String specification;

    private String materialBatchNo;


    /**
     * 牌号
     */
    private String brand;

    /**
     * 锭型
     */
    private String ingotType;  //规格属性

    /**
     * 锭号
     */
    private String ingotNumber; //规格属性

    /**
     * 供应商
     */
    private String supplier;

}