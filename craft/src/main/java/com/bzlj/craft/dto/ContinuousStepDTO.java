package com.bzlj.craft.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-19 18:12
 */
@Data
public class ContinuousStepDTO {

    private String workStepId;

    private String stepCode;

    private String workStepName;

    private LocalDateTime startTime;

    private LocalDateTime endTime;


    private List<ContinuousParamsDTO> continuousParams;

    private Integer stepOrder;
}
