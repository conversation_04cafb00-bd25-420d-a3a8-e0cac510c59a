package com.bzlj.craft.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 导入结果DTO
 * <AUTHOR>
 * @description: 用于返回Excel导入结果的DTO
 * @date 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportResultDTO {
    
    /**
     * 总记录数
     */
    private int totalCount;
    
    /**
     * 成功导入数量
     */
    private int successCount;
    
    /**
     * 失败数量
     */
    private int failureCount;
    
    /**
     * 更新数量
     */
    private int updateCount;
    
    /**
     * 新增数量
     */
    private int insertCount;
    
    /**
     * 是否全部成功
     */
    private boolean allSuccess;
    
    /**
     * 错误信息列表
     */
    private List<ErrorInfo> errors;
    
    /**
     * 错误信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorInfo {
        /**
         * 行号
         */
        private int rowIndex;
        
        /**
         * 错误字段
         */
        private String field;
        
        /**
         * 错误信息
         */
        private String message;
        
        /**
         * 原始数据
         */
        private String originalData;
    }
}
