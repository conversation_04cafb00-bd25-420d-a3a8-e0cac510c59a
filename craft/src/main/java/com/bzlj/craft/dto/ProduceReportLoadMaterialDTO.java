package com.bzlj.craft.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProduceReportLoadMaterialDTO {
    private String id;

    /**
     * 上料时间
     */
    private LocalDateTime createTime;

    /**
     * 上料人
     */
    private String createByUserName;

    /**
     * 物料号
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 加料量
     */
    private BigDecimal dosage;

    /**
     * 供应商
     */
    private String supplier;

    /**
     * 物料批次
     */
    private String materialBatchNo;

    /**
     * 加料顺序
     */
    private Integer order;

}