package com.bzlj.craft.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-14 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatisticsDTO {
    private Long total;          // 总任务数
    private Long inProgressCount;// 进行中数量
    private Long notStartedCount;// 未开始数量
    private Long completedCount; // 已完成数量
}
