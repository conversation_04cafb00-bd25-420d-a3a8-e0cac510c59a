package com.bzlj.craft.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 扣分明细
 * @date 2025-03-11 17:48
 */
@Data
public class ProcessScoreDetailsDTO {

    private String id;

    private String processStepName;

    private String processParam;

    private String standard;

    private String actual;

    private String deviationDegree;

    private BigDecimal deduct;

    private Integer version;

    private String handle;
    
    private String taskId;

    private String processScoreId;

}
