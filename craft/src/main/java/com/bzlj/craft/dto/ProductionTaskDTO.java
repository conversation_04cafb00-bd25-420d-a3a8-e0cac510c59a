package com.bzlj.craft.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-11 17:48
 */
@Data
public class ProductionTaskDTO {
    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 工艺ID
     */
    private String processId;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 流程类型
     */
    private String processType;

    /**
     * 流程类型
     */
    private String processTypeCode;

    /**
     * 工艺代码
     */
    private String craftCode;

    /**
     * 设备代码
     */
    private String equipmentCode;

    /**
     * 设备名称
     */
    private String equipmentName;

    /**
     * 牌号
     */
    private String brand;

    /**
     * 投入物料
     */
    private String inputMaterialCode;

    /**
     * 产出物料
     */
    private String outputMaterialCode;

    /**
     * 计划开始时间
     */
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime planEndTime;

    /**
     * 实际开始时间
     */
    private LocalDateTime startTime;

    /**
     * 实际结束时间
     */
    private LocalDateTime endTime;

    /**
     * 计划生产重量
     */
    private BigDecimal planWeight;

    /**
     * 计划生产数量
     */
    private Integer planQuantity;

    /**
     * 任务状态
     */
    private String statusCode;

    private BigDecimal score;
}
