package com.bzlj.craft.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
public class OperationLogDTO implements Serializable {
    private String logId;

    private String operatorId;

    private String operationType;

    private LocalDateTime operationTime;

    private Map<String, Object> description;

    private String operator;

}