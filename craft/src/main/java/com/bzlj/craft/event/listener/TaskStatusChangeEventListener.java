package com.bzlj.craft.event.listener;

import com.bzlj.craft.event.TaskStatusChangeEvent;
import com.bzlj.craft.service.ICraftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 任务状态变更事件监听器
 * <p>
 * 监听任务状态变更事件，执行相应的业务逻辑。
 * 这是一个示例监听器，展示了如何处理任务状态变更事件。
 * 实际项目中可以根据业务需求创建多个监听器来处理不同的业务逻辑。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Component
public class TaskStatusChangeEventListener {

    @Autowired
    private ICraftService craftService;

    /**
     * 处理任务状态变更事件
     * <p>
     * 当任务状态发生变更时，此方法会被自动调用。
     * 可以在这里实现各种业务逻辑，如：
     * - 记录状态变更日志
     * - 发送通知消息
     * - 更新相关统计数据
     * - 触发下游业务流程
     * </p>
     *
     * @param event 任务状态变更事件
     */
    @EventListener
    public void handleTaskStatusChangeEvent(TaskStatusChangeEvent event) {
        log.info("接收到任务状态变更事件: {}", event.getChangeDescription());
        
        try {
            // 记录详细的状态变更信息
            logStatusChange(event);
            
            // 根据不同的状态变更执行不同的业务逻辑
            if (event.isTaskStarted()) {
                handleTaskStarted(event);
            } else if (event.isTaskCompleted()) {
                handleTaskCompleted(event);
            }
            
        } catch (Exception e) {
            log.error("处理任务状态变更事件失败: {}", event, e);
        }
    }


    /**
     * 记录状态变更日志
     *
     * @param event 任务状态变更事件
     */
    private void logStatusChange(TaskStatusChangeEvent event) {
        String oldStatusName = event.getOldStatus() != null ? event.getOldStatus().getItemName() : "未知";
        String newStatusName = event.getNewStatus() != null ? event.getNewStatus().getItemName() : "未知";
        
        log.info("任务状态变更详情 - 任务ID: {}, 任务编号: {}, 状态变更: {} -> {}, 变更时间: {}, 操作人: {}, 变更原因: {}", 
                event.getTaskId(), 
                event.getTaskCode(),
                oldStatusName,
                newStatusName,
                event.getChangeTime(),
                event.getOperatorId() != null ? event.getOperatorId() : "系统",
                event.getChangeReason() != null ? event.getChangeReason() : "无");
    }

    /**
     * 处理任务开始事件
     *
     * @param event 任务状态变更事件
     */
    private void handleTaskStarted(TaskStatusChangeEvent event) {
        log.info("任务开始处理: taskCode={}", event.getTaskCode());
        //修改执行工步开始时间
        craftService.updateStepExecTime(event.getTaskId());
    }

    /**
     * 处理任务完成事件
     *
     * @param event 任务状态变更事件
     */
    private void handleTaskCompleted(TaskStatusChangeEvent event) {
        log.info("任务完成处理: taskCode={}", event.getTaskCode());
        craftService.updateStepExecTime(event.getTaskId());
    }

}
