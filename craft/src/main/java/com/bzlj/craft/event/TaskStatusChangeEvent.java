package com.bzlj.craft.event;

import com.bzlj.craft.entity.SysDictItem;
import com.bzlj.craft.enums.TaskStatus;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 任务状态变更事件
 * <p>
 * 当任务状态发生变更时发布此事件，包含任务的基本信息和状态变更详情。
 * 其他模块可以监听此事件来执行相应的业务逻辑，如：
 * - 发送通知
 * - 记录日志
 * - 触发下游流程
 * - 更新统计数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Getter
public class TaskStatusChangeEvent extends ApplicationEvent {

    /**
     * 任务ID
     */
    private final String taskId;

    /**
     * 任务编号
     */
    private final String taskCode;

    /**
     * 变更前的状态
     */
    private final SysDictItem oldStatus;

    /**
     * 变更后的状态
     */
    private final SysDictItem newStatus;

    /**
     * 状态变更时间
     */
    private final LocalDateTime changeTime;

    /**
     * 变更操作的用户ID（可选）
     */
    private final String operatorId;

    /**
     * 变更原因或备注（可选）
     */
    private final String changeReason;

    /**
     * 构造任务状态变更事件
     *
     * @param source       事件源对象
     * @param taskId       任务ID
     * @param taskCode     任务编号
     * @param oldStatus    变更前状态
     * @param newStatus    变更后状态
     * @param operatorId   操作用户ID
     * @param changeReason 变更原因
     */
    public TaskStatusChangeEvent(Object source, String taskId, String taskCode, 
                                SysDictItem oldStatus, SysDictItem newStatus, 
                                String operatorId, String changeReason) {
        super(source);
        this.taskId = taskId;
        this.taskCode = taskCode;
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.changeTime = LocalDateTime.now();
        this.operatorId = operatorId;
        this.changeReason = changeReason;
    }

    /**
     * 构造任务状态变更事件（简化版本）
     *
     * @param source    事件源对象
     * @param taskId    任务ID
     * @param taskCode  任务编号
     * @param oldStatus 变更前状态
     * @param newStatus 变更后状态
     */
    public TaskStatusChangeEvent(Object source, String taskId, String taskCode, 
                                SysDictItem oldStatus, SysDictItem newStatus) {
        this(source, taskId, taskCode, oldStatus, newStatus, null, null);
    }

    /**
     * 获取状态变更描述
     *
     * @return 状态变更描述字符串
     */
    public String getChangeDescription() {
        String oldStatusName = oldStatus != null ? oldStatus.getItemName() : "未知";
        String newStatusName = newStatus != null ? newStatus.getItemName() : "未知";
        return String.format("任务 %s 状态从 %s 变更为 %s", taskCode, oldStatusName, newStatusName);
    }

    /**
     * 判断是否为任务开始事件
     *
     * @return 如果新状态为"进行中"则返回true
     */
    public boolean isTaskStarted() {
        return newStatus != null && TaskStatus.in_progress.getCode().equals(newStatus.getItemCode());
    }

    /**
     * 判断是否为任务完成事件
     *
     * @return 如果新状态为"已完成"则返回true
     */
    public boolean isTaskCompleted() {
        return newStatus != null && TaskStatus.completed.getCode().equals(newStatus.getItemCode());
    }


    @Override
    public String toString() {
        return String.format("TaskStatusChangeEvent{taskId='%s', taskCode='%s', oldStatus='%s', newStatus='%s', changeTime=%s}", 
                           taskId, taskCode, 
                           oldStatus != null ? oldStatus.getItemCode() : null,
                           newStatus != null ? newStatus.getItemCode() : null,
                           changeTime);
    }
}
