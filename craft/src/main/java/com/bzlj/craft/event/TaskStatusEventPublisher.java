package com.bzlj.craft.event;

import com.bzlj.craft.entity.SysDictItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

/**
 * 任务状态事件发布服务
 * <p>
 * 负责发布任务状态变更相关的事件，提供统一的事件发布接口。
 * 使用Spring的ApplicationEventPublisher来发布事件，确保事件能够被
 * 所有注册的监听器接收和处理。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
public class TaskStatusEventPublisher {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 发布任务状态变更事件
     *
     * @param taskId       任务ID
     * @param taskCode     任务编号
     * @param oldStatus    变更前状态
     * @param newStatus    变更后状态
     * @param operatorId   操作用户ID
     * @param changeReason 变更原因
     */
    public void publishTaskStatusChangeEvent(String taskId, String taskCode, 
                                           SysDictItem oldStatus, SysDictItem newStatus,
                                           String operatorId, String changeReason) {
        try {
            TaskStatusChangeEvent event = new TaskStatusChangeEvent(
                this, taskId, taskCode, oldStatus, newStatus, operatorId, changeReason
            );
            
            log.info("发布任务状态变更事件: {}", event.getChangeDescription());
            applicationEventPublisher.publishEvent(event);
            log.debug("任务状态变更事件发布成功: {}", event);
            
        } catch (Exception e) {
            log.error("发布任务状态变更事件失败: taskId={}, taskCode={}, oldStatus={}, newStatus={}", 
                     taskId, taskCode, 
                     oldStatus != null ? oldStatus.getItemCode() : null,
                     newStatus != null ? newStatus.getItemCode() : null, e);
        }
    }

    /**
     * 发布任务状态变更事件（简化版本）
     *
     * @param taskId    任务ID
     * @param taskCode  任务编号
     * @param oldStatus 变更前状态
     * @param newStatus 变更后状态
     */
    public void publishTaskStatusChangeEvent(String taskId, String taskCode, 
                                           SysDictItem oldStatus, SysDictItem newStatus) {
        publishTaskStatusChangeEvent(taskId, taskCode, oldStatus, newStatus, null, null);
    }

}
