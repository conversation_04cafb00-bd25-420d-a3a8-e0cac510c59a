# 任务状态变更事件系统

## 概述

本模块实现了任务状态变更时的事件发布机制，当任务状态发生变更时会自动发布相应的事件，其他模块可以监听这些事件来执行相应的业务逻辑。

## 核心组件

### 1. TaskStatusChangeEvent (事件类)
- **位置**: `com.bzlj.craft.event.TaskStatusChangeEvent`
- **功能**: 封装任务状态变更的详细信息
- **主要属性**:
  - `taskId`: 任务ID
  - `taskCode`: 任务编号
  - `oldStatus`: 变更前状态
  - `newStatus`: 变更后状态
  - `changeTime`: 变更时间
  - `operatorId`: 操作用户ID（可选）
  - `changeReason`: 变更原因（可选）

### 2. TaskStatusEventPublisher (事件发布器)
- **位置**: `com.bzlj.craft.event.TaskStatusEventPublisher`
- **功能**: 负责发布任务状态变更事件
- **主要方法**:
  - `publishTaskStatusChangeEvent()`: 发布状态变更事件
  - `publishTaskStatusChangeEventAsync()`: 异步发布事件

### 3. TaskStatusChangeEventListener (事件监听器)
- **位置**: `com.bzlj.craft.event.listener.TaskStatusChangeEventListener`
- **功能**: 示例事件监听器，展示如何处理状态变更事件
- **处理逻辑**:
  - 记录状态变更日志
  - 根据不同状态执行相应业务逻辑
  - 支持同步和异步处理

## 使用方式

### 1. 自动事件发布
当调用 `SurveillanceService.changeStatus()` 方法更新任务状态时，系统会自动发布状态变更事件，无需额外代码。

```java
// 更新任务状态，会自动发布事件
surveillanceService.changeStatus("TASK001", newStatusDict);
```

### 2. 创建自定义事件监听器
```java
@Component
@Slf4j
public class CustomTaskStatusListener {
    
    @EventListener
    public void handleTaskStatusChange(TaskStatusChangeEvent event) {
        log.info("处理任务状态变更: {}", event.getChangeDescription());
        
        if (event.isTaskStarted()) {
            // 处理任务开始逻辑
            handleTaskStart(event);
        } else if (event.isTaskCompleted()) {
            // 处理任务完成逻辑
            handleTaskComplete(event);
        }
    }
    
    @Async("eventTaskExecutor")
    @EventListener
    public void handleTaskStatusChangeAsync(TaskStatusChangeEvent event) {
        // 异步处理耗时操作
        sendNotification(event);
    }
}
```

### 3. 手动发布事件
```java
@Autowired
private TaskStatusEventPublisher eventPublisher;

public void someMethod() {
    // 手动发布事件
    eventPublisher.publishTaskStatusChangeEvent(
        taskId, taskCode, oldStatus, newStatus, operatorId, reason
    );
}
```

## 配置说明

### 异步处理配置
系统提供了专门的线程池用于异步事件处理：

- **配置类**: `AsyncEventConfig`
- **线程池名称**: `eventTaskExecutor`
- **默认配置**:
  - 核心线程数: 2
  - 最大线程数: 8
  - 队列容量: 100
  - 线程空闲时间: 60秒

可以通过修改 `AsyncEventConfig` 类来调整线程池参数。

## 事件类型判断

`TaskStatusChangeEvent` 提供了便捷的方法来判断事件类型：

```java
if (event.isTaskStarted()) {
    // 任务开始事件 (状态变更为 in_progress)
}

if (event.isTaskCompleted()) {
    // 任务完成事件 (状态变更为 completed)
}

if (event.isTaskCancelled()) {
    // 任务取消事件 (状态变更为 cancelled)
}
```

## 最佳实践

### 1. 事件监听器设计
- **单一职责**: 每个监听器专注处理特定的业务逻辑
- **异常处理**: 监听器中要做好异常处理，避免影响其他监听器
- **性能考虑**: 耗时操作使用异步处理

### 2. 事件处理顺序
- 同步监听器按照Spring的默认顺序执行
- 可以使用 `@Order` 注解控制执行顺序
- 异步监听器的执行顺序不确定

### 3. 错误处理
- 事件发布失败不会影响主业务流程
- 监听器异常不会影响其他监听器的执行
- 建议在监听器中添加适当的日志记录

## 扩展点

### 1. 添加新的事件类型
可以参考 `TaskStatusChangeEvent` 创建其他类型的事件，如：
- 任务创建事件
- 任务删除事件
- 工步状态变更事件

### 2. 集成外部系统
可以在事件监听器中集成外部系统：
- 发送邮件通知
- 调用第三方API
- 更新缓存或搜索引擎

### 3. 事件持久化
可以添加监听器将重要事件持久化到数据库，用于审计和追踪。

## 测试

提供了完整的单元测试：
- **测试类**: `TaskStatusEventTest`
- **测试覆盖**: 事件创建、发布、监听等核心功能

运行测试：
```bash
mvn test -Dtest=TaskStatusEventTest
```

## 注意事项

1. **事务边界**: 事件在事务提交后发布，确保数据一致性
2. **性能影响**: 同步监听器会影响主流程性能，建议使用异步处理
3. **内存使用**: 大量事件可能影响内存，注意监控和调优
4. **依赖关系**: 避免监听器之间的强依赖关系
