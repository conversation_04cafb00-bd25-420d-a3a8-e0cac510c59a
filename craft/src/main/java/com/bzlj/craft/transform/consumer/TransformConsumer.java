package com.bzlj.craft.transform.consumer;

import com.bzlj.craft.transform.common.AbsCommonConsumer;
import com.bzlj.craft.transform.common.ForwardMessageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.Consumer;

@Component
@Slf4j
@RequiredArgsConstructor
public class TransformConsumer extends AbsCommonConsumer {

    /**
     * 实时数据消费者
     */
    @Bean
    public Consumer<List<ForwardMessageInfo<?>>> transformGyckConsumer() {
        return this::handleDataList;
    }

    private void handleDataList(List<ForwardMessageInfo<?>> nodes) {
        nodes.forEach(p -> {
            handleData(p, p.getServiceId());
        });
    }
}
