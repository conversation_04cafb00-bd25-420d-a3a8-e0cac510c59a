package com.bzlj.craft.transform.cache;

import com.bzlj.craft.transform.enums.ParamConfirmed;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.bzlj.craft.transform.enums.ParamConfirmed.pending;


@Service
public class ParamCacheManagerService {
    // 共享缓存：存储已确认的工艺参数信息
    private final Cache<String, ParamConfirmed> confirmedParamsCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 10分钟过期
            .maximumSize(1000)                     // 最大缓存条目
            .build();
    
    // 共享缓存：存储等待确认的工艺参数信息
    private final Cache<String, JsonNode> pendingParamsCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 10分钟过期
            .maximumSize(1000)
            .build();

    // 添加确认ID
    public void addConfirmedParamsCache(String matNo, String orderNo,  ParamConfirmed confirmed) {
        confirmedParamsCache.put(String.format("%s_%s", matNo, orderNo), confirmed);
    }



    // 检查是否已确认
    public ParamConfirmed isProcessConfirmed(String matNo, String orderNo) {
        ParamConfirmed paramConfirmed = confirmedParamsCache.getIfPresent(String.format("%s_%s", matNo, orderNo));
        if(Objects.isNull(paramConfirmed)) return pending;
        return paramConfirmed;
    }

    public void removeConfirmedParamsCache(String matNo, String orderNo) {
        ParamConfirmed paramConfirmed = confirmedParamsCache.getIfPresent(String.format("%s_%s", matNo, orderNo));
        if(paramConfirmed != null){
            confirmedParamsCache.invalidate(String.format("%s_%s", matNo, orderNo));
        }
    }

    // 添加待处理工艺信息
    public void addPendingProcessParams(String matNo, String orderNo, JsonNode jsonNode) {
        pendingParamsCache.put(String.format("%s_%s", matNo, orderNo), jsonNode);
    }

    // 获取并移除待处理工艺参数
    public JsonNode getAndRemovePendingParam(String matNo, String orderNo) {
        JsonNode jsonNode = pendingParamsCache.getIfPresent(String.format("%s_%s", matNo, orderNo));
        if (!Objects.isNull(jsonNode)) {
            pendingParamsCache.invalidate(String.format("%s_%s", matNo, orderNo));
        }
        return jsonNode;
    }
}