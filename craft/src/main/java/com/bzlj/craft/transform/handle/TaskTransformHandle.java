package com.bzlj.craft.transform.handle;


import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.model.DataFlowStatus;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.entity.Telegram;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.transform.service.TaskTransformService;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 任务分发转换处理器
 * <p>
 * 负责处理任务分发消息的转换和处理，主要功能包括：
 * 1. 解析任务分发JSON消息
 * 2. 创建生产任务及相关实体
 * 3. 建立任务与设备的关联关系
 * 4. 生成执行工步
 * 5. 处理任务扩展属性
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-06 10:42
 */
@Slf4j
@MessageHandler(messageType = "task_Distribute", desc = "任务转化")
public class TaskTransformHandle extends CommonHandler<String> {

    /**
     * 需要为 payload 添加 taskCode 字段的 Telegram serviceId 集合
     */
    private static final Set<String> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE = Set.of(
            // 在这里添加需要特殊处理的 serviceId
            "MTHE04__pipe_craft_params"
    );

    /**
     * 电报仓储
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 消息事件生产者
     */
    @Autowired
    private MessageEventProducer messageEventProducer;


    @Autowired
    private TaskTransformService taskTransformService;


    /**
     * 任务转换处理方法
     * <p>
     * 解析任务分发JSON消息，创建生产任务及相关实体。处理流程：
     * 1. 验证分厂、工序等基础数据
     * 2. 检查任务是否已存在
     * 3. 创建生产任务实体
     * 4. 处理任务扩展属性
     * 5. 建立任务与设备的关联
     * 6. 生成执行工步
     * </p>
     *
     * @param json 任务分发JSON字符串，支持单个任务或任务数组
     * @throws RuntimeException 当必要字段缺失或基础数据不存在时抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext){
        taskTransformService.taskTransform(json, handleContext);
    }

    /**
     * 获取关联ID列表
     * <p>
     * 从JSON消息中提取任务编号和工艺编号作为关联ID，
     * 用于后续的历史数据处理
     * </p>
     *
     * @param json JSON消息字符串
     * @return 关联ID列表，包含任务编号和工艺编号
     */
    @Override
    public List<String> getRelationIds(String json) {
        List<String> relationIds = new ArrayList<>();
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        if(jsonNode instanceof ArrayNode){
            jsonNode.forEach(node -> {
                if(Objects.nonNull(node.get("taskCode"))){
                    relationIds.add(node.get("taskCode").asText());
                }
                if(Objects.nonNull(node.get("craftCode"))){
                    relationIds.add(node.get("craftCode").asText());
                }
            });
        }
        return relationIds;
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除对应的电报记录
     * </p>
     *
     * @param telegramId 电报ID
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 根据关联ID列表查找相关的历史电报，
     * 并重新推送这些电报消息进行处理。
     * 对于特定的 serviceId，会为 payload 添加 taskCode 字段。
     * </p>
     *
     * @param relationIds 关联ID列表
     * @param handleContext 处理上下文，包含 taskCode 集合
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        if(CollectionUtils.isEmpty(relationIds)) return;
        //获取当前正在消费的消息
        String messageId = (String) handleContext.get("messageId");
        if(StringUtils.isEmpty(messageId)){
            throw new RuntimeException("当前消费id为空，请检查配置防止重复消费");
        }
        List<Telegram> telegrams = telegramRepository.findByRelationIdInAndIdIsNot(relationIds,messageId);
        if(CollectionUtils.isEmpty(telegrams)) return;

        // 从 handleContext 获取 taskCode 集合
        @SuppressWarnings("unchecked")
        List<String> taskCodes = (List<String>) handleContext.getOrDefault("taskCode", new ArrayList<>());

        // 分离需要和不需要 taskCode 的 telegram
        List<Telegram> telegramsNotRequiringTaskCode = telegrams.stream()
                .filter(telegram -> !TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE.contains(telegram.getServiceId()))
                .toList();

        List<Telegram> telegramsRequiringTaskCode = telegrams.stream()
                .filter(telegram -> TELEGRAM_SERVICE_IDS_REQUIRING_TASK_CODE.contains(telegram.getServiceId()))
                .toList();

        // 处理不需要 taskCode 的 telegram
        telegramsNotRequiringTaskCode.forEach(telegram -> {
            MessageEvent event = new MessageEvent();
            event.setMessageId(telegram.getId());
            event.setMessageType(telegram.getType());
            event.setPayload(telegram.getPayload());
            event.setDataFlowStatus(DataFlowStatus.inner);
            messageEventProducer.pushMessage(event);
        });

        // 处理需要 taskCode 的 telegram - 为每个 taskCode 创建一个消息
        if (!CollectionUtils.isEmpty(taskCodes) && !CollectionUtils.isEmpty(telegramsRequiringTaskCode)) {
            telegramsRequiringTaskCode.forEach(telegram -> {
                taskCodes.forEach(taskCode -> {
                    MessageEvent event = new MessageEvent();
                    event.setMessageId(telegram.getId());
                    event.setMessageType(telegram.getType());
                    event.setDataFlowStatus(DataFlowStatus.inner);
                    // 为 payload 添加当前的 taskCode
                    String payload = addTaskCodeToPayload(telegram.getPayload(), taskCode);
                    event.setPayload(payload);
                    messageEventProducer.pushMessage(event);
                });
            });
        }
    }



    /**
     * 为 payload 添加 taskCode 字段
     * <p>
     * 根据 payload 的类型（单个对象或数组），为其添加 taskCode 字段。
     * 如果是数组，则为数组中的每个元素添加相同的 taskCode 字段。
     * </p>
     *
     * @param payload 原始 payload 字符串
     * @param taskCode 要添加的 taskCode
     * @return 修改后的 payload 字符串
     */
    private String addTaskCodeToPayload(String payload, String taskCode) {
        try {
            JsonNode payloadNode = JsonUtils.toJsonNode(payload);

            if (payloadNode instanceof ArrayNode) {
                // 如果是数组，为每个元素添加相同的 taskCode
                ArrayNode arrayNode = (ArrayNode) payloadNode;
                for (JsonNode elementNode : arrayNode) {
                    if (elementNode instanceof ObjectNode) {
                        ((ObjectNode) elementNode).put("taskCode", taskCode);
                    }
                }
            } else if (payloadNode instanceof ObjectNode) {
                // 如果是单个对象，添加 taskCode
                ObjectNode objectNode = (ObjectNode) payloadNode;
                objectNode.put("taskCode", taskCode);
            }

            // 将修改后的 JsonNode 转换回字符串
            return JsonUtils.toJson(payloadNode);
        } catch (Exception e) {
            log.error("为 payload 添加 taskCode 字段时发生错误: {}", e.getMessage(), e);
            // 如果处理失败，返回原始 payload
            return payload;
        }
    }


}
