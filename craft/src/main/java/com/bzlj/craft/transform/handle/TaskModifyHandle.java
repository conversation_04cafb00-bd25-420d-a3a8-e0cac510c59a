package com.bzlj.craft.transform.handle;


import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.service.TaskModifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 任务属性修改处理器
 * <p>
 * 负责处理任务属性修改消息的转换和处理，主要功能包括：
 * 1. 接收任务属性修改消息
 * 2. 动态更新任务的各种属性
 * 3. 处理任务扩展属性的更新
 * 4. 支持批量任务属性修改
 * 5. 支持多种数据类型的属性转换
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@MessageHandler(messageType = "task_modify", desc = "任务属性修改")
public class TaskModifyHandle extends CommonHandler<String> {


    @Autowired
    private TaskModifyService taskModifyService;

    /**
     * 转换处理任务属性修改消息
     * <p>
     * 处理任务属性修改JSON数据的核心方法，包括以下步骤：
     * 1. 支持数组格式的批量处理
     * 2. 解析任务编码并查询对应任务
     * 3. 动态更新任务属性
     * 4. 保存更新后的任务数据
     * </p>
     *
     * @param json 任务属性修改的JSON字符串
     * @throws RuntimeException 当任务编码为空或任务不存在时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext){
        taskModifyService.taskModifyTransform(json, handleContext);
    }


    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    /**
     * 清理关联数据
     * <p>
     * 当前实现为空，暂无需清理关联数据
     * </p>
     *
     * @param telegramId 电报ID
     */
    @Override
    public void clearRelationData(String telegramId) {
        // 暂无需清理关联数据
    }


}
