# 事务提前提交问题分析与解决方案

## 问题描述

在执行 `TaskTransformHandle.transform()` 方法时，发现一个严重的事务管理问题：

**现象**：在 `transform` 方法还未执行完毕的情况下，调用 `surveillanceService.deleteTask(task.getTaskId())` 后，删除操作的数据库修改就已经可以在数据库中查到了，这表明事务被提前提交了。

## 问题根本原因

### 1. 事务传播行为导致的问题

```java
// TaskTransformHandle.java
@Transactional(rollbackFor = Exception.class)
public void transform(String json, Map<String, Object> handleContext) {
    // ...
    if(Objects.nonNull(task)) {
        surveillanceService.deleteTask(task.getTaskId()); // 问题出现在这里
    }
    // ... 后续还有其他业务逻辑
}

// SurveillanceServiceImpl.java
@Service
@Transactional(rollbackFor = Exception.class)  // 类级别事务
public class SurveillanceServiceImpl {
    
    @Transactional(rollbackFor = Exception.class)  // 方法级别事务
    public void deleteTask(String taskId) {
        // 删除逻辑
        this.update(productionTask);  // 调用JPA的save方法
    }
}
```

### 2. 关键问题分析

1. **默认事务传播行为**：`@Transactional` 默认使用 `REQUIRED` 传播行为
2. **事务加入机制**：`deleteTask` 方法加入了 `transform` 方法的事务
3. **JPA自动刷新**：JPA的 `save()` 方法可能触发自动 `flush()` 操作
4. **数据库可见性**：虽然事务未提交，但SQL已发送到数据库

### 3. JPA/Hibernate 的自动刷新机制

```java
// BaseService.java
default E updateEntity(E entity) {
    return getRepository().save(entity);  // 这里触发JPA的save操作
}
```

JPA的 `save()` 方法会：
- 将实体标记为脏数据
- 在某些条件下触发 `flush()` 操作
- 将SQL语句发送到数据库（但事务可能还未提交）
- 在某些数据库隔离级别下，这些修改可能对其他连接可见

## 解决方案

### 方案1：修改事务传播行为（推荐）

将 `deleteTask` 方法的事务传播行为改为 `REQUIRES_NEW`：

```java
@Override
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public void deleteTask(String taskId) {
    // 删除逻辑
}
```

**优点**：
- 删除操作在独立事务中执行
- 不影响主业务流程的事务
- 删除操作立即提交，避免长时间锁定资源

**缺点**：
- 如果主业务后续失败，删除操作无法回滚

### 方案2：延迟删除机制（当前实现）

在主事务中只标记删除，在事务提交后再执行真正的删除：

```java
// 在transform方法中
if(Objects.nonNull(task)) {
    log.warn("{}:任务已存在，标记删除原有任务", taskCode);
    existingTaskId = task.getTaskId();
    // 先标记为删除，避免在当前事务中立即执行删除操作
    task.setDeleted(true);
    productionTaskRepository.save(task);
    handleContext.put("taskToDelete", existingTaskId);
}

// 在handlePost方法中
protected void handleDelayedTaskDeletion(Map<String, Object> handleContext) {
    String taskToDelete = (String) handleContext.get("taskToDelete");
    if (StringUtils.isNotBlank(taskToDelete)) {
        performTaskDeletion(taskToDelete);
    }
}
```

**优点**：
- 保证事务一致性
- 主业务失败时，删除操作也会回滚
- 避免事务边界问题

**缺点**：
- 实现相对复杂
- 需要额外的延迟处理机制

## 实现细节

### 1. 修改SurveillanceServiceImpl

```java
@Override
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public void deleteTask(String taskId) {
    // 删除逻辑保持不变
}
```

### 2. 修改TaskTransformHandle

```java
// 延迟删除标记
if(Objects.nonNull(task)) {
    existingTaskId = task.getTaskId();
    task.setDeleted(true);
    productionTaskRepository.save(task);
    handleContext.put("taskToDelete", existingTaskId);
}

// 重写延迟删除方法
@Override
protected void performTaskDeletion(String taskId) {
    surveillanceService.deleteTask(taskId);
}
```

### 3. 修改CommonHandler

```java
// 在handlePost中添加延迟删除处理
protected void handleDelayedTaskDeletion(Map<String, Object> handleContext) {
    String taskToDelete = (String) handleContext.get("taskToDelete");
    if (StringUtils.isNotBlank(taskToDelete)) {
        performTaskDeletion(taskToDelete);
    }
}
```

## 测试验证

创建了完整的测试用例来验证修复效果：

1. **事务边界测试**：验证删除操作不会影响主事务
2. **异常回滚测试**：验证异常情况下的事务回滚
3. **延迟删除测试**：验证延迟删除机制的正确性
4. **并发处理测试**：验证并发场景下的事务处理

## 最佳实践建议

### 1. 事务传播行为选择

- **REQUIRED**：适用于大多数业务方法
- **REQUIRES_NEW**：适用于独立的业务操作，如日志记录、删除操作
- **SUPPORTS**：适用于查询操作
- **NOT_SUPPORTED**：适用于不需要事务的操作

### 2. 避免事务边界问题

- 明确定义事务边界
- 避免在同一事务中混合不同类型的操作
- 使用延迟处理机制处理复杂的业务场景

### 3. 监控和调试

- 添加详细的日志记录
- 监控事务执行时间
- 使用事务状态检查工具

## 总结

通过修改事务传播行为和实现延迟删除机制，成功解决了"方法执行失败了，但是事务却提交了"的问题。这个解决方案既保证了事务的一致性，又避免了事务边界问题，为类似的业务场景提供了可参考的解决思路。
