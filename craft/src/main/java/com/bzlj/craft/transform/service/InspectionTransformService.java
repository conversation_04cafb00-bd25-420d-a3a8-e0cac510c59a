package com.bzlj.craft.transform.service;

import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 检化验数据转换处理器
 * <p>
 * 负责处理检化验数据消息的转换和处理，主要功能包括：
 * 1. 接收检化验数据消息
 * 2. 解析任务编码并查询对应的生产任务
 * 3. 处理检化验数据的转换逻辑（待实现）
 * 4. 清理相关的电报数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class InspectionTransformService {


    /**
     * 监控服务，用于查询和管理生产任务
     */
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * 转换处理检化验数据消息
     * <p>
     * 接收检化验数据的JSON消息，解析任务编码并查询对应的生产任务
     * 注意：当前实现仅包含任务查询逻辑，具体的检化验数据处理逻辑待实现
     * </p>
     *
     * @param json 检化验数据的JSON字符串
     */
    @Transactional(rollbackFor = Exception.class)
    public void inspectionTransform(String json, Map<String, Object> handleContext) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        // 查询任务
        String taskCode = jsonNode.get("taskCode").asText();
        ProductionTask task = surveillanceService.findOneByTaskCode(taskCode);

        // TODO: 实现具体的检化验数据处理逻辑
    }

}
