package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.transform.service.LoadMaterialTransformService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 上料记录转换处理器
 * <p>
 * 负责处理上料记录消息的转换和处理，主要功能包括：
 * 1. 接收上料记录数据消息
 * 2. 解析物料信息并创建物料实体
 * 3. 处理物料属性和上料报告关联关系
 * 4. 支持批量上料记录数据处理
 * 5. 管理生产报告中的上料物料信息
 * </p>
 * <p>
 * 特别适用于特冶感应炉原料消耗实绩的处理场景
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@MessageHandler(messageType = "load_material", desc = "上料记录转化")
public class LoadMaterialTransformHandle extends CommonHandler<String> {

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    @Autowired
    private LoadMaterialTransformService loadMaterialTransformService;

    /**
     * 转换处理上料记录消息
     * <p>
     * 接收上料记录的JSON消息，调用具体的上料记录转换处理方法
     * </p>
     *
     * @param s 上料记录的JSON字符串
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String s, Map<String, Object> handleContext) {
        loadMaterialTransformService.loadMaterialTransform(s);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }


    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }
}
