package com.bzlj.craft.transform.entity;

import com.bici.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/7/2 14:27
 */
@Accessors(chain = true)
@Document(collection = "telegram_config")
@Data
public class TelegramConfig implements Serializable {

    @Id
    private String id;

    @Excel(name = "服务ID")
    private String serviceId;

    /**
     * 关联id
     */
    @Excel(name = "关联字段")
    private String relationField;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @CreatedDate
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @LastModifiedDate
    private Date updateTime;

    /**
     * 电文类型，上料/产出/投入
     */
    @Excel(name = "电文类型")
    private String type;
}
