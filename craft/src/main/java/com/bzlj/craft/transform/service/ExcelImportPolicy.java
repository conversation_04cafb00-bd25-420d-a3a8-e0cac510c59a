package com.bzlj.craft.transform.service;

import com.bzlj.craft.entity.CraftProcess;
import com.bzlj.craft.entity.Equipment;
import lombok.Data;
import org.apache.poi.ss.usermodel.Cell;

import java.io.InputStream;
import java.util.List;

public interface ExcelImportPolicy<T> {

    ImportResult<T> importFromExcel(InputStream inputStream);

    List<T> parseExcel(InputStream inputStream);

    default String getCellStringValue(Cell cell) {
        if (cell == null) return "";
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().trim();
            case NUMERIC -> String.valueOf((int) cell.getNumericCellValue());
            default -> "";
        };
    }

    @Data
    class ImportResult<T>{
        /**
         * 导入数量
         */
        private Integer size;

        private List<T> results;
    }
}
