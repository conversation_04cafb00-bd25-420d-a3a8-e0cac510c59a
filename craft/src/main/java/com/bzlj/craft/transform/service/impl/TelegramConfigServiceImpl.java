package com.bzlj.craft.transform.service.impl;

import com.bici.common.core.utils.StringUtils;
import com.bzlj.craft.transform.cache.TelegramConfigCache;
import com.bzlj.craft.transform.entity.TelegramConfig;
import com.bzlj.craft.transform.repository.TelegramConfigRepository;
import com.bzlj.craft.transform.service.ITelegramConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TelegramConfigServiceImpl implements ITelegramConfigService {

    private final TelegramConfigRepository telegramConfigRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importSyncConfig(List<TelegramConfig> telegramConfigs) {
        // 校验导入的配置
        checkConfig(telegramConfigs);

        // 获取所有受影响的serviceId，用于后续缓存刷新
        List<String> affectedServiceIds = telegramConfigs.stream()
                .map(config -> String.format("%s__%s", config.getServiceId(), config.getType()))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();

        // 获取id集合
        List<String> ids = telegramConfigs.stream().map(TelegramConfig::getId).toList();
        // 删除原有配置
        telegramConfigRepository.deleteAllById(ids);
        // 插入新配置
        for (TelegramConfig telegramConfig : telegramConfigs) {
            telegramConfigRepository.insert(telegramConfig);
        }

        // 刷新受影响的缓存
        int refreshedCount = refreshCacheForServiceIds(affectedServiceIds);

        log.info("成功导入{}条同步配置，涉及{}个服务，实际刷新缓存{}个",
                telegramConfigs.size(), affectedServiceIds.size(), refreshedCount);

        return String.format("成功导入%d条配置，刷新了%d个服务的缓存",
                telegramConfigs.size(), refreshedCount);
    }


    /**
     * 校验导入的配置
     *
     * @param telegramConfigs 同步配置列表
     */
    private static void checkConfig(List<TelegramConfig> telegramConfigs) {
        if (CollectionUtils.isEmpty(telegramConfigs)) {
            throw new RuntimeException("导入配置为空！");
        }
        long countServiceIds = telegramConfigs
                .stream()
                .map(TelegramConfig::getServiceId)
                .filter(StringUtils::isNotBlank)
                .count();
        // 判断serviceId是否存在未填写的
        if (countServiceIds != telegramConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的服务id！");
        }
        // 判断是否存在未填写的sourceField
        long countTypes = telegramConfigs
                .stream()
                .map(TelegramConfig::getType)
                .filter(StringUtils::isNotBlank)
                .count();
        if (countTypes != telegramConfigs.size()) {
            throw new RuntimeException("导入配置存在未填写的类型！");
        }
        long countRes = telegramConfigs
                .stream()
                .map(TelegramConfig::getRelationField)
                .filter(StringUtils::isNotBlank)
                .count();
        if (countRes != telegramConfigs.size()) {
            throw new RuntimeException("关联字段！");
        }
        //判断是否存在重复的serviceId 组合
        long count = telegramConfigs
                .stream()
                .map(telegramConfig -> StringUtils.join(telegramConfig.getServiceId(),
                        "_",
                        telegramConfig.getType(),
                        "-",
                        telegramConfig.getRelationField()))
                .distinct().count();
        if (count != telegramConfigs.size()) {
            throw new RuntimeException("导入配置存在重复的服务id和关联字段、类型组合！");
        }
        // 初始化id
        telegramConfigs.parallelStream().forEach(telegramConfig -> {
            if (StringUtils.isBlank(telegramConfig.getId())) {
                telegramConfig.setId(StringUtils.join(telegramConfig.getServiceId(),
                        "_",
                        telegramConfig.getType(),
                        "_",
                        telegramConfig.getRelationField()));
            }
        });
    }

    /**
     * 刷新指定服务ID列表的缓存
     *
     * @param keys 服务ID列表
     * @return 实际刷新成功的数量
     */
    private int refreshCacheForServiceIds(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            log.debug("没有需要刷新的缓存");
            return 0;
        }

        log.info("开始刷新{}个服务的缓存: {}", keys.size(), keys);

        int successCount = 0;
        int failCount = 0;

        for (String key : keys) {
            try {
                // 刷新缓存
                TelegramConfigCache.refresh(key);
                successCount++;
                log.debug("成功刷新缓存, key: {}", key);
            } catch (Exception e) {
                failCount++;
                log.error("刷新缓存失败, key: {}", key, e);
                // 如果刷新失败，尝试移除缓存，让下次访问时重新加载
                try {
                    TelegramConfigCache.remove(key);
                    log.debug("移除缓存成功, serviceId: {}", key);
                } catch (Exception removeEx) {
                    log.error("移除缓存也失败, serviceId: {}", key, removeEx);
                }
            }
        }

        log.info("缓存刷新完成: 成功{}个, 失败{}个", successCount, failCount);
        return successCount;
    }

}

