# 事务管理最佳实践

## 问题描述

在消息处理框架中，经常遇到"方法执行失败了，但是事务却提交了"的问题。这通常是由于事务边界不清晰、异步处理、或者事务传播机制配置不当导致的。

## 问题原因分析

### 1. 事务边界问题
- 主业务逻辑和后续处理（如消息发送、数据清理）在同一个事务中
- 后续处理失败时，可能不会触发主业务的回滚

### 2. 异步处理问题
- 消息发送等操作使用异步处理，脱离了原始事务上下文
- 异步操作的异常不会影响主事务

### 3. 事务传播机制问题
- 没有正确配置事务传播行为
- 嵌套事务的回滚策略不明确

## 解决方案

### 1. 事务配置优化

#### 启用事务管理
```java
@Configuration
@EnableTransactionManagement(
    proxyTargetClass = true,  // 使用CGLIB代理
    order = 1                 // 设置执行顺序
)
public class TransactionConfig {
    // 配置类
}
```

#### 事务注解配置
```java
@Transactional(
    rollbackFor = Exception.class,    // 所有异常都回滚
    propagation = Propagation.REQUIRED, // 事务传播行为
    timeout = 30                      // 事务超时时间
)
public void businessMethod() {
    // 业务逻辑
}
```

### 2. 事务边界分离

#### 主业务事务
```java
@Transactional(rollbackFor = Exception.class)
public void transform(String json, Map<String, Object> handleContext) {
    try {
        // 核心业务逻辑
        processMainBusiness(json);
    } catch (Exception e) {
        log.error("主业务处理失败: {}", e.getMessage(), e);
        throw e; // 确保异常被抛出以触发回滚
    }
}
```

#### 后续处理独立事务
```java
@Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
    // 在新事务中处理历史数据
    // 即使失败也不影响主业务
}
```

### 3. 使用事务工具类

#### 在新事务中执行
```java
transactionUtils.executeInNewTransaction(() -> {
    // 在独立事务中执行操作
    messageEventProducer.pushMessage(event);
});
```

#### 以非事务方式执行
```java
transactionUtils.executeWithoutTransaction(() -> {
    // 以非事务方式执行，如日志记录
    logService.recordOperation(operation);
});
```

### 4. 异常处理策略

#### 主业务异常处理
```java
@Transactional(rollbackFor = Exception.class)
public void transform(String json, Map<String, Object> handleContext) {
    try {
        // 业务逻辑
    } catch (Exception e) {
        log.error("业务处理失败: {}", e.getMessage(), e);
        // 确保异常被抛出以触发事务回滚
        throw e;
    }
}
```

#### 后续处理异常处理
```java
public void handlePost(MessageEvent<T> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
    try {
        // 后续处理
        dealLegacyData(getRelationIds(event.getPayload()), currentHandleContext);
        clearRelationData(event.getMessageId());
    } catch (Exception e) {
        log.error("后续处理失败: {}", e.getMessage(), e);
        // 后续处理失败不应该影响主业务流程
    }
}
```

## 事务传播行为选择

### REQUIRED（默认）
- 如果当前存在事务，则加入该事务
- 如果当前没有事务，则创建新事务
- 适用于大多数业务方法

### REQUIRES_NEW
- 总是创建新事务
- 如果当前存在事务，则暂停当前事务
- 适用于独立的业务操作，如日志记录、消息发送

### SUPPORTS
- 如果当前存在事务，则加入该事务
- 如果当前没有事务，则以非事务方式执行
- 适用于查询操作

### NOT_SUPPORTED
- 以非事务方式执行
- 如果当前存在事务，则暂停当前事务
- 适用于不需要事务保护的操作

## 最佳实践

### 1. 事务边界清晰
- 主业务逻辑使用一个事务
- 后续处理使用独立事务或非事务方式
- 避免在同一个事务中混合不同类型的操作

### 2. 异常处理完整
- 主业务异常必须抛出以触发回滚
- 后续处理异常要妥善处理，不影响主业务
- 记录详细的错误日志

### 3. 性能考虑
- 避免长时间运行的事务
- 使用只读事务优化查询性能
- 合理设置事务超时时间

### 4. 测试验证
- 编写事务测试用例
- 验证正常提交和异常回滚
- 测试并发事务场景

## 监控和调试

### 1. 日志记录
```java
log.debug("开始执行事务操作");
log.debug("事务操作执行成功");
log.error("事务操作执行失败: {}", e.getMessage(), e);
```

### 2. 事务状态检查
```java
boolean inTransaction = transactionUtils.isInTransaction();
if (inTransaction) {
    log.debug("当前在事务中");
}
```

### 3. 性能监控
- 监控事务执行时间
- 监控事务回滚率
- 监控数据库连接池状态

## 常见问题和解决方案

### 1. 事务不生效
- 检查是否启用了事务管理
- 确认方法是通过Spring代理调用的
- 验证事务注解配置是否正确

### 2. 事务回滚不生效
- 确认异常类型是否在rollbackFor范围内
- 检查异常是否被正确抛出
- 验证事务传播行为配置

### 3. 性能问题
- 减少事务执行时间
- 使用批量操作
- 优化数据库查询

### 4. 并发问题
- 使用适当的事务隔离级别
- 避免长时间持有锁
- 处理死锁和超时异常
