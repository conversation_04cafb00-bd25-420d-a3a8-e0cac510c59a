package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.transform.service.ProductionPerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 生产实绩处理器
 * <p>
 * 负责处理生产实绩消息的转换和处理，主要功能包括：
 * 1. 接收生产实绩数据消息
 * 2. 更新任务状态为已完成
 * 3. 处理产出物料信息和属性
 * 4. 记录操作日志
 * 5. 支持批量生产实绩数据处理
 * 6. 管理任务物料关联关系
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@MessageHandler(messageType = "production_performance", desc = "生产实绩")
public class ProductionPerformanceHandle extends CommonHandler<String> {

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    @Autowired
    private ProductionPerformanceService productionPerformanceService;

    /**
     * 转换处理生产实绩消息
     * <p>
     * 处理生产实绩JSON数据的核心方法，包括以下步骤：
     * 1. 支持数组格式的批量处理
     * 2. 更新任务状态为已完成
     * 3. 处理任务的开始时间和结束时间
     * 4. 处理产出物料信息
     * 5. 记录操作日志
     * </p>
     *
     * @param json 生产实绩的JSON字符串
     * @throws RuntimeException 当任务不存在时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext) {
        productionPerformanceService.productionPerformanceTransform(json, handleContext);
    }

    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {

    }

    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

}
