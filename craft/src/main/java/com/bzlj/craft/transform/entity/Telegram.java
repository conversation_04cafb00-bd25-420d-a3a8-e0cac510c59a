package com.bzlj.craft.transform.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/7/2 14:27
 */
@Accessors(chain = true)
@Document(collection = "telegram")
@Data
public class Telegram implements Serializable {

    @Id
    private String id;

    private String serviceId;

    /**
     * 关联id
     */
    private String relationId;

    /**
     * 电文类型，上料/产出/投入
     */
    private String type;


    private String payload;

    /**
     * payload的MD5哈希值，用于快速比较和查询
     */
    private String payloadHash;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @CreatedDate
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @LastModifiedDate
    private Date updateTime;

}
