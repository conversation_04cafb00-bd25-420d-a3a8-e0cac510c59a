package com.bzlj.craft.transform.common;

import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.model.DataFlowStatus;
import bici.bzlj.dataprocess.core.producer.MessageEventProducer;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
@Slf4j
public abstract class AbsCommonConsumer {
    @Autowired
    private MessageEventProducer messageEventProducer;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 公共的数据处理逻辑
     *
     * @param messageInfo
     * @param modelName
     * <AUTHOR>
     * @date 2025/5/29 17:44
     */
    public void handleData(ForwardMessageInfo<?> messageInfo, String modelName) {
        try {
            Object payload = messageInfo.getPayload();

            // 先判断payload的数据类型
            if (payload == null) {
                log.warn("消息载荷为空，跳过处理: messageId={}", messageInfo.getTelegramId());
                return;
            }

            // 将payload转化为JsonNode
            JsonNode jsonNode = convertToJsonNode(payload);
            if (jsonNode == null) {
                log.warn("payload转换为JsonNode失败，跳过处理: messageId={}, payloadType={}",
                        messageInfo.getTelegramId(), payload.getClass().getSimpleName());
                return;
            }

            // 判断JsonNode是否为ArrayNode
            if (jsonNode.isArray()) {
                ArrayNode arrayNode = (ArrayNode) jsonNode;
                String baseTelegramId = messageInfo.getTelegramId();

                log.debug("检测到ArrayNode类型payload，数组大小: {}, messageId: {}", arrayNode.size(), baseTelegramId);

                // 循环处理数组中的每个节点
                for (int i = 0; i < arrayNode.size(); i++) {
                    JsonNode node = arrayNode.get(i);
                    // 生成带序号的messageId：原messageId_1, 原messageId_2, ...
                    String messageId = baseTelegramId + "_" + (i + 1);
                    createAndSendMessageEvent(messageId, messageInfo.getServiceId(), node.toString(), messageInfo.getDataFlowStatus());
                }
            } else {
                // 非数组类型的JsonNode
                log.debug("检测到非数组JsonNode类型payload: {}", jsonNode.getNodeType());
                createAndSendMessageEvent(messageInfo.getTelegramId(), messageInfo.getServiceId(), jsonNode.toString(), messageInfo.getDataFlowStatus());
            }
        } catch (Exception e) {
            log.error("消费[{}]kafka数据失败:topic:{},电文内容:{}", modelName,
                    messageInfo.getServiceId(),
                    JsonUtils.toJson(messageInfo),
                    e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 将payload转换为JsonNode
     *
     * @param payload 原始载荷
     * @return JsonNode对象，转换失败时返回null
     */
    private JsonNode convertToJsonNode(Object payload) {
        try {
            if (payload instanceof JsonNode) {
                // 如果已经是JsonNode，直接返回
                return (JsonNode) payload;
            } else if (payload instanceof String) {
                // 如果是字符串，尝试解析为JsonNode
                return JsonUtils.toJsonNode((String) payload);
            } else {
                // 其他类型，先转换为JSON字符串再解析
                String jsonString = JsonUtils.toJson(payload);
                return JsonUtils.toJsonNode(jsonString);
            }
        } catch (Exception e) {
            log.error("payload转换为JsonNode失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 创建并发送MessageEvent
     *
     * @param messageId 消息ID
     * @param messageType 消息类型
     * @param payload 消息载荷
     * @param dataFlowStatus 数据流转状态
     */
    private void createAndSendMessageEvent(String messageId, String messageType, Object payload, DataFlowStatus dataFlowStatus) {
        MessageEvent event = new MessageEvent();
        event.setMessageId(messageId);
        event.setMessageType(messageType);
        event.setPayload(payload);
        event.setDataFlowStatus(dataFlowStatus);
        messageEventProducer.pushMessage(event);
    }
}
