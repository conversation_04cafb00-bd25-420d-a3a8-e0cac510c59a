package com.bzlj.craft.transform.data;

import com.bzlj.craft.entity.Plant;
import com.bzlj.craft.entity.SysDictItem;
import com.bzlj.craft.repository.PlantRepository;
import com.bzlj.craft.repository.SysDictItemRepository;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class DataPrepareService {
    private final Map<String, Object> status_dict_map = new ConcurrentHashMap<>();

    private final Map<String, Object> plant_map = new ConcurrentHashMap<>();

    private final Map<String, Object> param_type_dict_map = new ConcurrentHashMap<>();

    private final String dictCode = "TASK_STATUS";

    private static final String PARAM_TYPE_DICT_CODE = "PARAM_TYPE";

    @Autowired
    PlantRepository plantRepository;

    @Autowired
    SysDictItemRepository sysDictItemRepository;


    /**
     * 通用的字典数据加载方法
     * @param dictCode 字典代码
     * @param cacheMap 缓存Map
     * @param description 描述信息，用于日志输出
     */
    private void loadDictData(String dictCode, Map<String, Object> cacheMap, String description) {
        List<SysDictItem> sysDictItems = sysDictItemRepository.findByDictCodeDictCode(dictCode);
        if(!CollectionUtils.isEmpty(sysDictItems)){
            sysDictItems.forEach(sysDictItem -> {
                cacheMap.put(sysDictItem.getItemCode(), sysDictItem);
            });
            log.info("{}字典数据预加载完成，共加载 {} 条数据", description, sysDictItems.size());
        }
    }

    @PostConstruct
    public void init() {
        // 加载任务状态字典
        loadDictData(dictCode, status_dict_map, "任务状态");

        // 加载参数类型字典
        loadDictData(PARAM_TYPE_DICT_CODE, param_type_dict_map, "参数类型");

        List<Plant> plants = plantRepository.findAll();
        if(!CollectionUtils.isEmpty(plants)){
            plants.forEach(plant ->
                    plant_map.put(plant.getPlantCode(), plant)
            );
            log.info("工厂数据预加载完成，共加载 {} 条数据",plants.size());
        }
        //工序导入
    }

    /**
     * 通用的字典项获取方法
     * @param itemCode 字典项编码
     * @param cacheMap 缓存Map
     * @return 字典项对象，如果不存在则返回null
     */
    private SysDictItem getDictItem(String itemCode, Map<String, Object> cacheMap) {
        SysDictItem sysDictItem = (SysDictItem) cacheMap.get(itemCode);
        if(Objects.isNull(sysDictItem)){
            SysDictItem dict = sysDictItemRepository.findFirstByItemCode(itemCode);
            if(Objects.nonNull(dict)){
                cacheMap.put(dict.getItemCode(), dict);
                return dict;
            }
            return null;
        }
        return sysDictItem;
    }

    public SysDictItem getStatusDictItem(String statusCode){
        return getDictItem(statusCode, status_dict_map);
    }

    public Plant getPlant(String plantCode){
        Plant plant = (Plant) plant_map.get(plantCode);
        if(Objects.isNull(plant)){
            plant = plantRepository.findFirstByPlantCode(plantCode);
            if(Objects.nonNull(plant)){
                plant_map.put(plant.getPlantCode(),plant);
                return plant;
            }
            return null;
        }
        return plant;
    }

    public void setPlat(Plant plant){
        plant_map.put(plant.getPlantCode(),plant);
    }

    public SysDictItem getParamTypeDictItem(String paramTypeCode){
        return getDictItem(paramTypeCode, param_type_dict_map);
    }

}
