package com.bzlj.craft.transform.enums;


public enum TaskChangeStatus {
    CANCEL("02", "撤销"),
    CANNOT_CANCEL("08", "不可撤销"),
    START("30","任务开始"),
    ;

    private String code;
    private String name;

    TaskChangeStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static TaskChangeStatus getByCode(String code) {
        for (TaskChangeStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        throw new RuntimeException(String.format("没有找到任务更新任务状态枚举：%s",code));
    }
}
