package com.bzlj.craft.transform.service.impl;

import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.transform.constant.ExcelImportPolicyConstants;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service(value = ExcelImportPolicyConstants.EXCEL_IMPORT_POLICY + "process")
public class CraftProcessExcelImporter implements ExcelImportPolicy<CraftProcess> {

    private static final String PROCESS_TYPE_DICT_CODE = "PROCESS_TYPE";

    private final PlantRepository plantRepository;

    private final CraftProcessRepository craftProcessRepository;

    private final DataPrepareService dataPrepareService;

    private final ProcessStepRepository processStepRepository;

    private final SysDictItemRepository sysDictItemRepository;

    private final SysDictRepository sysDictRepository;

    public CraftProcessExcelImporter(PlantRepository plantRepository,
                                     CraftProcessRepository craftProcessRepository,
                                     DataPrepareService dataPrepareService,
                                     ProcessStepRepository processStepRepository,
                                     SysDictItemRepository sysDictItemRepository,
                                     SysDictRepository sysDictRepository) {
        this.plantRepository = plantRepository;
        this.craftProcessRepository = craftProcessRepository;
        this.dataPrepareService = dataPrepareService;
        this.processStepRepository = processStepRepository;
        this.sysDictItemRepository = sysDictItemRepository;
        this.sysDictRepository = sysDictRepository;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<CraftProcess> importFromExcel(InputStream inputStream){
        ImportResult<CraftProcess> results = new ImportResult<>();

        List<CraftProcess> processes = parseExcel(inputStream);
        List<String> excelCodes = processes.stream()
                .map(CraftProcess::getProcessCode)
                .collect(Collectors.toList());

        // 批量查询已存在的编码
        List<String> existingCodes = craftProcessRepository.findExistingCodes(excelCodes);

        // 过滤重复数据
        List<CraftProcess> newProcesses = processes.stream()
                .filter(p -> !existingCodes.contains(p.getProcessCode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(newProcesses)) {
            results.setSize(0);
            results.setResults(List.of());
            return results;
        }
        List<CraftProcess> craftProcesses = craftProcessRepository.saveAll(newProcesses);
        //创建同名称工步
        List<ProcessStep> steps = craftProcesses.stream().map(process -> {
            ProcessStep processStep = new ProcessStep();
            processStep.setProcess(process);
            processStep.setStepOrder(1);
            processStep.setParamConfig(Map.of());
            processStep.setQualityStandard(Map.of());
            processStep.setStepName(process.getProcessName());
            processStep.setStepCode(process.getProcessCode());
            return processStep;
        }).toList();
        processStepRepository.saveAll(steps);
        results.setSize(craftProcesses.size());
        results.setResults(craftProcesses);
        return results;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CraftProcess> parseExcel(InputStream inputStream) {
        List<CraftProcess> processes = new ArrayList<>();
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 默认读取第一个Sheet

            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过表头

                String processCode = getCellStringValue(row.getCell(0));
                String processName = getCellStringValue(row.getCell(1));
                String plantName = getCellStringValue(row.getCell(2));
                Plant plant = dataPrepareService.getPlant(plantName);
                if(Objects.isNull(plant)){
                    plant = new Plant();
                    plant.setPlantCode(plantName);
                    plant.setPlantName(plantName);
                    plant = plantRepository.save(plant);
                    dataPrepareService.setPlat(plant);
                }

                // 创建或获取工序类型字典项
                SysDictItem processType = null;
                if (StringUtils.hasText(processCode) && StringUtils.hasText(processName)) {
                    processType = getOrCreateSysDictItem(processCode, processName, PROCESS_TYPE_DICT_CODE);
                }

                // 构建CraftProcess
                CraftProcess process = new CraftProcess();
                process.setProcessCode(processCode);
                process.setProcessName(processName);
                process.setPlant(plant);
                process.setProcessType(processType); // 设置工序类型
                process.setProcessOrder(1); // 默认值，按需调整
                process.setInputOutputSpec(Map.of()); // 默认空JSON

                processes.add(process);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally{
            try {
                workbook.close();
            }catch (IOException e){
                throw new RuntimeException("Failed to close workbook", e);
            }
        }
        return processes;
    }

    /**
     * 获取或创建SysDict对象
     * @param dictCode 字典编码
     * @param dictName 字典名称
     * @return SysDict对象
     */
    private SysDict getOrCreateSysDict(String dictCode, String dictName) {
        return sysDictRepository.findById(dictCode)
                .orElseGet(() -> {
                    SysDict sysDict = new SysDict();
                    sysDict.setDictCode(dictCode);
                    sysDict.setDictName(dictName);
                    sysDict.setDescription("工序类型字典");
                    return sysDictRepository.save(sysDict);
                });
    }

    /**
     * 获取或创建SysDictItem对象
     * @param itemCode 字典项编码（对应processCode）
     * @param itemName 字典项名称（对应processName）
     * @param dictCode 字典编码
     * @return SysDictItem对象
     */
    private SysDictItem getOrCreateSysDictItem(String itemCode, String itemName, String dictCode) {
        // 首先检查是否已存在该字典项
        SysDictItem existingItem = sysDictItemRepository.findFirstByItemCode(itemCode);
        if (existingItem != null) {
            return existingItem;
        }

        // 获取或创建字典
        SysDict sysDict = getOrCreateSysDict(dictCode, "工序类型");

        // 获取当前字典下已有的字典项总数，用于计算sortOrder
        int existingCount = sysDictItemRepository.countByDictCodeDictCode(dictCode);
        int nextSortOrder = existingCount + 1;

        // 创建新的字典项
        SysDictItem sysDictItem = new SysDictItem();
        sysDictItem.setItemCode(itemCode);
        sysDictItem.setItemName(itemName);
        sysDictItem.setDictCode(sysDict);
        sysDictItem.setSortOrder(nextSortOrder);
        sysDictItem.setIsActive(true);

        return sysDictItemRepository.save(sysDictItem);
    }
}