package com.bzlj.craft.transform.common;

import bici.bzlj.dataprocess.core.model.DataFlowStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * kafka 解析完成数据转发实体
 *
 * <AUTHOR>
 * @date 2025/6/04 15:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ForwardMessageInfo<T> {
    /**
     * 服务id
     */
    private String serviceId;
    /**
     * 发送时间
     */
    private Long sendTime;
    /**
     * 内容
     */
    private T payload;

    /**
     * 落库电文id
     */
    private String telegramId;

    /**
     * 数据流转状态
     */
    private DataFlowStatus dataFlowStatus;
}
