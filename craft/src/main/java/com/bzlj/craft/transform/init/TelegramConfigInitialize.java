package com.bzlj.craft.transform.init;

import com.bzlj.craft.transform.cache.TelegramConfigCache;
import com.bzlj.craft.transform.repository.TelegramConfigRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/7/2 16:46
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Configuration
@RequiredArgsConstructor
public class TelegramConfigInitialize {

    private final TelegramConfigRepository telegramConfigRepository;


    @Bean
    public TelegramConfigCache getTelegramConfigCache() {
        return new TelegramConfigCache(telegramConfigRepository);
    }
}
