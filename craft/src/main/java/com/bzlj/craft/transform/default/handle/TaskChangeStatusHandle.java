package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.enums.TaskStatus;
import com.bzlj.craft.repository.ProductionTaskExtendRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.enums.TaskChangeStatus;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static com.bzlj.craft.exception.BiciErrorData.TASK_NOT_EXIST;

/**
 * 任务状态变更处理器
 * <p>
 * 负责处理任务状态变更消息的转换和处理，主要功能包括：
 * 1. 接收任务状态变更消息
 * 2. 根据状态码执行相应的操作（取消、开始等）
 * 3. 更新任务状态和扩展信息
 * 4. 记录状态变更日志
 * 5. 清理相关的电报数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@MessageHandler(messageType = "task_change_status", desc = "任务状态")
public class TaskChangeStatusHandle extends CommonHandler<String> {

    /**
     * 监控服务，用于查询和管理生产任务
     */
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * 生产任务扩展仓储，用于管理任务扩展信息
     */
    @Autowired
    private ProductionTaskExtendRepository productionTaskExtendRepository;

    /**
     * 数据准备服务，用于获取基础数据
     */
    @Autowired
    private DataPrepareService dataPrepareService;

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 变更任务状态的核心处理方法
     * <p>
     * 根据接收到的任务状态变更消息，执行相应的状态变更操作：
     * - CANCEL: 删除任务
     * - CANNOT_CANCEL: 记录无法撤销的日志
     * - START: 更新任务状态为进行中
     * - 其他状态: 记录日志但不处理
     * </p>
     *
     * @param json 任务状态变更的JSON字符串
     * @throws RuntimeException 当任务状态或任务号为空时抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    void changeTaskStatus(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        JsonNode status = jsonNode.get("taskStatus");
        JsonNode taskCodeNode = jsonNode.get("taskCode");

        // 参数验证
        if (Objects.isNull(status)) {
            throw new RuntimeException("任务更改状态为空");
        }
        if (Objects.isNull(taskCodeNode) || StringUtils.isEmpty(taskCodeNode.asText())) {
            throw new RuntimeException("任务更改任务号为空");
        }
        String taskCode = taskCodeNode.asText();
        List<ProductionTask> tasks = surveillanceService.findByTaskCodes(List.of(taskCode));
        if(CollectionUtils.isEmpty(tasks)){
            throw TASK_NOT_EXIST.buildException(taskCode);
        }

        ProductionTask productionTask = tasks.stream().findFirst().get();
        // 根据状态码执行相应操作
        switch (TaskChangeStatus.getByCode(status.asText())) {
            case CANCEL:
                // 取消任务 - 删除任务
                surveillanceService.deleteTask(productionTask.getTaskId());
                break;
            case CANNOT_CANCEL:
                // 无法撤销 - 记录日志
                log.info("{}:任务无法撤销，任务状态：{}", productionTask.getTaskCode(), status.asText());
                break;
            case START:
                // 开始任务 - 更新状态为进行中
                surveillanceService.changeStatus(productionTask.getTaskId(), dataPrepareService.getStatusDictItem(TaskStatus.in_progress.getCode()));
                log.info("{}:任务开始，更新任务状态：{}", productionTask.getTaskCode(), status.asText());
                break;
            default:
                // 其他状态 - 仅记录日志
                log.info("{}:任务更改状态为{}，无需处理", productionTask.getTaskCode(), status.asText());
        }
    }

    /**
     * 转换处理任务状态变更消息
     * <p>
     * 接收任务状态变更的JSON消息，调用具体的状态变更处理方法
     * </p>
     *
     * @param s 任务状态变更的JSON字符串
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String s) {
        changeTaskStatus(s);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除相关的电报数据
     * </p>
     *
     * @param telegramId 电报ID，如果为空则不执行删除操作
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }
}
