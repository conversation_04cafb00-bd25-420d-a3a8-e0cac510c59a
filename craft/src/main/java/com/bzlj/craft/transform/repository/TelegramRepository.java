package com.bzlj.craft.transform.repository;

import com.bzlj.craft.transform.entity.Telegram;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;


public interface TelegramRepository extends MongoRepository<Telegram, String> {
    List<Telegram> findByRelationIdInAndIdIsNot(List<String> relationIds, String id);

    void deleteAllByServiceIdAndRelationIdAndTypeAndPayload(String serviceId, String relationId, String type, String payload);

    /**
     * 使用payloadHash代替payload进行删除，提高性能
     * payloadHash是payload的MD5哈希值，体积小且有索引，查询效率高
     */
    void deleteAllByServiceIdAndRelationIdAndTypeAndPayloadHash(String serviceId, String relationId, String type, String payloadHash);

}