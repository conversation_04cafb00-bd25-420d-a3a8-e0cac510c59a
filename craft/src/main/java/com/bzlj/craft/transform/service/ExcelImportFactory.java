package com.bzlj.craft.transform.service;

import com.bzlj.craft.transform.constant.ExcelImportPolicyConstants;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * excel策略工厂接口
 * @description:
 * @author: polang
 * @create:
 **/
@Component
public class ExcelImportFactory implements ApplicationContextAware {

    private static Map<String, ExcelImportPolicy> policyMap;

    /**
     * 导入策略
     * @param
     * @return
     */
   public static ExcelImportPolicy getPolicy(String type){
       ExcelImportPolicy policy = null;
       if(MapUtils.isNotEmpty(policyMap)){
           policy = policyMap.getOrDefault(ExcelImportPolicyConstants.EXCEL_IMPORT_POLICY + type, null);
       }
       return policy;
   }

    /**
     * 默认策略
     * @return
     */
    public static ExcelImportPolicy getDefaultPolicy()        {
        return policyMap.get(ExcelImportPolicyConstants.PROCESS_EXCEL_IMPORT_POLICY);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        policyMap = applicationContext.getBeansOfType(ExcelImportPolicy.class);
    }
}
