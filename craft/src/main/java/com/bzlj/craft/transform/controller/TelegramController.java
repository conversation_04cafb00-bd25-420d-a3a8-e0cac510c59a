package com.bzlj.craft.transform.controller;

import com.bici.common.core.utils.poi.ExcelUtil;
import com.bzlj.craft.transform.entity.TelegramConfig;
import com.bzlj.craft.transform.service.ITelegramConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/telegram/config")
@Slf4j
@RequiredArgsConstructor
public class TelegramController {

    private final ITelegramConfigService telegramConfigService;

    /**
     * 导入同步配置Excel文件
     */
    @PostMapping("/importSyncConfigByExcel")
    public void importSyncConfigByExcel(@RequestParam(value = "file") MultipartFile file) {
        try {
            ExcelUtil<TelegramConfig> util = new ExcelUtil<>(TelegramConfig.class);
            List<TelegramConfig> telegramConfigs = util.importExcel(file.getInputStream());
            String message = telegramConfigService.importSyncConfig(telegramConfigs);
            log.info(message);
        } catch (Exception e) {
            log.error("导入Excel时发生错误", e);
        }
    }

}
