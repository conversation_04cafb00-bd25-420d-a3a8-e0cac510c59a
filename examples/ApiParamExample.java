package examples;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;

/**
 * API参数使用示例
 * 展示新的ApiParam注解的使用方法和生成的JSON结构
 */
public class ApiParamExample {

    /**
     * 示例1：基础用法
     * 生成的JSON结构：
     * {
     *   "api": {
     *     "url": "/craft/craft/findPlantList",
     *     "method": "get",
     *     "params": {
     *       "status": "active",
     *       "limit": 10
     *     }
     *   }
     * }
     */
    @ComponentProperty(
        label = "所属分厂", 
        path = "plantCode", 
        dataSourceType = DataSourceType.API,
        dataSource = @ComponentProperty.DataSource(
            apiUrl = "/craft/craft/findPlantList",
            httpMethod = HttpMethod.GET,
            apiParams = {
                @ComponentProperty.ApiParam(
                    key = "params", 
                    value = "{\"status\":\"active\",\"limit\":10}"
                )
            },
            mapping = @ComponentProperty.Mapping(
                valueKey = "plantCode",
                labelKey = "plantName"
            )
        )
    )
    private String plantCode;

    /**
     * 示例2：多种参数类型
     * 生成的JSON结构：
     * {
     *   "api": {
     *     "url": "/craft/craft/findProcessByPlantCode",
     *     "method": "get",
     *     "params": {
     *       "status": "active"
     *     },
     *     "dependencyParams": {
     *       "plantCode": "plant.plantCode"
     *     },
     *     "apiParams": {
     *       "version": "v1",
     *       "format": "json"
     *     }
     *   }
     * }
     */
    @ComponentProperty(
        label = "工序", 
        path = "processCode", 
        dataSourceType = DataSourceType.API,
        dataSource = @ComponentProperty.DataSource(
            apiUrl = "/craft/craft/findProcessByPlantCode",
            httpMethod = HttpMethod.GET,
            apiParams = {
                @ComponentProperty.ApiParam(
                    key = "params", 
                    value = "{\"status\":\"active\"}"
                ),
                @ComponentProperty.ApiParam(
                    key = "dependencyParams", 
                    value = "{\"plantCode\":\"plant.plantCode\"}"
                ),
                @ComponentProperty.ApiParam(
                    key = "apiParams", 
                    value = "{\"version\":\"v1\",\"format\":\"json\"}"
                )
            },
            mapping = @ComponentProperty.Mapping(
                valueKey = "processCode",
                labelKey = "processName"
            )
        )
    )
    private String processCode;

    /**
     * 示例3：复杂参数结构
     * 生成的JSON结构：
     * {
     *   "api": {
     *     "url": "/craft/craft/findTaskList",
     *     "method": "post",
     *     "queryParams": {
     *       "filter": {
     *         "status": "active",
     *         "type": "production"
     *       },
     *       "sort": {
     *         "createTime": "desc"
     *       }
     *     },
     *     "headers": {
     *       "Authorization": "Bearer ${token}",
     *       "Content-Type": "application/json"
     *     }
     *   }
     * }
     */
    @ComponentProperty(
        label = "任务", 
        path = "taskId", 
        dataSourceType = DataSourceType.API,
        dataSource = @ComponentProperty.DataSource(
            apiUrl = "/craft/craft/findTaskList",
            httpMethod = HttpMethod.POST,
            apiParams = {
                @ComponentProperty.ApiParam(
                    key = "queryParams", 
                    value = "{\"filter\":{\"status\":\"active\",\"type\":\"production\"},\"sort\":{\"createTime\":\"desc\"}}"
                ),
                @ComponentProperty.ApiParam(
                    key = "headers", 
                    value = "{\"Authorization\":\"Bearer ${token}\",\"Content-Type\":\"application/json\"}"
                )
            },
            mapping = @ComponentProperty.Mapping(
                valueKey = "taskId",
                labelKey = "taskName"
            )
        )
    )
    private String taskId;
}
