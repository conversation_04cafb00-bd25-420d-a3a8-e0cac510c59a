<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>bici.bzlj</groupId>
    <artifactId>bzlj</artifactId>
    <version>${bzlj.version}</version>
    <packaging>pom</packaging>
    <modules>
        <module>craft</module>
        <module>message-exchange</module>
        <module>craft-score</module>
        <module>base</module>
        <module>craft-common</module>
    </modules>
    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <java.version>21</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <bzlj.version>1.0.0</bzlj.version>
        <flatten-maven.version>1.3.0</flatten-maven.version>
        <gitflow.incremental.build.version>4.5.4</gitflow.incremental.build.version>
        <reflections.version>0.10.2</reflections.version>
        <mapstruct.plus.version>1.4.4</mapstruct.plus.version>
        <lombok.version>1.18.36</lombok.version>
        <harbor.host>***************</harbor.host>
        <harbor.namespace>bwty</harbor.namespace>
        <harbor.username>${jib.user.name}</harbor.username>
        <harbor.password>${jib.user.password}</harbor.password>
        <jib.phase.backed>deploy</jib.phase.backed>
        <db-migration>2.0.8</db-migration>
        <querydsl.version>5.1.0</querydsl.version>
        <blazebit.version>1.6.10</blazebit.version>
        <hutool.version>5.8.25</hutool.version>
<!--        <gib.excludePathsMatching>\.github|\.gitignore|.*ApiController\.java</gib.excludePathsMatching>-->
<!--        <gib.baseBranch>refs/remotes/origin/polang-1.0.0</gib.baseBranch>-->
<!--        <gib.logImpactedTo>C:\Users\<USER>\Desktop\k8s\log.txt</gib.logImpactedTo>-->
<!--        <gib.referenceBranch>refs/remotes/origin/polang-dev</gib.referenceBranch>-->
        <dynamic.mongo.version>1.0.1</dynamic.mongo.version>
        <spring-cloud.version>4.2.0</spring-cloud.version>
        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
        <httpclient.version>4.5.14</httpclient.version>
        <json.version>20240303</json.version>
        <mybatis.version>3.5.11</mybatis.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <json-schema.version>1.5.2</json-schema.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.bzlj</groupId>
                <artifactId>bzlj-framework-dependencies</artifactId>
                <version>1.0.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.bici</groupId>
                <artifactId>graph-ops-nebula</artifactId>
                <version>0.1.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <parameters>true</parameters>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <!--<extensions>
            &lt;!&ndash; 增量编译插件 &ndash;&gt;
            <extension>
                <groupId>io.github.gitflow-incremental-builder</groupId>
                <artifactId>gitflow-incremental-builder</artifactId>
                <version>${gitflow.incremental.build.version}</version>
            </extension>
        </extensions>-->
    </build>

    <repositories>
        <repository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>bici</id>
            <name>bici nexus</name>
            <url>https://nexus.bicisims.com/repository/maven-releases/</url>
        </repository>
    </distributionManagement>

</project>