package com.bzlj.message;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.BooleanUtils;
import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 从excel导入规则
 * 需按照特冶电文在线文档格式
 */
public class ExcelDataExtractor {

    public static void main(String[] args) {
        ExcelDataExtractor extractor = new ExcelDataExtractor();

        String path = "E:\\Users\\文件\\宝武特冶\\整理过的导入电文\\熔炼化学成分电文清单0520.xlsx";

        List<MessageRules> list = extractor.extractExcelData(path);

        //ConfigurableApplicationContext run = SpringApplication.run(MessageApplication.class, args);
        //MessageRulesRepository bean = run.getBean(MessageRulesRepository.class);
        //System.err.println(bean.insert(list));

        System.err.println(list);
        System.err.println(list.size());
    }

    private static final HashMap<String, Integer> IS_LOOP = new HashMap<String, Integer>() {{
        put("单体", 0);
        put("循环", 1);
    }};

    public List<MessageRules> extractExcelData(String filePath) {
        SheetDataListener listener = new SheetDataListener();
        ExcelReader excelReader = null;
        try {
            excelReader = EasyExcel.read(filePath, listener).build();
            excelReader.readAll();
        } finally {
            if (excelReader != null) {
                excelReader.finish(); // 关键：必须显式关闭
            }
        }
        return listener.getResult();
    }

    static class SheetDataListener extends AnalysisEventListener<Map<Integer, String>> {
        private final List<MessageRules> resultList = new ArrayList<>();
        private String currentSheetName;
        private Integer isLoop = 0;
        private int currentSequence = 1;

        @Override
        public void invoke(Map<Integer, String> rowMap, AnalysisContext context) {
            // 获取当前行号（从0开始计数）
            int rowIndex = context.readRowHolder().getRowIndex();

            // 获取当前sheet名称
            String sheetName = context.readSheetHolder().getSheetName();

            // 当切换sheet时重置计数器
            if (!sheetName.equals(currentSheetName)) {
                currentSheetName = sheetName;
                currentSequence = 1;
            }

            // 只处理第三行及之后的数据
            if (rowIndex < 2) return;

            // 按列顺序整理数据
            List<String> rowData = formatRowData(rowMap);

            if (StrUtil.isNotBlank(rowData.get(0)) && isLoop != MapUtil.getInt(IS_LOOP, rowData.get(0), -1))
                isLoop = MapUtil.getInt(IS_LOOP, rowData.get(0), -1);

            MessageRules messageRules = new MessageRules();
            messageRules.setTelegraphTextCode(currentSheetName);
            messageRules.setField(rowData.get(1));
            messageRules.setFieldName(rowData.get(2));
            messageRules.setFieldType(rowData.get(3));
            messageRules.setFieldSize(parseStringToInt(rowData.get(4), 0));
            //if (rowData.get(4).contains(",")) messageRules.setPrecision(parseStringToInt(rowData.get(4), 1));

            if (rowData.size() < 6) {
                messageRules.setIsRequired(0);
            } else {
                messageRules.setIsRequired(BooleanUtils.valueOf(rowData.get(5)) ? 1 : 0);
            }

            //if (rowData.size() >= 7) messageRules.setRemark(rowData.get(6));
            if (rowData.size() >= 7) messageRules.setPrecision(Integer.valueOf(rowData.get(6)));

            messageRules.setIsLoop(isLoop);
            messageRules.setSorts(currentSequence);
            resultList.add(messageRules);

            currentSequence++;
        }


        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 可选的收尾操作
        }

        private List<String> formatRowData(Map<Integer, String> rowMap) {
            // 确定最大列索引
            int maxColIndex = rowMap.keySet().stream()
                    .max(Integer::compareTo)
                    .orElse(-1);

            // 按顺序填充所有列数据
            List<String> orderedData = new ArrayList<>();
            for (int i = 0; i <= maxColIndex; i++) {
                String orDefault = rowMap.getOrDefault(i, "");
                if (StrUtil.isEmpty(orDefault)) orDefault = "";
                orderedData.add(orDefault);
            }
            return orderedData;
        }

        public List<MessageRules> getResult() {
            return resultList;
        }
    }


    /**
     * 将包含逗号的字符串转换为整数
     *
     * @param input 输入字符串，可能包含逗号或为空
     * @return 解析后的整数值（遵循特定规则）
     */
    public static int parseStringToInt(String input, int index) {
        // 处理空值和空字符串
        if (input == null || input.isEmpty())
            return 0;

        // 分割字符串（最多分割为2部分）
        String[] parts = input.split(",", 2);
        String firstSegment = parts[index].trim();

        // 处理空字符串的情况
        if (firstSegment.isEmpty())
            return 0;

        try {
            return Integer.parseInt(firstSegment);
        } catch (NumberFormatException e) {
            // 处理非数字内容
            return 0;
        }
    }

}
