package com.bzlj.message.internaldistribution.handlers;

import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.repository.mongo.MessageExceptionRepository;
import com.bzlj.message.internaldistribution.repository.mongo.TopicMapRepository;
import com.fasterxml.jackson.databind.JsonNode;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.lang.reflect.Method;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

/**
 * MessageVerifyHandler 测试类 - 基于 TestNG
 * 重点测试数字类型校验逻辑的优化
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public class MessageVerifyHandlerTest {

    @Mock
    private TopicMapRepository topicMapRepository;

    @Mock
    private MessageExceptionRepository messageExceptionRepository;

    @InjectMocks
    private MessageVerifyHandler messageVerifyHandler;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试 isValidNumber 方法 - 使用反射调用私有方法
     */
    @Test(description = "测试数字类型校验逻辑")
    public void testIsValidNumber() throws Exception {
        // 使用反射获取私有方法
        Method isValidNumberMethod = MessageVerifyHandler.class.getDeclaredMethod("isValidNumber", JsonNode.class);
        isValidNumberMethod.setAccessible(true);

        // 测试用例1：已经是数字类型的值
        JsonNode intNode = JsonUtils.toJsonNode("123");
        assertTrue((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, intNode), 
                "整数应该被识别为有效数字");

        JsonNode doubleNode = JsonUtils.toJsonNode("123.45");
        assertTrue((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, doubleNode), 
                "小数应该被识别为有效数字");

        // 测试用例2：字符串类型但可以转换为数字
        JsonNode stringIntNode = JsonUtils.toJsonNode("\"123\"");
        assertTrue((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, stringIntNode), 
                "字符串整数应该被识别为有效数字");

        JsonNode stringDoubleNode = JsonUtils.toJsonNode("\"123.45\"");
        assertTrue((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, stringDoubleNode), 
                "字符串小数应该被识别为有效数字");

        JsonNode stringNegativeNode = JsonUtils.toJsonNode("\"-123.45\"");
        assertTrue((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, stringNegativeNode), 
                "负数字符串应该被识别为有效数字");

        JsonNode scientificNode = JsonUtils.toJsonNode("\"1.23e10\"");
        assertTrue((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, scientificNode), 
                "科学计数法字符串应该被识别为有效数字");

        // 测试用例3：无效的数字格式
        JsonNode invalidStringNode = JsonUtils.toJsonNode("\"abc123\"");
        assertFalse((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, invalidStringNode), 
                "包含字母的字符串不应该被识别为有效数字");

        JsonNode emptyStringNode = JsonUtils.toJsonNode("\"\"");
        assertFalse((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, emptyStringNode), 
                "空字符串不应该被识别为有效数字");

        JsonNode nullNode = JsonUtils.toJsonNode("null");
        assertFalse((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, nullNode), 
                "null值不应该被识别为有效数字");

        // 测试用例4：其他类型
        JsonNode booleanNode = JsonUtils.toJsonNode("true");
        assertFalse((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, booleanNode), 
                "布尔值不应该被识别为有效数字");

        JsonNode arrayNode = JsonUtils.toJsonNode("[1,2,3]");
        assertFalse((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, arrayNode), 
                "数组不应该被识别为有效数字");

        JsonNode objectNode = JsonUtils.toJsonNode("{\"key\":\"value\"}");
        assertFalse((Boolean) isValidNumberMethod.invoke(messageVerifyHandler, objectNode), 
                "对象不应该被识别为有效数字");
    }

    /**
     * 测试 isNumericString 方法
     */
    @Test(description = "测试字符串数字转换逻辑")
    public void testIsNumericString() throws Exception {
        // 使用反射获取私有方法
        Method isNumericStringMethod = MessageVerifyHandler.class.getDeclaredMethod("isNumericString", String.class);
        isNumericStringMethod.setAccessible(true);

        // 有效的数字字符串
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "123"), 
                "整数字符串应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "123.45"), 
                "小数字符串应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "-123.45"), 
                "负数字符串应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "+123.45"), 
                "正数字符串应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "1.23e10"), 
                "科学计数法字符串应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "1.23E-10"), 
                "科学计数法（负指数）字符串应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "0"), 
                "零应该有效");
        assertTrue((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "0.0"), 
                "零小数应该有效");

        // 无效的数字字符串
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "abc"), 
                "纯字母字符串应该无效");
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "123abc"), 
                "数字+字母字符串应该无效");
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "12.34.56"), 
                "多个小数点字符串应该无效");
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, ""), 
                "空字符串应该无效");
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, " "), 
                "空格字符串应该无效");
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "NaN"), 
                "NaN字符串应该无效");
        assertFalse((Boolean) isNumericStringMethod.invoke(messageVerifyHandler, "Infinity"), 
                "Infinity字符串应该无效");
    }

    /**
     * 测试完整的消息验证流程
     */
    @Test(description = "测试包含数字类型校验的完整消息验证流程")
    public void testVerifyHandlerWithNumberValidation() {
        // 模拟 TopicMap
        TopicMap topicMap = new TopicMap();
        topicMap.setTelegraphTextCode("TEST_SERVICE");
        topicMap.setTopic("test-topic");
        topicMap.setType("test-type");

        when(topicMapRepository.findByTelegraphTextCode("TEST_SERVICE")).thenReturn(topicMap);

        // 构造包含数字类型校验的测试消息
        String testMessage = """
            {
                "messageBody": {
                    "serviceId": "TEST_SERVICE",
                    "__blocks__": {
                        "Table1": {
                            "meta": {
                                "columns": [
                                    {"pos": 0, "name": "amount", "type": "N"},
                                    {"pos": 1, "name": "description", "type": "S"}
                                ]
                            },
                            "data": [
                                ["123.45", "测试描述1"],
                                ["678", "测试描述2"]
                            ]
                        }
                    }
                }
            }
            """;

        // 执行验证
        try {
            messageVerifyHandler.verifyHandler(testMessage, LocalDateTime.now());
            // 如果没有抛出异常，说明验证通过
            verify(messageExceptionRepository, never()).insert(any(MessageException.class));
        } catch (Exception e) {
            fail("消息验证应该成功，但抛出了异常: " + e.getMessage());
        }
    }
}
