package com.bzlj.message.internaldistribution.controller;

import com.bzlj.message.internaldistribution.entity.mongo.DailyMessageStatistics;
import com.bzlj.message.internaldistribution.repository.mongo.DailyMessageStatisticsRepository;
import com.bzlj.message.internaldistribution.service.MessageStatisticsService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

/**
 * 消息统计控制器测试类 - 使用 TestNG
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public class MessageStatisticsControllerTest {

    @Mock
    private MessageStatisticsService messageStatisticsService;

    @Mock
    private DailyMessageStatisticsRepository statisticsRepository;

    @InjectMocks
    private MessageStatisticsController messageStatisticsController;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test(description = "测试获取今日实时统计")
    public void testGetTodayStatistics() {
        // Given
        Map<String, Map<String, Long>> mockStats = new HashMap<>();
        Map<String, Long> serviceStats = new HashMap<>();
        serviceStats.put("messageCenterCount", 10L);
        serviceStats.put("xbusMessageCount", 5L);
        mockStats.put("test-service", serviceStats);

        when(messageStatisticsService.getAllTodayStatistics()).thenReturn(mockStats);

        // When
        ResponseEntity<Map<String, Map<String, Long>>> response = 
                messageStatisticsController.getTodayStatistics();

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 200);
        assertNotNull(response.getBody());
        assertTrue(response.getBody().containsKey("test-service"));
    }

    @Test(description = "测试获取今日实时统计异常情况")
    public void testGetTodayStatisticsException() {
        // Given
        when(messageStatisticsService.getAllTodayStatistics())
                .thenThrow(new RuntimeException("Redis连接失败"));

        // When
        ResponseEntity<Map<String, Map<String, Long>>> response = 
                messageStatisticsController.getTodayStatistics();

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 500);
    }

    @Test(description = "测试获取指定服务今日实时统计")
    public void testGetTodayStatisticsByServiceId() {
        // Given
        String serviceId = "test-service";
        Map<String, Long> mockStats = new HashMap<>();
        mockStats.put("messageCenterCount", 15L);
        mockStats.put("xbusMessageCount", 8L);

        when(messageStatisticsService.getTodayStatistics(serviceId)).thenReturn(mockStats);

        // When
        ResponseEntity<Map<String, Long>> response = 
                messageStatisticsController.getTodayStatisticsByServiceId(serviceId);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 200);
        assertNotNull(response.getBody());
        assertEquals(response.getBody().get("messageCenterCount"), Long.valueOf(15L));
        assertEquals(response.getBody().get("xbusMessageCount"), Long.valueOf(8L));
    }

    @Test(description = "测试获取指定日期历史统计")
    public void testGetDailyStatistics() {
        // Given
        LocalDate testDate = LocalDate.of(2025, 6, 20);
        List<DailyMessageStatistics> mockStats = Arrays.asList(
                createMockDailyStatistics("service1", testDate, 10L, 5L),
                createMockDailyStatistics("service2", testDate, 8L, 3L)
        );

        when(statisticsRepository.findByStatisticsDate(testDate)).thenReturn(mockStats);

        // When
        ResponseEntity<List<DailyMessageStatistics>> response = 
                messageStatisticsController.getDailyStatistics(testDate);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 200);
        assertNotNull(response.getBody());
        assertEquals(response.getBody().size(), 2);
    }

    @Test(description = "测试获取指定服务指定日期历史统计")
    public void testGetDailyStatisticsByServiceId() {
        // Given
        String serviceId = "test-service";
        LocalDate testDate = LocalDate.of(2025, 6, 20);
        DailyMessageStatistics mockStats = createMockDailyStatistics(serviceId, testDate, 20L, 12L);

        when(statisticsRepository.findByStatisticsDateAndServiceId(testDate, serviceId))
                .thenReturn(Optional.of(mockStats));

        // When
        ResponseEntity<DailyMessageStatistics> response = 
                messageStatisticsController.getDailyStatisticsByServiceId(serviceId, testDate);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 200);
        assertNotNull(response.getBody());
        assertEquals(response.getBody().getServiceId(), serviceId);
        assertEquals(response.getBody().getMessageCenterCount(), Long.valueOf(20L));
    }

    @Test(description = "测试获取指定服务指定日期历史统计 - 数据不存在")
    public void testGetDailyStatisticsByServiceIdNotFound() {
        // Given
        String serviceId = "non-existent-service";
        LocalDate testDate = LocalDate.of(2025, 6, 20);

        when(statisticsRepository.findByStatisticsDateAndServiceId(testDate, serviceId))
                .thenReturn(Optional.empty());

        // When
        ResponseEntity<DailyMessageStatistics> response = 
                messageStatisticsController.getDailyStatisticsByServiceId(serviceId, testDate);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 404);
    }

    @Test(description = "测试获取日期范围内历史统计")
    public void testGetStatisticsInRange() {
        // Given
        LocalDate startDate = LocalDate.of(2025, 6, 18);
        LocalDate endDate = LocalDate.of(2025, 6, 20);
        String serviceId = "test-service";

        List<DailyMessageStatistics> mockStats = Arrays.asList(
                createMockDailyStatistics("service1", startDate, 5L, 3L)
        );

        when(statisticsRepository.findByStatisticsDate(startDate)).thenReturn(mockStats);

        // When
        ResponseEntity<List<DailyMessageStatistics>> response = 
                messageStatisticsController.getStatisticsInRange(startDate, endDate, serviceId);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 200);
        assertNotNull(response.getBody());
    }

    @Test(description = "测试手动保存统计数据")
    public void testManualSaveStatistics() {
        // Given
        LocalDate testDate = LocalDate.of(2025, 6, 19);

        // When
        ResponseEntity<String> response = 
                messageStatisticsController.manualSaveStatistics(testDate);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 200);
        assertNotNull(response.getBody());
        assertTrue(response.getBody().contains("统计数据保存成功"));

        // 验证服务方法被调用
        verify(messageStatisticsService).saveStatisticsAndClearCache(testDate);
    }

    @Test(description = "测试手动保存统计数据异常情况")
    public void testManualSaveStatisticsException() {
        // Given
        LocalDate testDate = LocalDate.of(2025, 6, 19);
        doThrow(new RuntimeException("保存失败")).when(messageStatisticsService)
                .saveStatisticsAndClearCache(testDate);

        // When
        ResponseEntity<String> response = 
                messageStatisticsController.manualSaveStatistics(testDate);

        // Then
        assertNotNull(response);
        assertEquals(response.getStatusCode().value(), 500);
        assertNotNull(response.getBody());
        assertTrue(response.getBody().contains("统计数据保存失败"));
    }

    /**
     * 创建模拟的每日统计数据
     */
    private DailyMessageStatistics createMockDailyStatistics(String serviceId, LocalDate date, 
                                                            Long messageCenterCount, Long xbusCount) {
        return new DailyMessageStatistics()
                .setServiceId(serviceId)
                .setStatisticsDate(date)
                .setMessageCenterCount(messageCenterCount)
                .setXbusMessageCount(xbusCount)
                .setTotalCount(messageCenterCount + xbusCount)
                .setCreateTime(LocalDateTime.now())
                .setUpdateTime(LocalDateTime.now());
    }
}
