package com.bzlj.message;


import com.bzlj.message.internaldistribution.util.Constants;

import java.util.Random;

/**
 * 随机电文生成器
 */
public class RandomStringGenerator {

    public static void main(String[] args) {
        String randomString = generateRandomString(1815+(196*2));
        System.out.println(randomString);
        System.out.println(STR."字符串长度: \{randomString.length()}");

        String str = generateRandomSymbolString(56+(17*3));
        System.out.println(str);
        System.out.println(STR."分隔符字符串长度: \{str.length()}");
    }

    public static String generateRandomString(int length) {
        // 定义字符池：数字、字母（大小写）、空格
        String charPool = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789                                                               ";

        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            // 随机选择一个字符
            int randomIndex = random.nextInt(charPool.length());
            char randomChar = charPool.charAt(randomIndex);
            sb.append(randomChar);
        }

        return sb.toString();
    }

    public static String generateRandomSymbolString(int length) {
        // 定义字符池：数字、字母（大小写）、空格
        String charPool = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            // 随机选择一个字符
            int randomIndex = random.nextInt(charPool.length());
            char randomChar = charPool.charAt(randomIndex);
            sb.append(randomChar);
            if (i < length - 1) sb.append(Constants.TELEGRAPH_TEXT_SEPARATOR);
        }

        return sb.toString();
    }

}