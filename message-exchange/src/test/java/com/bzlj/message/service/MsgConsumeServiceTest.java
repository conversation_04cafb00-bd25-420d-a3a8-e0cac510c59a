package com.bzlj.message.service;

import com.bzlj.message.MessageApplication;
import com.bzlj.message.internaldistribution.service.impl.MsgConsumeServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.testng.AbstractTestNGSpringContextTests;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * 消息消费服务测试类 - 基于 TestNG
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@SpringBootTest(classes = MessageApplication.class)
public class MsgConsumeServiceTest extends AbstractTestNGSpringContextTests {

    @Autowired
    private MsgConsumeServiceImpl msgConsumeServiceImpl;

    /**
     * 测试消息中心消息分发
     */
    @Test(description = "测试消息中心消息分发功能")
    public void testConsumeMessage() {
        // Given
        String messageStr = getMessageStr();

        // When
        String result = msgConsumeServiceImpl.consumeMessage(messageStr);

        // Then
        System.out.println("消息中心消息分发结果: " + result);
        Assert.assertNotNull(result, "消息分发结果不应该为空");
        Assert.assertTrue(result.contains("success") || result.contains("成功"),
                "消息分发应该返回成功状态");
    }

    /**
     * 测试 XBus 消息分发
     */
    @Test(description = "测试 XBus 消息分发功能")
    public void testConsumeXBusMessage() {
        // Given
        String xbusStr = getXBusStr();
        String serviceId = "MTDZD1";

        // When
        String result = msgConsumeServiceImpl.consumeXBusMessage(xbusStr, serviceId, null);

        // Then
        System.out.println("XBus 消息分发结果: " + result);
        Assert.assertNotNull(result, "XBus 消息分发结果不应该为空");
        Assert.assertTrue(result.contains("success") || result.contains("成功"),
                "XBus 消息分发应该返回成功状态");
    }

    /**
     * 测试空消息处理
     */
    @Test(description = "测试空消息的处理")
    public void testConsumeEmptyMessage() {
        // Given
        String emptyMessage = "";

        // When & Then
        try {
            String result = msgConsumeServiceImpl.consumeMessage(emptyMessage);
            System.out.println("空消息处理结果: " + result);
            // 空消息应该被正确处理，不抛异常
        } catch (Exception e) {
            System.out.println("空消息处理异常: " + e.getMessage());
            // 根据业务逻辑，可能需要验证特定的异常类型
        }
    }

    /**
     * 测试无效 JSON 消息处理
     */
    @Test(description = "测试无效 JSON 消息的处理")
    public void testConsumeInvalidJsonMessage() {
        // Given
        String invalidJson = "{ invalid json }";

        // When & Then
        try {
            String result = msgConsumeServiceImpl.consumeMessage(invalidJson);
            System.out.println("无效 JSON 处理结果: " + result);
        } catch (Exception e) {
            System.out.println("无效 JSON 处理异常: " + e.getMessage());
            // 验证异常处理是否符合预期
            Assert.assertNotNull(e.getMessage(), "异常信息不应该为空");
        }
    }

    /**
     * 测试 null 参数处理
     */
    @Test(description = "测试 null 参数的处理")
    public void testConsumeNullMessage() {
        // When & Then
        try {
            String result = msgConsumeServiceImpl.consumeMessage(null);
            System.out.println("null 消息处理结果: " + result);
        } catch (Exception e) {
            System.out.println("null 消息处理异常: " + e.getMessage());
            // 验证 null 参数的处理
        }
    }

    private String getMessageStr() {
        return "{\n" +
                "\"serviceType\": \"\",\n" +
                "\"__resAppEname__\": \"eplat-auth-service\",\n" +
                "\"signature\": \"202505041608:admin:IPLAT4CWEB:IPLAT4C:302c021433903c5d9d9c52b55621226ec27cf9d1d888013002147b9c43962e319c1ac4a59d3ca5fc4cba7fb93e40\",\n" +
                "\"$$remote$$\": \"false\",\n" +
                "\"isServiceAuth\": \"0\",\n" +
                "\"methodName\": \"addGroupRelation\",\n" +
                "\"soaInvokeProtocol\": \"local\",\n" +
                "\"needCleanDiagnostic\": false,\n" +
                "\"serviceName\": \"BEESSubj\",\n" +
                "\"EPES4JUserInfo\": {\n" +
                "\"loginName\": \"admin\",\n" +
                "\"userId\": \"admin\",\n" +
                "\"username\": \"系统管理员\"\n" +
                "},\n" +
                "\"__blocks__\": {\n" +
                "\"Table2\": {\n" +
                "\"meta\": {\n" +
                "\"columns\": [\n" +
                "{\n" +
                "\"pos\": 0,\n" +
                "\"name\": \"ID\",\n" +
                "\"descName\": \"CID\"\n" +
                "},\n" +
                "{\n" +
                "\"pos\": 1,\n" +
                "\"name\": \"NAME\",\n" +
                "\"descName\": \"CNAME\"\n" +
                "}\n" +
                "]\n" +
                "},\n" +
                "\"attr\": {},\n" +
                "\"rows\": [\n" +
                "[\n" +
                "\"子群组标识1\",\n" +
                "\"子群组英文名1\"\n" +
                "],\n" +
                "[\n" +
                "\"子群组标识2\",\n" +
                "\"子群组英文名2\"\n" +
                "]\n" +
                "]\n" +
                "},\n" +
                "\"Table1\": {\n" +
                "\"meta\": {\n" +
                "\"columns\": [\n" +
                "{\n" +
                "\"pos\": 0,\n" +
                "\"name\": \"ID\",\n" +
                "\"descName\": \"CID\",\n" +
                "\"type\":\"N\"\n" +
                "},\n" +
                "{\n" +
                "\"pos\": 1,\n" +
                "\"name\": \"NAME\",\n" +
                "\"descName\": \"CNAME\"\n" +
                "},\n" +
                "{\n" +
                "\"pos\": 2,\n" +
                "\"name\": \"userid\",\n" +
                "\"descName\": \"Cuserid\"\n" +
                "}\n" +
                "]\n" +
                "},\n" +
                "\"attr\": {},\n" +
                "\"rows\": [\n" +
                "[\n" +
                "12.56,\n" +
                "\"目标父群组英文名\",\n" +
                "\"操作人用户ID\"\n" +
                "],\n" +
                "[\n" +
                "1212,\n" +
                "\"目标父群组英文名\",\n" +
                "\"操作人用户ID\"\n" +
                "]\n" +
                "]\n" +
                "}\n" +
                "},\n" +
                "\"__resProjectEname__\": \"eplat\",\n" +
                "\"__sys__\": {\n" +
                "\"msg\": \"\",\n" +
                "\"traceId\": \"0aa5a055121746349439756000010f4\",\n" +
                "\"detailMsg\": \"\",\n" +
                "\"name\": \"\",\n" +
                "\"msgKey\": \"\",\n" +
                "\"descName\": \"\",\n" +
                "\"status\": 0\n" +
                "},\n" +
                "\"__version__\": \"2.0\",\n" +
                "\"serviceId\": \"R_BE_ES_14\"\n" +
                "}";
    }

    private String getXBusStr() {
        return "D$C$3$3$I$S$7$5$z$k$l$S$z$E$i$V$p$F$J$m$I$r$l$6$d$E$U$J$W$G$T$2$z$t$I$s$e$G$Z$i$A$W$M$M$8$I$s$P$Q$0$Y$H$H$N$k$6$o$I$0$o$F$Q$o$j$2$z$o$R$7$c$d$Q$H$U$A$E$1$O$s$Q$c$n$g$1$w$x$X$z$0$X$U$R$E$H$V$u$B$P$l$8$9$W$P$f$4$6$ 2";
    }

}