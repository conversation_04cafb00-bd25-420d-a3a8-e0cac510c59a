package com.bzlj.message;

import com.bzlj.dynamic.mongo.annotations.EnableDynamicMongo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableScheduling
@EnableDynamicMongo
@EnableMongoRepositories(basePackages = {"com.bzlj.message.internaldistribution.repository"})
@SpringBootApplication(exclude = {QuartzAutoConfiguration.class})
public class MessageApplication {
    public static void main(String[] args) {
        SpringApplication.run(MessageApplication.class, args);
    }
}