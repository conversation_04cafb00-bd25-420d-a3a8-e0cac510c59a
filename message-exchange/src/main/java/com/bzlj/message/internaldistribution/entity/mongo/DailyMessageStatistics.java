package com.bzlj.message.internaldistribution.entity.mongo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 每日消息统计实体
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
@Accessors(chain = true)
@Document(collection = "daily_message_statistics")
public class DailyMessageStatistics {

    @Id
    private String id;

    /**
     * 统计日期
     */
    @Field("statistics_date")
    private LocalDate statisticsDate;

    /**
     * 服务ID
     */
    @Field("service_id")
    private String serviceId;

    /**
     * 消息中心消息数量
     */
    @Field("message_center_count")
    private Long messageCenterCount = 0L;

    /**
     * XBus消息数量
     */
    @Field("xbus_message_count")
    private Long xbusMessageCount = 0L;

    /**
     * 总消息数量
     */
    @Field("total_count")
    private Long totalCount = 0L;

    /**
     * 创建时间
     */
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Field("update_time")
    private LocalDateTime updateTime;
}
