package com.bzlj.message.internaldistribution.entity.mongo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 电文验证规则实体
 */
@Data
@Accessors(chain = true)
@Document(collection = "message_rules")
public class MessageRules implements Serializable {

    @Id
    private String id;

    /**
     * 电文号
     */
    @Field("telegraph_text_code")
    private String telegraphTextCode;

    /**
     * 字段名
     */
    @Field("field")
    private String field;

    /**
     * 字段中文名
     */
    @Field("field_name")
    private String fieldName;

    /**
     * 字段类型 C字符串 N数值
     */
    @Field("field_type")
    private String fieldType;

    /**
     * 字段长度
     */
    @Field("field_size")
    private Integer fieldSize;

    /**
     * 精度
     */
    @Field("precision")
    private Integer precision;

    /**
     * 是否必须 0否 1是
     */
    @Field("is_required")
    private Integer isRequired;

    /**
     * 是否循环体 0否 1是
     */
    @Field("is_loop")
    private Integer isLoop;

    /**
     * 排序号
     */
    @Field("sorts")
    private Integer sorts;

    /**
     * 单位
     */
    @Field("unit")
    private String unit;

    /**
     * 缺省值
     */
    @Field("default_value")
    private String defaultValue;

    /**
     * 上限
     */
    @Field("max")
    private String max;

    /**
     * 下限
     */
    @Field("min")
    private String min;

    /**
     * 备注
     */
    @Field("remark")
    private String remark;


}