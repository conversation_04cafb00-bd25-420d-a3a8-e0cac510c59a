package com.bzlj.message.internaldistribution.repository.mongo;

import com.bzlj.message.internaldistribution.entity.mongo.DailyMessageStatistics;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 每日消息统计 Repository
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public interface DailyMessageStatisticsRepository extends MongoRepository<DailyMessageStatistics, String> {

    /**
     * 根据统计日期和服务ID查找统计记录
     *
     * @param statisticsDate 统计日期
     * @param serviceId      服务ID
     * @return 统计记录
     */
    Optional<DailyMessageStatistics> findByStatisticsDateAndServiceId(LocalDate statisticsDate, String serviceId);

    /**
     * 根据统计日期查找所有统计记录
     *
     * @param statisticsDate 统计日期
     * @return 统计记录列表
     */
    List<DailyMessageStatistics> findByStatisticsDate(LocalDate statisticsDate);

    /**
     * 删除指定日期之前的统计记录
     *
     * @param beforeDate 指定日期
     */
    void deleteByStatisticsDateBefore(LocalDate beforeDate);
}
