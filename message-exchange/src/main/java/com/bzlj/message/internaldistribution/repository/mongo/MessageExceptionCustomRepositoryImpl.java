package com.bzlj.message.internaldistribution.repository.mongo;

import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MessageException 自定义查询实现类
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class MessageExceptionCustomRepositoryImpl implements MessageExceptionCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public long countByTypeInAndRetryCountLessThan(List<Integer> types, Integer retryCount) {
        try {
            Query query = new Query();
            
            // 构建查询条件
            Criteria criteria = new Criteria();
            criteria.and("type").in(types);
            criteria.and("retry_count").lt(retryCount);
            
            query.addCriteria(criteria);
            
            long count = mongoTemplate.count(query, MessageException.class);
            log.debug("Count query executed: types={}, retryCount<{}, result={}", types, retryCount, count);
            
            return count;
        } catch (Exception e) {
            log.error("Error executing count query: types={}, retryCount<{}", types, retryCount, e);
            return 0;
        }
    }

    @Override
    public List<MessageException> findByTypeInAndRetryCountLessThanOrderByReceiveTimeAscLimit(
            List<Integer> types, Integer retryCount, int limit) {
        try {
            Query query = new Query();
            
            // 构建查询条件
            Criteria criteria = new Criteria();
            criteria.and("type").in(types);
            criteria.and("retry_count").lt(retryCount);

            query.addCriteria(criteria);

            // 按接收时间升序排序
            query.with(Sort.by(Sort.Direction.ASC, "receive_time"));
            
            // 限制返回数量
            query.limit(limit);
            
            List<MessageException> result = mongoTemplate.find(query, MessageException.class);
            log.debug("Find query executed: types={}, retryCount<{}, limit={}, result size={}", 
                     types, retryCount, limit, result.size());
            
            return result;
        } catch (Exception e) {
            log.error("Error executing find query: types={}, retryCount<{}, limit={}", 
                     types, retryCount, limit, e);
            return List.of();
        }
    }
}
