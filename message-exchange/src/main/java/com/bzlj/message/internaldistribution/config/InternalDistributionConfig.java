package com.bzlj.message.internaldistribution.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 内部分发配置
 *
 * <AUTHOR>
 * @date 2025/5/30 14:55
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "internal.distribution.queue")
public class InternalDistributionConfig {

    /**
     * 队列最大长度
     */
    private Integer size = 10000;

}
