package com.bzlj.message.internaldistribution.repository.mongo;

import com.bzlj.message.internaldistribution.entity.mongo.MessageException;

import java.util.List;

/**
 * MessageException 自定义查询接口
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public interface MessageExceptionCustomRepository {

    /**
     * 查询指定types且重试次数小于指定值的数据总数
     *
     * @param types      异常类型列表
     * @param retryCount 重试次数上限
     * @return 符合条件的记录数
     */
    long countByTypeInAndRetryCountLessThan(List<Integer> types, Integer retryCount);

    /**
     * 查询指定types且重试次数小于指定值的数据，按接收时间排序并限制返回条数
     *
     * @param types      异常类型列表
     * @param retryCount 重试次数上限
     * @param limit      返回记录数量限制
     * @return 符合条件的记录列表
     */
    List<MessageException> findByTypeInAndRetryCountLessThanOrderByReceiveTimeAscLimit(
            List<Integer> types, Integer retryCount, int limit);
}
