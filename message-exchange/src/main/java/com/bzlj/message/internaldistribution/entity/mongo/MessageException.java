package com.bzlj.message.internaldistribution.entity.mongo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * 消息异常实体
 *
 * <AUTHOR>
 * @date 2025/5/13 15:25
 */
@Data
@Accessors(chain = true)
@Document(collection = "message_exception")
public class MessageException {

    @Id
    private String id;

    /**
     * 主题
     */
    @Field("topic")
    private String topic;

    /**
     * 电文号
     */
    @Field("telegraph_text_code")
    private String telegraphTextCode;

    /**
     * 电文体
     */
    @Field("telegraph_text_body")
    private String telegraphTextBody;

    /**
     * 异常信息
     */
    @Field("exceptions_info")
    private String exceptionsInfo;

    /**
     * 创建时间
     */
    @Field("create_time")
    private LocalDateTime createTime;

    /**
     * 接收消息的时间
     */
    @Field("receive_time")
    private LocalDateTime receiveTime;

    /**
     * 异常类型：0、非投递异常  1、投递异常  2、入队异常  3、服务关闭时清理
     */
    @Field("type")
    private Integer type;
    /**
     * 重试次数
     */
    @Field("retry_count")
    private Integer retryCount = 0;

}