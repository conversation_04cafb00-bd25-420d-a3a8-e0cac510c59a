package com.bzlj.message.internaldistribution.dto;

import com.bzlj.message.common.util.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 消息发送数据传输对象
 *
 * <AUTHOR>
 * @date 2025/5/13 19:53
 */
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueueSendDTO {

    /**
     * 主题
     */
    private String topic;

    /**
     * 服务ID/电文号
     */
    private String id;

    /**
     * 消息体
     */
    private String payload;
    /**
     * 消息接收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 重试次数
     */
    private Integer retryCount = 0;


    @Override
    public String toString() {
        return JsonUtils.toJson(this);
    }

}
