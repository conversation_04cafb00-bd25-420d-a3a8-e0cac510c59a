package com.bzlj.message.internaldistribution.service.impl;

import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.common.util.VirtualThreadUtil;
import com.bzlj.message.internaldistribution.config.InternalDistributionConfig;
import com.bzlj.message.internaldistribution.handlers.MessageVerifyHandler;
import com.bzlj.message.internaldistribution.handlers.XBusMessageVerifyHandler;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.service.MessageStatisticsService;
import com.bzlj.message.internaldistribution.service.MsgConsumeService;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 消息分发处理
 *
 * <AUTHOR>
 * @date 2025/5/12 9:49
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MsgConsumeServiceImpl implements MsgConsumeService {

    private final MessageVerifyHandler messageVerifyHandler;
    private final XBusMessageVerifyHandler xBusMessageVerifyHandler;
    private final InternalDistributionConfig internalDistributionConfig;
    private final MessageStatisticsService messageStatisticsService;

    /**
     * 消息中心消息分发
     *
     * @param jsonStr
     */
    @Override
    public String consumeMessage(String jsonStr) {
        // 消息接收时间
        LocalDateTime receiveTime = LocalDateTime.now();

        // 提取serviceId用于统计
        String serviceId = extractServiceIdFromMessage(jsonStr);

        log.error("测控消息--------------{}",jsonStr);
        //队列阈值监控 超过阈值响应失败
        if (QueueManagement.mainQueue().size() >= internalDistributionConfig.getSize()) {
            return getResult("超过本系统设定阈值，请稍后重试", 501);
        }

        // 统计消息中心消息数量并缓存最新电文数据
        try {
            messageStatisticsService.incrementMessageCenterCountWithCache(serviceId, jsonStr);
        } catch (Exception e) {
            log.warn("消息中心计数递增和电文缓存失败，服务ID: {}", serviceId, e);
        }

        //异步处理后续验证逻辑
        VirtualThreadUtil.execute(() -> {
            messageVerifyHandler.verifyHandler(jsonStr, receiveTime);
        });
        return getResult("消息接收成功", 2005);
    }

    /**
     * XBus消息分发
     *
     * @param jsonStr
     */
    @Override
    public String consumeXBusMessage(String jsonStr, String serviceId, HttpServletResponse response) {
        // 消息接收时间
        LocalDateTime receiveTime = LocalDateTime.now();

        //队列阈值监控 超过阈值响应失败
        if (QueueManagement.mainQueue().size() > internalDistributionConfig.getSize()) {
            response.setHeader("returnCode", "1001");
            return getXBusResult("超过本系统设定阈值，请稍后重试", 1001);
        }

        // 统计XBus消息数量并缓存最新电文数据
        try {
            messageStatisticsService.incrementXBusMessageCountWithCache(serviceId, jsonStr);
        } catch (Exception e) {
            log.warn("XBus消息计数递增和电文缓存失败，服务ID: {}", serviceId, e);
        }

        //异步处理后续验证逻辑
        VirtualThreadUtil.execute(() -> {
            xBusMessageVerifyHandler.verifyHandler(jsonStr, serviceId, receiveTime);
        });

        return getXBusResult("消息接收成功", 0);
    }


    /**
     * 消息中心返回值
     * 2005 成功
     * 500-504重试、其他不重试
     *
     * @param message
     * @param code
     * @return
     */
    private String getResult(String message, Integer code) {
        JSONObject userOrderInfo = new JSONObject();
        userOrderInfo.put("__messageCode__", code);
        Map<String, Object> sys = new HashMap<>();
        sys.put("status", 1);
        sys.put("msg", message);
        userOrderInfo.put("__sys__", sys);
        return userOrderInfo.toString();
    }

    /**
     * XBus返回值
     * 0成功
     * 1001~1999失败返回值需要重发区间
     * 1001因内部处理原因造成失败，需要重发
     * 1002同步服务调用失败、超时
     * 1003目标服务未正常启动、目标服务返回的消息、格式无效
     * 2001~2999失败返回值无需重发区间
     * 2001代理服务号为空，无需重发
     *
     * @param message
     * @param returnCode
     * @return
     */
    private String getXBusResult(String message, Integer returnCode) {
        JSONObject result = new JSONObject();
        result.put("returnMessage", message);
        result.put("returnCode", returnCode);
        return result.toString();
    }

    /**
     * 从消息中心消息中提取serviceId
     * 使用 Jackson 基于 JsonUtils 工具类实现
     *
     * @param jsonStr 消息内容
     * @return serviceId
     */
    private String extractServiceIdFromMessage(String jsonStr) {
        try {
            String serviceId = getServiceId(jsonStr);
            if (StringUtils.isBlank(serviceId)) {
                // 判断是否有 messageBody 字段
                String messageBody = JsonUtils.toJsonNode(jsonStr).get("messageBody").asText();
                if (StringUtils.isNotBlank(messageBody)) {
                    serviceId = getServiceId(messageBody);
                }
            }
            // 如果都没有找到，返回unknown
            return StringUtils.isNotBlank(serviceId) ? serviceId : "unknown";

        } catch (Exception e) {
            log.warn("从消息中提取服务ID失败: {}", e.getMessage());
            return "unknown";
        }
    }

    private String getServiceId(String jsonStr) {
        // 使用 JsonUtils 解析 JSON
        JsonNode rootNode = JsonUtils.toJsonNode(jsonStr);

        // 尝试从不同的字段中提取serviceId
        // 1. 直接从根节点获取 serviceId
        JsonNode serviceIdNode = rootNode.get("serviceId");
        if (serviceIdNode != null && !serviceIdNode.isNull()) {
            return serviceIdNode.asText();
        }

        // 2. 尝试获取 __serviceId__
        JsonNode underscoreServiceIdNode = rootNode.get("__serviceId__");
        if (underscoreServiceIdNode != null && !underscoreServiceIdNode.isNull()) {
            return underscoreServiceIdNode.asText();
        }

        // 3. 如果有 __blocks__ 字段，尝试从中提取
        JsonNode blocksNode = rootNode.get("__blocks__");
        if (blocksNode != null && !blocksNode.isNull() && blocksNode.isObject()) {
            JsonNode blocksServiceIdNode = blocksNode.get("serviceId");
            if (blocksServiceIdNode != null && !blocksServiceIdNode.isNull()) {
                return blocksServiceIdNode.asText();
            }
        }

        // 4. 尝试从其他可能的嵌套结构中提取
        // 如果有 data 字段
        JsonNode dataNode = rootNode.get("data");
        if (dataNode != null && !dataNode.isNull() && dataNode.isObject()) {
            JsonNode dataServiceIdNode = dataNode.get("serviceId");
            if (dataServiceIdNode != null && !dataServiceIdNode.isNull()) {
                return dataServiceIdNode.asText();
            }
        }
        return null;
    }

}
