package com.bzlj.message.internaldistribution.service;

import java.time.LocalDate;
import java.util.Map;

/**
 * 消息统计服务接口
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
public interface MessageStatisticsService {

    /**
     * 增加消息中心消息计数
     *
     * @param serviceId 服务ID
     */
    void incrementMessageCenterCount(String serviceId);

    /**
     * 增加消息中心消息计数并缓存最新电文数据
     *
     * @param serviceId 服务ID
     * @param messageData 电文数据
     */
    void incrementMessageCenterCountWithCache(String serviceId, String messageData);

    /**
     * 增加XBus消息计数
     *
     * @param serviceId 服务ID
     */
    void incrementXBusMessageCount(String serviceId);

    /**
     * 增加XBus消息计数并缓存最新电文数据
     *
     * @param serviceId 服务ID
     * @param messageData 电文数据
     */
    void incrementXBusMessageCountWithCache(String serviceId, String messageData);

    /**
     * 获取当日指定服务的统计数据
     *
     * @param serviceId 服务ID
     * @return 统计数据 [messageCenterCount, xbusMessageCount]
     */
    Map<String, Long> getTodayStatistics(String serviceId);

    /**
     * 获取当日所有服务的统计数据
     *
     * @return 统计数据 Map<serviceId, Map<type, count>>
     */
    Map<String, Map<String, Long>> getAllTodayStatistics();

    /**
     * 保存当日统计数据到MongoDB并清理Redis缓存
     *
     * @param date 统计日期
     */
    void saveStatisticsAndClearCache(LocalDate date);

    /**
     * 初始化当日缓存
     */
    void initTodayCache();
}
