package com.bzlj.message.internaldistribution.handlers;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.internaldistribution.dto.MessageSendDTO;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.repository.mongo.MessageRulesRepository;
import com.bzlj.message.internaldistribution.repository.mongo.TopicMapRepository;
import com.bzlj.message.internaldistribution.util.Constants;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * XBus消息验证处理器
 *
 * <AUTHOR>
 * @date 2025/5/13 16:39
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XBusMessageVerifyHandler {

    private final TopicMapRepository topicMapRepository;
    private final MessageRulesRepository messageRulesRepository;
    private final MessageVerifyHandler messageVerifyHandler;

    /**
     * XBus消息验证入队
     *
     * @param jsonStr
     * @param serviceId
     */
//    @Async
    public void verifyHandler(String jsonStr, String serviceId, LocalDateTime receiveTime) {
        try {
            log.info("XBus入参电文:{}", String.format("%s:%s", serviceId, jsonStr));

            //是否是有效电文验证
            TopicMap isExist = topicMapRepository.findByTelegraphTextCode(serviceId);
            if (StrUtil.isEmpty(serviceId) || ObjUtil.isNull(isExist)) {
                messageVerifyHandler.insertMessageException(isExist,
                        jsonStr,
                        "无效电文",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            //电文验证
            ObjectNode convertJson = JsonUtils.createObjectNode();
            if (!dataVerifyRules(jsonStr, serviceId, convertJson)) {
                messageVerifyHandler.insertMessageException(isExist,
                        jsonStr,
                        "格式验证失败",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            //验证通过后加入队列
            QueueSendDTO queueSendDTO = new QueueSendDTO().setTopic(isExist.getTopic())
                    .setId(serviceId)
                    .setPayload(getSendDTO(serviceId, isExist.getType(), convertJson));
            if (!QueueManagement.mainQueue().offer(queueSendDTO)) {
                messageVerifyHandler.insertMessageException(isExist,
                        jsonStr,
                        "队列加入失败",
                        MessageExceptionEnum.ENQUEUE_EXCEPTION.getType(),
                        receiveTime);
            }

        } catch (Exception e) {
            TopicMap isExist = topicMapRepository.findByTelegraphTextCode(serviceId);
            messageVerifyHandler.insertMessageException(isExist,
                    jsonStr,
                    e.getMessage(),
                    MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                    receiveTime);
        }

    }

    /**
     * 数据校验
     * <p>
     * 循环体存在于电文的最后，截取了非循环体的数据后都属于循环体
     *
     * @param jsonStr
     * @param serviceId
     * @return
     */
    private Boolean dataVerifyRules(String jsonStr, String serviceId, ObjectNode convertJson) {
        Boolean flag;
        List<MessageRules> rules = messageRulesRepository.findByTelegraphTextCode(serviceId);
        if (CollUtil.isEmpty(rules) || ObjUtil.isEmpty(jsonStr)) return Boolean.FALSE;
        rules.sort(Comparator.comparingInt(MessageRules::getSorts));

        List<MessageRules> notLoopList = rules.stream()
                .filter(s -> s.getIsLoop() == 0)
                .sorted(Comparator.comparingInt(MessageRules::getSorts)).toList();
        List<MessageRules> loopList = rules.stream()
                .filter(s -> s.getIsLoop() == 1)
                .sorted(Comparator.comparingInt(MessageRules::getSorts)).toList();

        // 计算gbk字节长度用于日志输出
        int gbkByteLength = 0;
        try {
            gbkByteLength = jsonStr.getBytes("GBK").length;
        } catch (UnsupportedEncodingException e) {
            // 如果转换失败，使用字符长度
            gbkByteLength = jsonStr.length();
        }

        log.info("入参电文总长度：{}、gbk字节长度：{}、电文规则总长度：{}、非循环体规则总长度：{}、循环体规则总长度：{}",
                jsonStr.length(), gbkByteLength,
                rules.stream().mapToInt(MessageRules::getFieldSize).sum(),
                notLoopList.stream().mapToInt(MessageRules::getFieldSize).sum(),
                loopList.stream().mapToInt(MessageRules::getFieldSize).sum());

        if (jsonStr.contains(Constants.TELEGRAPH_TEXT_SEPARATOR))
            flag = symbolVerifyRules(rules, notLoopList, loopList, jsonStr, convertJson);
        else flag = strVerifyRules(rules, notLoopList, loopList, jsonStr, convertJson);

        return flag;
    }

    /**
     * 纯电文字符串格式校验
     *
     * @param rules
     * @param jsonStr
     * @param convertJson
     * @return
     */
    private Boolean strVerifyRules(List<MessageRules> rules,
                                   List<MessageRules> notLoopList,
                                   List<MessageRules> loopList,
                                   String jsonStr,
                                   ObjectNode convertJson) {

        // 保留原始UTF-8字符串用于最终赋值
        String originalJsonStr = jsonStr;

        // 计算字符串在gbk编码下的字节长度
        int gbkByteLength;
        try {
            gbkByteLength = jsonStr.getBytes("GBK").length;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("字符串编码转换出错: " + e.getMessage());
        }

        //非循环体长度
        int notLoopSum = notLoopList.stream().mapToInt(MessageRules::getFieldSize).sum();
        //循环体长度
        int loopSum = loopList.stream().mapToInt(MessageRules::getFieldSize).sum();
        if (gbkByteLength < notLoopSum + loopSum) return Boolean.FALSE;

        //基础长度校验
        if (notLoopSum > 0 && loopSum > 0) {
            // 计算循环体部分的字节长度
            int loopByteLength = gbkByteLength - notLoopSum;
            int loop = loopByteLength % loopSum;
            //循环体取余不尽为长度异常
            if (loop > 0) return Boolean.FALSE;
        } else if (notLoopSum > 0 && gbkByteLength != notLoopSum) {
            return Boolean.FALSE;
        } else if (loopSum > 0) {
            int loop = gbkByteLength % loopSum;
            //循环体取余不尽为长度异常
            if (loop > 0) return Boolean.FALSE;
        }

        //循环体的集合
        List<Map<String, String>> loopItems = new ArrayList<>();
        AtomicInteger currentByteIndex = new AtomicInteger(0);

        // 第一遍处理所有非循环字段和循环体第一次实例
        Map<String, String> loopItem = new LinkedHashMap<>();
        for (MessageRules rule : rules) {
            if (currentByteIndex.intValue() >= gbkByteLength) break;

            // 计算在原始UTF-8字符串中对应的字节位置
            String utf8Value = extractUtf8ValueBygbkPosition(originalJsonStr,
                    currentByteIndex.intValue(), rule.getFieldSize());

            String finalValue = utf8Value.trim();
            if (!"C".equals(rule.getFieldType())) {
                // 数值型字段处理
                finalValue = parseNumericValue(finalValue, rule.getFieldSize(), rule.getPrecision());
            }
            if (rule.getIsLoop() == 1) {
                // 处理循环体第一次实例
                loopItem.put(rule.getField(), finalValue);
            } else {
                convertJson.put(rule.getField(), finalValue);
            }
            currentByteIndex.addAndGet(rule.getFieldSize());
        }
        if (!loopItem.isEmpty()) loopItems.add(loopItem);

        //剩余字符串字节长度
        int remaining = gbkByteLength - currentByteIndex.intValue();
        //循环次数
        int maxInstances = 0;
        if (loopSum > 0) maxInstances = remaining / loopSum;

        // 第二遍处理剩余循环体实例
        for (int i = 0; i < maxInstances; i++) {
            Map<String, String> loopItem1 = new LinkedHashMap<>();
            for (MessageRules rule : loopList) {
                // 计算在原始UTF-8字符串中对应的字节位置
                String utf8Value = extractUtf8ValueBygbkPosition(originalJsonStr,
                        currentByteIndex.intValue(), rule.getFieldSize());

                String finalValue = utf8Value.trim();
                if (!"C".equals(rule.getFieldType())) {
                    // 数值型字段处理
                    finalValue = parseNumericValue(finalValue, rule.getFieldSize(), rule.getPrecision());
                }
                loopItem1.put(rule.getField(), finalValue);
                currentByteIndex.addAndGet(rule.getFieldSize());
            }
            loopItems.add(loopItem1);
        }

        if (!loopItems.isEmpty()) {
            ArrayNode loopArray = JsonUtils.createArrayNode();
            for (Map<String, String> item : loopItems) {
                ObjectNode itemNode = JsonUtils.createObjectNode();
                for (Map.Entry<String, String> entry : item.entrySet()) {
                    itemNode.put(entry.getKey(), entry.getValue());
                }
                loopArray.add(itemNode);
            }
            convertJson.set(Constants.LOOP_KEY, loopArray);
        }

        return Boolean.TRUE;
    }


    /**
     * 根据gbk字符位置从UTF-8字符串中提取对应的值
     *
     * @param utf8Str UTF-8编码的原始字符串
     * @param gbkStartPos gbk编码中的起始位置
     * @param gbkLength gbk编码中的长度
     * @return 对应的UTF-8字符串片段
     */
    private String extractUtf8ValueBygbkPosition(String utf8Str, int gbkStartPos, int gbkLength) {
        try {
            // 将UTF-8字符串转换为gbk字节数组
            byte[] gbkBytes = utf8Str.getBytes("GBK");

            // 检查边界
            if (gbkStartPos >= gbkBytes.length) {
                return "";
            }

            int endPos = Math.min(gbkStartPos + gbkLength, gbkBytes.length);

            // 提取gbk字节片段
            byte[] targetBytes = Arrays.copyOfRange(gbkBytes, gbkStartPos, endPos);

            // 将gbk字节转换回UTF-8字符串
            return new String(targetBytes, "GBK");

        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("字符编码转换出错: " + e.getMessage());
        }
    }

    /**
     * 符号分割电文校验
     *
     * @param rules
     * @param jsonStr
     * @param convertJson
     * @return
     */
    private Boolean symbolVerifyRules(List<MessageRules> rules, List<MessageRules> notLoopList, List<MessageRules> loopList, String jsonStr, ObjectNode convertJson) {

        List<String> list = Arrays.asList(jsonStr.split(Constants.BACKSLASH + Constants.TELEGRAPH_TEXT_SEPARATOR, 0));
//        if (list.size() < notLoopList.size() + loopList.size()) return Boolean.FALSE;

        List<String> notLoopStrArr = new ArrayList<>();
        List<String> loopStrArr = new ArrayList<>();

        // 第一遍处理所有
        StringBuilder temp = new StringBuilder();
        for (int i = 0; i < Math.min(rules.size(), list.size()); i++) {
            MessageRules messageRules = rules.get(i);
            if (messageRules.getIsLoop() == 1) {
                if (StrUtil.isNotEmpty(temp)) temp.append(",,");
                temp.append(list.get(i));
            } else {
                notLoopStrArr.add(list.get(i));
            }
        }
        if (StrUtil.isNotEmpty(temp)) loopStrArr.add(temp.toString());

        // 剩余的元素根据循环体规则长度进行分组
        int startIndex = rules.size();
        // 循环体取余不尽为长度异常
        if ((list.size() - startIndex) > 0 && (list.size() - startIndex) % loopList.size() > 0) {
            return Boolean.FALSE;
        }
        for (int i = startIndex; i < list.size(); i += loopList.size()) {
            StringBuilder group = new StringBuilder();
            for (int j = 0; j < loopList.size() && i + j < list.size(); j++) {
                if (j > 0) group.append(",,");
                group.append(list.get(i + j));
            }
            loopStrArr.add(group.toString());
        }

        //非循环体验证
        if (!symbolVerify(notLoopList, convertJson, notLoopStrArr)) return Boolean.FALSE;

        //循环体验证
        ArrayNode objects = JsonUtils.createArrayNode();
        for (String str : loopStrArr) {
            ObjectNode jsonObj = JsonUtils.createObjectNode();
            String[] split = str.split(",,", 0);
            if(Arrays.stream(split).allMatch(StringUtils::isBlank)) continue;
            List<String> tempList = Arrays.asList(split);
            if (!symbolVerify(loopList, jsonObj, tempList)) return Boolean.FALSE;
            objects.add(jsonObj);
        }

        if (!objects.isEmpty()) convertJson.set(Constants.LOOP_KEY, objects);

        return Boolean.TRUE;
    }

    /**
     * 数据必填验证
     *
     * @param loopList 验证规则
     * @param jsonObj  jsonObj
     * @param tempList 需验证字段集合
     * @return
     */
    private Boolean symbolVerify(List<MessageRules> loopList, ObjectNode jsonObj, List<String> tempList) {
        for (int i = 0; i < tempList.size(); i++) {
            String node = tempList.get(i);
            MessageRules rule = loopList.get(i);
            jsonObj.put(rule.getField(), node);
            if (rule.getIsRequired() == 1 && StrUtil.isBlank(node)) return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 投递消息组装
     *
     * @param serviceId
     * @param type
     * @param convertJson
     * @return
     */
    private String getSendDTO(String serviceId, String type, ObjectNode convertJson) {
        MessageSendDTO sendDTO = new MessageSendDTO();
        sendDTO.setId(serviceId);
        sendDTO.setPayload(JsonUtils.toJson(convertJson));
        sendDTO.setReceiptTime(DateUtil.toInstant(LocalDateTime.now()).toEpochMilli());
        sendDTO.setType(type);
        return sendDTO.toString();
    }

    /**
     * 解析数值型字段
     *
     * @param value 原始值
     * @param fieldSize 字段长度
     * @param precision 精度（小数位数）
     * @return 格式化后的数值字符串
     */
    private String parseNumericValue(String value, Integer fieldSize, Integer precision) {
        if (StrUtil.isBlank(value)) {
            return value;
        }

        // 如果精度为null或0，直接返回去除前导零的整数
        if (precision == null || precision == 0) {
            try {
                // 去除前导零，但保留至少一个0
                long longValue = Long.parseLong(value);
                return String.valueOf(longValue);
            } catch (NumberFormatException e) {
                log.warn("数值解析失败，原始值: {}, 字段长度: {}, 精度: {}", value, fieldSize, precision);
                return value;
            }
        }

        try {
            // 有精度的情况，需要插入小数点
            if (value.length() < precision) {
                // 如果值的长度小于精度，在前面补0
                value = String.format("%0" + precision + "d", Long.parseLong(value));
            }

            // 插入小数点
            int decimalPointPosition = value.length() - precision;
            String integerPart = value.substring(0, decimalPointPosition);
            String decimalPart = value.substring(decimalPointPosition);

            // 如果整数部分为空，设为0
            if (StrUtil.isBlank(integerPart)) {
                integerPart = "0";
            } else {
                // 去除整数部分的前导零，但保留至少一个0
                integerPart = String.valueOf(Long.parseLong(integerPart));
            }

            return integerPart + "." + decimalPart;

        } catch (Exception e) {
            log.warn("数值解析失败，原始值: {}, 字段长度: {}, 精度: {}, 错误: {}",
                    value, fieldSize, precision, e.getMessage());
            return value;
        }
    }

}

