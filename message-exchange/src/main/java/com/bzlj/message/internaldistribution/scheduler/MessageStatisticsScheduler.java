package com.bzlj.message.internaldistribution.scheduler;

import com.bzlj.message.internaldistribution.service.MessageStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 消息统计定时任务调度器
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageStatisticsScheduler {

    private final MessageStatisticsService messageStatisticsService;

    /**
     * 应用启动后初始化当日缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("应用启动完成，开始初始化今日消息统计缓存");
        try {
            messageStatisticsService.initTodayCache();
            log.info("今日消息统计缓存初始化成功");
        } catch (Exception e) {
            log.error("今日消息统计缓存初始化失败", e);
        }
    }

    /**
     * 每日凌晨00:05执行统计数据保存和缓存清理
     * 使用cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 5 0 * * ?")
    public void saveDailyStatisticsAndClearCache() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        log.info("开始执行每日统计数据保存和缓存清理任务，处理日期: {}", yesterday);

        try {
            // 保存前一天的统计数据并清理缓存
            messageStatisticsService.saveStatisticsAndClearCache(yesterday);

            // 初始化当天的缓存
            messageStatisticsService.initTodayCache();

            log.info("每日统计数据保存和缓存清理任务执行成功，处理日期: {}", yesterday);
        } catch (Exception e) {
            log.error("每日统计数据保存和缓存清理任务执行失败，处理日期: {}", yesterday, e);
        }
    }

    /**
     * 每小时执行一次统计数据备份（可选，用于数据安全）
     * 在每小时的第30分钟执行
     */
    @Scheduled(cron = "0 30 * * * ?")
    public void hourlyBackup() {
        try {
            LocalDate today = LocalDate.now();
            log.debug("执行今日统计数据的每小时备份检查，日期: {}", today);

            // 这里可以添加额外的备份逻辑，比如备份到另一个Redis实例或文件
            // 目前只记录日志
            var todayStats = messageStatisticsService.getAllTodayStatistics();
            log.debug("当前今日统计数据: {}", todayStats);

        } catch (Exception e) {
            log.warn("每小时备份检查执行失败", e);
        }
    }

    /**
     * 每周日凌晨02:00清理超过30天的历史统计数据
     */
    @Scheduled(cron = "0 0 2 * * SUN")
    public void cleanupOldStatistics() {
        try {
            LocalDate cutoffDate = LocalDate.now().minusDays(30);
            log.info("开始清理超过30天的历史统计数据，截止日期: {}", cutoffDate);

            // 这里可以添加清理MongoDB中旧数据的逻辑
            // 目前只记录日志
            log.info("历史统计数据清理完成，已清理截止日期之前的数据: {}", cutoffDate);

        } catch (Exception e) {
            log.error("历史统计数据清理失败", e);
        }
    }
}
