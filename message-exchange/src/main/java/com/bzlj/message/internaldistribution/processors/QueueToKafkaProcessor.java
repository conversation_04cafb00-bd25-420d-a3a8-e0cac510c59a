package com.bzlj.message.internaldistribution.processors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bzlj.message.internaldistribution.dto.MessageSendDTO;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.handlers.MessageVerifyHandler;
import com.bzlj.message.internaldistribution.producer.VirtualThreadKafkaProducer;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.repository.mongo.MessageExceptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.SmartLifecycle;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
@RequiredArgsConstructor
public class QueueToKafkaProcessor implements SmartLifecycle {

//    private final StreamBridge streamBridge;
    private final VirtualThreadKafkaProducer virtualThreadKafkaProducer;
    private final MessageExceptionRepository messageExceptionRepository;
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final MessageVerifyHandler messageVerifyHandler;
    private static final ThreadPoolExecutor INTERNAL_DISTRIBUTION_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 4,
            5,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(10000),
            new CustomizableThreadFactory("InternalDistribution-Queue-Thread-"));

    @Override
    public void start() {
        if (running.compareAndSet(false, true)) {
            INTERNAL_DISTRIBUTION_POOL.execute(this::processQueue);
        }
    }

    /**
     * 关闭时队列消息落库
     */
    @Override
    public void stop() {
        if (running.compareAndSet(true, false)) {
            INTERNAL_DISTRIBUTION_POOL.shutdown();
            try {
                // 等待现有任务完成
                if (!INTERNAL_DISTRIBUTION_POOL.awaitTermination(30, TimeUnit.SECONDS)) {
                    INTERNAL_DISTRIBUTION_POOL.shutdownNow(); // 取消等待中的任务
                    // 再次等待任务响应中断
                    if (!INTERNAL_DISTRIBUTION_POOL.awaitTermination(30, TimeUnit.SECONDS)) {
                        log.error("线程池未正常终止");
                    }
                }
            } catch (InterruptedException e) {
                INTERNAL_DISTRIBUTION_POOL.shutdownNow();
                Thread.currentThread().interrupt();
            }

            // 内存队列消息落库
            List<Object> mainQueueItems = new ArrayList<>();
            QueueManagement.mainQueue().drainTo(mainQueueItems);
//            QueueManagement.priorityQueue().drainTo(mainQueueItems);

            LocalDateTime now = LocalDateTime.now();
            List<MessageException> messageExceptionList = new ArrayList<>();
            for (Object mainQueueItem : mainQueueItems) {
                JSONObject jsonObject = new JSONObject(mainQueueItem);
                QueueSendDTO bean = JSONUtil.toBean(jsonObject, QueueSendDTO.class);
                MessageException messageException = new MessageException()
                        .setCreateTime(now)
                        .setTopic(bean.getTopic())
                        .setTelegraphTextCode(bean.getId())
                        .setTelegraphTextBody(bean.getPayload())
                        .setType(MessageExceptionEnum.SERVICE_EXCEPTION.getType())
                        .setExceptionsInfo(MessageExceptionEnum.SERVICE_EXCEPTION.getMsg())
                        .setReceiveTime(bean.getReceiveTime())
                        .setRetryCount(bean.getRetryCount() != null ? bean.getRetryCount() : 0);
                messageExceptionList.add(messageException);
            }
            if (CollUtil.isNotEmpty(messageExceptionList)) messageExceptionRepository.insert(messageExceptionList);
        }
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    /**
     * 消息发送
     */
    private void processQueue() {
        while (running.get()) {
            QueueSendDTO take = new QueueSendDTO();
            try {
                //阻塞获取主队列消息
                take = (QueueSendDTO) QueueManagement.mainQueue().take();
                handleMessage(take);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                // 处理异常，记录日志
                String payload = take.getPayload();
                MessageSendDTO bean = JSONUtil.toBean(payload, MessageSendDTO.class);
                TopicMap topicMap = new TopicMap();
                topicMap.setTopic(take.getTopic());
                topicMap.setTelegraphTextCode(take.getId());
                messageVerifyHandler.insertMessageException(topicMap,
                        bean.toString(),
                        "kafka投递异常....." + e.getMessage(),
                        MessageExceptionEnum.SEND_EXCEPTION.getType(),
                        take.getReceiveTime());
            }
        }
    }

    private void handleMessage(QueueSendDTO queueSend) throws InterruptedException {
        String payload = queueSend.getPayload();
        MessageSendDTO bean = JSONUtil.toBean(payload, MessageSendDTO.class);
        if (ObjUtil.isNotNull(bean)){
            bean.setSendTime(DateUtil.toInstant(LocalDateTime.now()).toEpochMilli());
        }
        virtualThreadKafkaProducer.sendMessage(queueSend);
//        // 增加重试机制
//        AtomicInteger retryCount = new AtomicInteger(0);
//        boolean sent = false;
//        while (retryCount.get() < 3 && !sent) {
//            // 使用 StreamBridge 发送消息
//            sent = streamBridge.send(queueSend.getTopic(), bean.toString());
//            if (!sent) {
//                retryCount.incrementAndGet();
//                Thread.sleep(1000L * retryCount.get()); // 指数退避
//            }
//        }
//
//        // 处理发送失败的情况
//        if (!sent) {
//            TopicMap topicMap = new TopicMap();
//            topicMap.setTopic(queueSend.getTopic());
//            topicMap.setTelegraphTextCode(bean.getId());
//            messageVerifyHandler.insertMessageException(topicMap,
//                    bean.toString(),
//                    "kafka投递异常.....",
//                    MessageExceptionEnum.SEND_EXCEPTION.getType(),
//                    queueSend.getReceiveTime());
//        }
    }

}