package com.bzlj.message.internaldistribution.controller;

import com.bzlj.message.internaldistribution.entity.mongo.DailyMessageStatistics;
import com.bzlj.message.internaldistribution.repository.mongo.DailyMessageStatisticsRepository;
import com.bzlj.message.internaldistribution.service.MessageStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 消息统计查询控制器
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@RestController
@RequestMapping("/statistics")
@RequiredArgsConstructor
@Tag(name = "消息统计", description = "消息统计相关接口")
public class MessageStatisticsController {

    private final MessageStatisticsService messageStatisticsService;
    private final DailyMessageStatisticsRepository statisticsRepository;

    @GetMapping("/today")
    @Operation(summary = "获取今日实时统计", description = "获取今日所有服务的实时消息统计数据")
    public ResponseEntity<Map<String, Map<String, Long>>> getTodayStatistics() {
        try {
            Map<String, Map<String, Long>> statistics = messageStatisticsService.getAllTodayStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取今日实时统计失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/today/{serviceId}")
    @Operation(summary = "获取指定服务今日实时统计", description = "获取指定服务今日的实时消息统计数据")
    public ResponseEntity<Map<String, Long>> getTodayStatisticsByServiceId(
            @Parameter(description = "服务ID") @PathVariable String serviceId) {
        try {
            Map<String, Long> statistics = messageStatisticsService.getTodayStatistics(serviceId);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取指定服务今日实时统计失败，服务ID: {}", serviceId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/daily")
    @Operation(summary = "获取指定日期的历史统计", description = "获取指定日期的历史消息统计数据")
    public ResponseEntity<List<DailyMessageStatistics>> getDailyStatistics(
            @Parameter(description = "统计日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        try {
            List<DailyMessageStatistics> statistics = statisticsRepository.findByStatisticsDate(date);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取指定日期历史统计失败，日期: {}", date, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/daily/{serviceId}")
    @Operation(summary = "获取指定服务指定日期的历史统计", description = "获取指定服务在指定日期的历史消息统计数据")
    public ResponseEntity<DailyMessageStatistics> getDailyStatisticsByServiceId(
            @Parameter(description = "服务ID") @PathVariable String serviceId,
            @Parameter(description = "统计日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        try {
            return statisticsRepository.findByStatisticsDateAndServiceId(date, serviceId)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("获取指定服务指定日期历史统计失败，服务ID: {}，日期: {}", serviceId, date, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/range")
    @Operation(summary = "获取日期范围内的历史统计", description = "获取指定日期范围内的历史消息统计数据")
    public ResponseEntity<List<DailyMessageStatistics>> getStatisticsInRange(
            @Parameter(description = "开始日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @Parameter(description = "服务ID，可选") 
            @RequestParam(required = false) String serviceId) {
        try {
            // 这里可以实现日期范围查询，目前简化处理
            List<DailyMessageStatistics> statistics;
            if (serviceId != null && !serviceId.trim().isEmpty()) {
                // 如果指定了serviceId，需要实现自定义查询
                statistics = List.of(); // 简化处理
            } else {
                // 获取开始日期的数据作为示例
                statistics = statisticsRepository.findByStatisticsDate(startDate);
            }
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取日期范围内历史统计失败，开始日期: {}，结束日期: {}", startDate, endDate, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/manual-save")
    @Operation(summary = "手动保存统计数据", description = "手动触发保存指定日期的统计数据到MongoDB")
    public ResponseEntity<String> manualSaveStatistics(
            @Parameter(description = "统计日期，格式：yyyy-MM-dd") 
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        try {
            messageStatisticsService.saveStatisticsAndClearCache(date);
            return ResponseEntity.ok("统计数据保存成功，日期: " + date);
        } catch (Exception e) {
            log.error("手动保存统计数据失败，日期: {}", date, e);
            return ResponseEntity.internalServerError().body("统计数据保存失败: " + e.getMessage());
        }
    }
}
