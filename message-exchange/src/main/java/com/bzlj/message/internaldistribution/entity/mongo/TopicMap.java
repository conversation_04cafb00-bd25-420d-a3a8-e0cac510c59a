package com.bzlj.message.internaldistribution.entity.mongo;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;

/**
 * 电文主题映射实体
 *
 * <AUTHOR>
 * @date 2025/5/13 14:25
 */
@Data
@Document(collection = "topic_map")
public class TopicMap implements Serializable {

    @Id
    private String id;

    /**
     * 主题
     */
    @Field("topic")
    private String topic;

    /**
     * 电文号
     */
    @Field("telegraph_text_code")
    private String telegraphTextCode;

    /**
     * 电文描述
     */
    @Field("description")
    private String description;

    /**
     * 二级分类
     */
    @Field("type")
    private String type;

    /**
     * 二级分类描述
     */
    @Field("type_description")
    private String typeDescription;

}
