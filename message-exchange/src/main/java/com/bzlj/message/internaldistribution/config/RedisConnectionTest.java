package com.bzlj.message.internaldistribution.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * Redis 连接测试类
 * 应用启动时测试 Redis 连接，支持单机、集群、哨兵三种模式
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisConnectionTest implements CommandLineRunner {

    private final RedissonClient redissonClient;
    private final RedissonProperties redissonProperties;

    @Override
    public void run(String... args) {
        try {
            // 测试 Redis 连接
            String testKey = "redis:connection:test:" + System.currentTimeMillis();
            String testValue = "连接测试成功";

            // 设置测试值
            redissonClient.getBucket(testKey).set(testValue);

            // 获取测试值
            Object result = redissonClient.getBucket(testKey).get();

            if (testValue.equals(result)) {
                log.info("✅ Redis 连接测试成功！");
                printRedisConfigInfo();
            } else {
                log.warn("⚠️ Redis 连接测试失败：值不匹配");
            }

            // 清理测试数据
            redissonClient.getBucket(testKey).delete();

        } catch (Exception e) {
            log.error("❌ Redis 连接测试失败", e);
            log.warn("💡 请检查 Redis 服务是否正常运行以及配置是否正确");
            printRedisConfigInfo();
        }
    }

    /**
     * 打印 Redis 配置信息，支持所有部署模式
     */
    private void printRedisConfigInfo() {
        try {
            String mode = redissonProperties.getMode().toLowerCase();
            log.info("📊 Redis 配置信息:");
            log.info("   - 部署模式: {}", mode);

            switch (mode) {
                case "single":
                    printSingleServerInfo();
                    break;
                case "cluster":
                    printClusterServerInfo();
                    break;
                case "sentinel":
                    printSentinelServerInfo();
                    break;
                default:
                    log.info("   - 未知部署模式: {}", mode);
                    break;
            }

        } catch (Exception e) {
            log.warn("获取 Redis 配置信息失败", e);
        }
    }

    /**
     * 打印单机模式配置信息
     */
    private void printSingleServerInfo() {
        try {
            RedissonProperties.SingleServerConfig config = redissonProperties.getSingleServerConfig();
            log.info("   - 服务器地址: {}", config.getAddress());
            log.info("   - 数据库索引: {}", config.getDatabase());
            log.info("   - 连接池大小: {}", config.getConnectionPoolSize());
            log.info("   - 最小空闲连接: {}", config.getConnectionMinimumIdleSize());
            log.info("   - 连接超时: {}ms", config.getConnectTimeout());
            log.info("   - 命令超时: {}ms", config.getTimeout());

            // 显示实际配置类型
            try {
                Config actualConfig = redissonClient.getConfig();
                if (actualConfig.isClusterConfig()) {
                    log.info("   - 实际配置类型: 集群模式");
                } else if (actualConfig.isSentinelConfig()) {
                    log.info("   - 实际配置类型: 哨兵模式");
                } else {
                    log.info("   - 实际配置类型: 单机模式");
                }
            } catch (Exception e) {
                log.debug("无法获取实际配置类型: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.warn("获取单机模式配置信息失败", e);
        }
    }

    /**
     * 打印集群模式配置信息
     */
    private void printClusterServerInfo() {
        try {
            RedissonProperties.ClusterConfig config = redissonProperties.getClusterConfig();
            log.info("   - 集群节点数量: {}", config.getNodeAddresses().size());
            log.info("   - 集群节点地址:");
            for (int i = 0; i < config.getNodeAddresses().size(); i++) {
                log.info("     {}. {}", i + 1, config.getNodeAddresses().get(i));
            }
            log.info("   - 主节点连接池大小: {}", config.getMasterConnectionPoolSize());
            log.info("   - 从节点连接池大小: {}", config.getSlaveConnectionPoolSize());
            log.info("   - 读取模式: {}", config.getReadMode());
            log.info("   - 订阅模式: {}", config.getSubscriptionMode());
            log.info("   - 扫描间隔: {}ms", config.getScanInterval());

            // 尝试从实际配置中获取信息
            try {
                Config actualConfig = redissonClient.getConfig();
                if (actualConfig.isClusterConfig()) {
                    log.info("   - 实际配置类型: 集群模式");
                    log.info("   - 集群配置已激活");
                } else {
                    log.info("   - 警告: 配置为集群模式但实际运行在其他模式");
                }
            } catch (Exception e) {
                log.debug("无法获取实际配置信息: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.warn("获取集群模式配置信息失败", e);
        }
    }

    /**
     * 打印哨兵模式配置信息
     */
    private void printSentinelServerInfo() {
        try {
            RedissonProperties.SentinelConfig config = redissonProperties.getSentinelConfig();
            log.info("   - 主服务器名称: {}", config.getMasterName());
            log.info("   - 数据库索引: {}", config.getDatabase());
            log.info("   - 哨兵节点数量: {}", config.getSentinelAddresses().size());
            log.info("   - 哨兵节点地址:");
            for (int i = 0; i < config.getSentinelAddresses().size(); i++) {
                log.info("     {}. {}", i + 1, config.getSentinelAddresses().get(i));
            }
            log.info("   - 主节点连接池大小: {}", config.getMasterConnectionPoolSize());
            log.info("   - 从节点连接池大小: {}", config.getSlaveConnectionPoolSize());
            log.info("   - 读取模式: {}", config.getReadMode());
            log.info("   - 订阅模式: {}", config.getSubscriptionMode());

            // 尝试从实际配置中获取信息
            try {
                Config actualConfig = redissonClient.getConfig();
                if (actualConfig.isSentinelConfig()) {
                    log.info("   - 实际配置类型: 哨兵模式");
                    log.info("   - 哨兵配置已激活");
                } else {
                    log.info("   - 警告: 配置为哨兵模式但实际运行在其他模式");
                }
            } catch (Exception e) {
                log.debug("无法获取实际配置信息: {}", e.getMessage());
            }
        } catch (Exception e) {
            log.warn("获取哨兵模式配置信息失败", e);
        }
    }


}
