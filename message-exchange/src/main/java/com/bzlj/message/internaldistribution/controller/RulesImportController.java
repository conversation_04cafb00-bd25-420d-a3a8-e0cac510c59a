package com.bzlj.message.internaldistribution.controller;

import cn.hutool.core.util.StrUtil;
import com.bzlj.message.internaldistribution.entity.mongo.MessageRules;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.repository.mongo.MessageRulesRepository;
import com.bzlj.message.internaldistribution.repository.mongo.TopicMapRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class RulesImportController {

    private final MessageRulesRepository messageRulesRepository;
    private final TopicMapRepository topicMapRepository;

    @PostMapping("saveBatchTopicMap")
    public void temp(@RequestBody List<TopicMap> topicMaps) {
        System.err.println(topicMapRepository.insert(topicMaps));
    }

    /**
     * 批量插入规则数据
     *
     * @param inJsonString
     * @throws JsonProcessingException
     */
    @PostMapping("/saveBatch")
    public void consumeMessage(@RequestBody String inJsonString) throws JsonProcessingException {

        ObjectMapper objectMapper = new ObjectMapper();
        List<MessageRules> allFields = new ArrayList<>();

        try {
            JsonNode rootNode = objectMapper.readTree(inJsonString);

            // 解析 "mes" 部分
            JsonNode mesNode = rootNode.get("mes");
            if (mesNode != null && mesNode.isObject()) {
                extractFields(mesNode, allFields);
            }

            // 解析 "inspection" 部分
            JsonNode inspectionNode = rootNode.get("inspection");
            if (inspectionNode != null && inspectionNode.isObject()) {
                extractFields(inspectionNode, allFields);
            }

            // 输出所有提取的字段
            //for (MessageRules field : allFields) {
            //    System.out.println(field);
            //}

            messageRulesRepository.insert(allFields);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 递归遍历 JsonNode 并提取最内层的字段对象
     *
     * @param node       当前遍历的 JsonNode
     * @param fieldsList 存储提取的字段对象的列表
     */
    private static void extractFields(JsonNode node, List<MessageRules> fieldsList) {
        Iterator<String> sectionNames = node.fieldNames();
        while (sectionNames.hasNext()) {
            String sectionName = sectionNames.next(); // 如 "MTDT10", "MTDT11"
            JsonNode sectionArray = node.get(sectionName);

            if (sectionArray != null && sectionArray.isArray()) {
                for (JsonNode fieldNode : sectionArray) {
                    if (fieldNode.isObject()) {
                        MessageRules field = parseField(fieldNode);
                        if (field != null) {
                            fieldsList.add(field);
                        }
                    }
                }
            }
        }
    }

    /**
     * 解析单个字段对象
     *
     * @param fieldNode 表示单个字段对象的 JsonNode
     * @return 解析后的 Field 对象，如果节点不完整则返回 null
     */
    private static MessageRules parseField(JsonNode fieldNode) {
        String telegraphTextCode = getFieldValue(fieldNode, "telegraph_text_code");
        int isLoop = getFieldValueAsInt(fieldNode, "is_loop", 0);
        int sorts = getFieldValueAsInt(fieldNode, "sorts", 0);
        String field = getFieldValue(fieldNode, "field");
        String fieldName = getFieldValue(fieldNode, "field_name");
        int isRequired = getFieldValueAsInt(fieldNode, "is_required", 0);
        String fieldType = getFieldValue(fieldNode, "field_type");
        int fieldSize = getFieldValueAsInt(fieldNode, "field_size", 0);
        Integer precision = parseStringToInt(fieldNode, "field_size");
        String unit = getFieldValue(fieldNode, "unit");
        String defaultValue = getFieldValue(fieldNode, "default_value");
        String max = getFieldValue(fieldNode, "max");
        String min = getFieldValue(fieldNode, "min");
        String remark = getFieldValue(fieldNode, "remark");

        MessageRules messageRules = new MessageRules()
                .setTelegraphTextCode(telegraphTextCode).setIsLoop(isLoop)
                .setSorts(sorts).setField(field).setFieldName(fieldName)
                .setIsRequired(isRequired).setFieldType(fieldType)
                .setFieldSize(fieldSize).setUnit(unit).setDefaultValue(defaultValue)
                .setMax(max).setMin(min).setRemark(remark).setPrecision(precision);

        return messageRules;
    }

    /**
     * 获取 JsonNode 的字符串值，如果不存在则返回默认值或空字符串
     */
    private static String getFieldValue(JsonNode node, String fieldName) {
        return node.hasNonNull(fieldName) ? node.get(fieldName).asText() : "";
    }

    /**
     * 获取 JsonNode 的整数值，如果不存在则返回默认值
     */
    private static int getFieldValueAsInt(JsonNode node, String fieldName, int defaultValue) {
        return node.hasNonNull(fieldName) ? node.get(fieldName).asInt() : defaultValue;
    }


    public static Integer parseStringToInt(JsonNode node, String fieldName) {

        String input = node.hasNonNull(fieldName) ? node.get(fieldName).asText() : null;

        // 处理空值和空字符串
        if (input == null || input.isEmpty())
            return null;

        // 分割字符串（最多分割为2部分）
        String[] parts = input.split("\\.");

        String firstSegment = null;
        if (parts.length == 2) {
            firstSegment = parts[1].trim();
        }

        // 处理空字符串的情况
        if (StrUtil.isEmpty(firstSegment))
            return null;

        try {
            return Integer.parseInt(firstSegment);
        } catch (NumberFormatException e) {
            // 处理非数字内容
            return null;
        }
    }

}
