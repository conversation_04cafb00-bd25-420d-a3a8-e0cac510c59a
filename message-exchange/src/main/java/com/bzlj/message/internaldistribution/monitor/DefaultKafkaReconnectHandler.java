package com.bzlj.message.internaldistribution.monitor;

import com.bzlj.message.internaldistribution.repository.mongo.MessageExceptionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DefaultKafkaReconnectHandler implements KafkaReconnectHandler {

    private final MessageExceptionRepository messageExceptionRepository;

    /**
     * 加载因异常投递失败的数据到内存队列中
     */
    @Override
    public void onReconnect() {
//        List<Integer> types = Arrays.asList(
//                MessageExceptionEnum.SERVICE_EXCEPTION.getType(),
//                MessageExceptionEnum.ENQUEUE_EXCEPTION.getType(),
//                MessageExceptionEnum.SEND_EXCEPTION.getType());
//
//        List<MessageException> byTypeIn = messageExceptionRepository.findByTypeIn(types);
//        for (MessageException messageException : byTypeIn) {
//            QueueSendDTO queueSendDTO = new QueueSendDTO()
//                    .setTopic(messageException.getTopic())
//                    .setId(messageException.getTelegraphTextCode())
//                    .setPayload(messageException.getTelegraphTextBody());
//            QueueManagement.priorityQueue().offer(queueSendDTO);
//            messageExceptionRepository.delete(messageException);
//        }
    }

}
