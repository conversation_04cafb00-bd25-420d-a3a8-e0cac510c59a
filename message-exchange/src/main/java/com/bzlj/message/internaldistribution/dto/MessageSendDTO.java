package com.bzlj.message.internaldistribution.dto;

import com.bzlj.message.common.util.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 消息发送数据传输对象
 *
 * <AUTHOR>
 * @date 2025/5/16 16:52
 */
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageSendDTO {

    /**
     * 服务ID/电文号
     */
    private String id;

    /**
     * 消息体 - 支持字符串和对象类型
     */
    private Object payload;

    /**
     * 接收时间戳
     */
    private Long receiptTime;

    /**
     * 发送时间戳
     */
    private Long sendTime;

    /**
     * 二级分类
     */
    private String type;

    @Override
    public String toString() {
        // 使用 JsonUtils 来正确序列化整个对象
        return JsonUtils.toJson(this);
    }

}
