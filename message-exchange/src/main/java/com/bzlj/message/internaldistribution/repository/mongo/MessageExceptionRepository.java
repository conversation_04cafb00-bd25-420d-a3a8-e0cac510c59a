package com.bzlj.message.internaldistribution.repository.mongo;

import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface MessageExceptionRepository extends MongoRepository<MessageException, String>, MessageExceptionCustomRepository {

    List<MessageException> findByTypeIn(List<Integer> types);
}
