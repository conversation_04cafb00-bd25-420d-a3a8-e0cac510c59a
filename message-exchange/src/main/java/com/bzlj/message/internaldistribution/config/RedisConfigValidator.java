package com.bzlj.message.internaldistribution.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Redis 配置验证器
 * 在应用启动时验证 Redis 配置的正确性
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisConfigValidator {

    private final RedissonProperties redissonProperties;

    @EventListener(ApplicationReadyEvent.class)
    public void validateRedisConfig() {
        log.info("开始验证 Redis 配置...");
        
        try {
            String mode = redissonProperties.getMode().toLowerCase();
            boolean isValid = true;
            
            switch (mode) {
                case "single":
                    isValid = validateSingleServerConfig();
                    break;
                case "cluster":
                    isValid = validateClusterConfig();
                    break;
                case "sentinel":
                    isValid = validateSentinelConfig();
                    break;
                default:
                    log.error("❌ 无效的 Redis 部署模式: {}", mode);
                    log.error("💡 支持的模式: single（单机）、cluster（集群）、sentinel（哨兵）");
                    isValid = false;
                    break;
            }
            
            if (isValid) {
                log.info("✅ Redis 配置验证通过");
            } else {
                log.error("❌ Redis 配置验证失败，请检查配置文件");
            }
            
        } catch (Exception e) {
            log.error("❌ Redis 配置验证过程中发生异常", e);
        }
    }

    /**
     * 验证单机模式配置
     */
    private boolean validateSingleServerConfig() {
        try {
            RedissonProperties.SingleServerConfig config = redissonProperties.getSingleServerConfig();
            boolean isValid = true;
            
            log.info("验证单机模式配置...");
            
            // 验证地址
            if (config.getAddress() == null || config.getAddress().trim().isEmpty()) {
                log.error("❌ 单机模式地址不能为空");
                isValid = false;
            } else if (!config.getAddress().startsWith("redis://") && !config.getAddress().startsWith("rediss://")) {
                log.warn("⚠️ 建议使用 redis:// 或 rediss:// 协议前缀");
            }
            
            // 验证数据库索引
            if (config.getDatabase() < 0 || config.getDatabase() > 15) {
                log.warn("⚠️ 数据库索引通常在 0-15 范围内，当前值: {}", config.getDatabase());
            }
            
            // 验证连接池配置
            if (config.getConnectionPoolSize() <= 0) {
                log.error("❌ 连接池大小必须大于 0，当前值: {}", config.getConnectionPoolSize());
                isValid = false;
            }
            
            if (config.getConnectionMinimumIdleSize() < 0) {
                log.error("❌ 最小空闲连接数不能小于 0，当前值: {}", config.getConnectionMinimumIdleSize());
                isValid = false;
            }
            
            // 验证超时配置
            if (config.getConnectTimeout() <= 0) {
                log.error("❌ 连接超时时间必须大于 0，当前值: {}ms", config.getConnectTimeout());
                isValid = false;
            }
            
            if (config.getTimeout() <= 0) {
                log.error("❌ 命令超时时间必须大于 0，当前值: {}ms", config.getTimeout());
                isValid = false;
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验证单机模式配置时发生异常", e);
            return false;
        }
    }

    /**
     * 验证集群模式配置
     */
    private boolean validateClusterConfig() {
        try {
            RedissonProperties.ClusterConfig config = redissonProperties.getClusterConfig();
            boolean isValid = true;
            
            log.info("验证集群模式配置...");
            
            // 验证节点地址
            List<String> nodeAddresses = config.getNodeAddresses();
            if (nodeAddresses == null || nodeAddresses.isEmpty()) {
                log.error("❌ 集群节点地址列表不能为空");
                isValid = false;
            } else {
                if (nodeAddresses.size() < 3) {
                    log.warn("⚠️ Redis 集群建议至少配置 3 个节点，当前配置: {} 个", nodeAddresses.size());
                }
                
                // 验证每个节点地址格式
                for (String address : nodeAddresses) {
                    if (address == null || address.trim().isEmpty()) {
                        log.error("❌ 发现空的节点地址");
                        isValid = false;
                    } else if (!address.startsWith("redis://") && !address.startsWith("rediss://")) {
                        log.warn("⚠️ 节点地址建议使用 redis:// 或 rediss:// 协议前缀: {}", address);
                    }
                }
            }
            
            // 验证连接池配置
            if (config.getMasterConnectionPoolSize() <= 0) {
                log.error("❌ 主节点连接池大小必须大于 0，当前值: {}", config.getMasterConnectionPoolSize());
                isValid = false;
            }
            
            if (config.getSlaveConnectionPoolSize() <= 0) {
                log.error("❌ 从节点连接池大小必须大于 0，当前值: {}", config.getSlaveConnectionPoolSize());
                isValid = false;
            }
            
            // 验证读取模式
            String readMode = config.getReadMode();
            if (!"SLAVE".equalsIgnoreCase(readMode) && 
                !"MASTER".equalsIgnoreCase(readMode) && 
                !"MASTER_SLAVE".equalsIgnoreCase(readMode)) {
                log.error("❌ 无效的读取模式: {}，支持的模式: SLAVE、MASTER、MASTER_SLAVE", readMode);
                isValid = false;
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验证集群模式配置时发生异常", e);
            return false;
        }
    }

    /**
     * 验证哨兵模式配置
     */
    private boolean validateSentinelConfig() {
        try {
            RedissonProperties.SentinelConfig config = redissonProperties.getSentinelConfig();
            boolean isValid = true;
            
            log.info("验证哨兵模式配置...");
            
            // 验证主服务器名称
            if (config.getMasterName() == null || config.getMasterName().trim().isEmpty()) {
                log.error("❌ 哨兵模式主服务器名称不能为空");
                isValid = false;
            }
            
            // 验证哨兵地址
            List<String> sentinelAddresses = config.getSentinelAddresses();
            if (sentinelAddresses == null || sentinelAddresses.isEmpty()) {
                log.error("❌ 哨兵节点地址列表不能为空");
                isValid = false;
            } else {
                if (sentinelAddresses.size() < 3) {
                    log.warn("⚠️ Redis 哨兵建议至少配置 3 个节点，当前配置: {} 个", sentinelAddresses.size());
                }
                
                // 验证每个哨兵地址格式
                for (String address : sentinelAddresses) {
                    if (address == null || address.trim().isEmpty()) {
                        log.error("❌ 发现空的哨兵地址");
                        isValid = false;
                    } else if (!address.startsWith("redis://") && !address.startsWith("rediss://")) {
                        log.warn("⚠️ 哨兵地址建议使用 redis:// 或 rediss:// 协议前缀: {}", address);
                    }
                }
            }
            
            // 验证数据库索引
            if (config.getDatabase() < 0 || config.getDatabase() > 15) {
                log.warn("⚠️ 数据库索引通常在 0-15 范围内，当前值: {}", config.getDatabase());
            }
            
            // 验证连接池配置
            if (config.getMasterConnectionPoolSize() <= 0) {
                log.error("❌ 主节点连接池大小必须大于 0，当前值: {}", config.getMasterConnectionPoolSize());
                isValid = false;
            }
            
            if (config.getSlaveConnectionPoolSize() <= 0) {
                log.error("❌ 从节点连接池大小必须大于 0，当前值: {}", config.getSlaveConnectionPoolSize());
                isValid = false;
            }
            
            // 验证读取模式
            String readMode = config.getReadMode();
            if (!"SLAVE".equalsIgnoreCase(readMode) && 
                !"MASTER".equalsIgnoreCase(readMode) && 
                !"MASTER_SLAVE".equalsIgnoreCase(readMode)) {
                log.error("❌ 无效的读取模式: {}，支持的模式: SLAVE、MASTER、MASTER_SLAVE", readMode);
                isValid = false;
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验证哨兵模式配置时发生异常", e);
            return false;
        }
    }
}
