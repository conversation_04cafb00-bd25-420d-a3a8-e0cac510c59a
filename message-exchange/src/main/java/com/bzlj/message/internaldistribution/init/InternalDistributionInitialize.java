package com.bzlj.message.internaldistribution.init;

import com.bzlj.message.internaldistribution.breaker.CircuitBreaker;
import com.bzlj.message.internaldistribution.config.InternalDistributionConfig;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaAdmin;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/30 15:16
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Configuration
@RequiredArgsConstructor
public class InternalDistributionInitialize {

    @Value("${spring.cloud.stream.kafka.binder.brokers}")
    private String bootstrapServers;


    @Bean
    public QueueManagement getQueueManagement(InternalDistributionConfig config) {
        return new QueueManagement(config);
    }

    @Bean
    public RedisUtils getRedisUtils(RedissonClient redissonClient) {
        return new RedisUtils(redissonClient);
    }

    @Bean
    public KafkaAdmin kafkaAdmin() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configs.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, 3000);
        return new KafkaAdmin(configs);
    }

    @Bean
    public CircuitBreaker getCircuitBreaker() {
        return new CircuitBreaker();
    }
}
