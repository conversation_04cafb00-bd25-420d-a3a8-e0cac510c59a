package com.bzlj.message.internaldistribution.controller;

import com.bzlj.message.internaldistribution.monitor.KafkaHealthMonitor;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/kafka")
@RequiredArgsConstructor
public class KafkaStatusController {

    private final KafkaHealthMonitor kafkaHealthMonitor;

    /**
     * 获取kafka状态信息
     *
     * @return
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getKafkaStatus() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", kafkaHealthMonitor.getCurrentStatus().getName());
        response.put("description", kafkaHealthMonitor.getCurrentStatus().getDescription());
        response.put("healthy", kafkaHealthMonitor.isHealthy());
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
}