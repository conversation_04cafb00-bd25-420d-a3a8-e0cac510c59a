package com.bzlj.message.internaldistribution.breaker;

import com.bzlj.message.common.util.VirtualThreadUtil;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

public class CircuitBreaker {
    private final AtomicBoolean open = new AtomicBoolean(false);
    private final ReentrantLock lock = new ReentrantLock();
    private final AtomicInteger failureCount = new AtomicInteger(0);

    public boolean isOpen() {
        return open.get();
    }

    public void trip() {
        int failureThreshold = 5;
        if (failureCount.incrementAndGet() >= failureThreshold) {
            lock.lock();
            try {
                if (!open.get()) {
                    open.set(true);
                    resetAfter(Duration.ofMinutes(1));
                }
            } finally {
                lock.unlock();
            }
        }
    }

    public void tripNow() {
        lock.lock();
        try {
            if (!open.get()) {
                open.set(true);
            }
        } finally {
            lock.unlock();
        }
    }

    public void reset() {
        lock.lock();
        try {
            open.set(false);
            failureCount.set(0);
        } finally {
            lock.unlock();
        }
    }

    private void resetAfter(Duration duration) {
        VirtualThreadUtil.execute(() -> {
            try {
                Thread.sleep(duration);
                reset();
            } catch (InterruptedException ignored) {
            }
        });
    }
}
