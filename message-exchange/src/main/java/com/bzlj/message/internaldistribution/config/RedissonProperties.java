package com.bzlj.message.internaldistribution.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Redisson 配置属性类
 * 支持单机、集群、哨兵三种部署模式
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
@Component
@ConfigurationProperties(prefix = "redisson")
public class RedissonProperties {

    /**
     * Redis 部署模式：single（单机）、cluster（集群）、sentinel（哨兵）
     */
    private String mode = "single";

    /**
     * 单机模式配置
     */
    private SingleServerConfig singleServerConfig = new SingleServerConfig();

    /**
     * 集群模式配置
     */
    private ClusterConfig clusterConfig = new ClusterConfig();

    /**
     * 哨兵模式配置
     */
    private SentinelConfig sentinelConfig = new SentinelConfig();

    /**
     * 单机模式配置类
     */
    @Data
    public static class SingleServerConfig {
        /**
         * Redis 服务器地址
         */
        private String address = "redis://localhost:6379";

        /**
         * Redis 密码
         */
        private String password = "";

        /**
         * Redis 数据库索引
         */
        private int database = 0;

        /**
         * 连接池大小
         */
        private int connectionPoolSize = 64;

        /**
         * 最小空闲连接数
         */
        private int connectionMinimumIdleSize = 10;

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 3000;

        /**
         * 命令执行超时时间（毫秒）
         */
        private int timeout = 3000;

        /**
         * 重试次数
         */
        private int retryAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private int retryInterval = 1500;

        /**
         * 空闲连接超时时间（毫秒）
         */
        private int idleConnectionTimeout = 10000;

        /**
         * ping 连接间隔（毫秒）
         */
        private int pingConnectionInterval = 30000;

        /**
         * 是否保持连接
         */
        private boolean keepAlive = true;
    }

    /**
     * 集群模式配置类
     */
    @Data
    public static class ClusterConfig {
        /**
         * 集群节点地址列表
         */
        private List<String> nodeAddresses = List.of("redis://localhost:7000", "redis://localhost:7001", "redis://localhost:7002");

        /**
         * Redis 密码
         */
        private String password = "";

        /**
         * 主节点连接池大小
         */
        private int masterConnectionPoolSize = 64;

        /**
         * 从节点连接池大小
         */
        private int slaveConnectionPoolSize = 64;

        /**
         * 主节点最小空闲连接数
         */
        private int masterConnectionMinimumIdleSize = 10;

        /**
         * 从节点最小空闲连接数
         */
        private int slaveConnectionMinimumIdleSize = 10;

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 3000;

        /**
         * 命令执行超时时间（毫秒）
         */
        private int timeout = 3000;

        /**
         * 重试次数
         */
        private int retryAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private int retryInterval = 1500;

        /**
         * 空闲连接超时时间（毫秒）
         */
        private int idleConnectionTimeout = 10000;

        /**
         * ping 连接间隔（毫秒）
         */
        private int pingConnectionInterval = 30000;

        /**
         * 是否保持连接
         */
        private boolean keepAlive = true;

        /**
         * 扫描间隔时间（毫秒）
         */
        private int scanInterval = 1000;

        /**
         * 读取模式：SLAVE（从节点）、MASTER（主节点）、MASTER_SLAVE（主从节点）
         */
        private String readMode = "SLAVE";

        /**
         * 订阅模式：SLAVE（从节点）、MASTER（主节点）
         */
        private String subscriptionMode = "MASTER";
    }

    /**
     * 哨兵模式配置类
     */
    @Data
    public static class SentinelConfig {
        /**
         * 主服务器名称
         */
        private String masterName = "mymaster";

        /**
         * 哨兵节点地址列表
         */
        private List<String> sentinelAddresses = List.of("redis://localhost:26379", "redis://localhost:26380", "redis://localhost:26381");

        /**
         * Redis 密码
         */
        private String password = "";

        /**
         * 哨兵密码
         */
        private String sentinelPassword = "";

        /**
         * Redis 数据库索引
         */
        private int database = 0;

        /**
         * 主节点连接池大小
         */
        private int masterConnectionPoolSize = 64;

        /**
         * 从节点连接池大小
         */
        private int slaveConnectionPoolSize = 64;

        /**
         * 主节点最小空闲连接数
         */
        private int masterConnectionMinimumIdleSize = 10;

        /**
         * 从节点最小空闲连接数
         */
        private int slaveConnectionMinimumIdleSize = 10;

        /**
         * 连接超时时间（毫秒）
         */
        private int connectTimeout = 3000;

        /**
         * 命令执行超时时间（毫秒）
         */
        private int timeout = 3000;

        /**
         * 重试次数
         */
        private int retryAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private int retryInterval = 1500;

        /**
         * 空闲连接超时时间（毫秒）
         */
        private int idleConnectionTimeout = 10000;

        /**
         * ping 连接间隔（毫秒）
         */
        private int pingConnectionInterval = 30000;

        /**
         * 是否保持连接
         */
        private boolean keepAlive = true;

        /**
         * 读取模式：SLAVE（从节点）、MASTER（主节点）、MASTER_SLAVE（主从节点）
         */
        private String readMode = "SLAVE";

        /**
         * 订阅模式：SLAVE（从节点）、MASTER（主节点）
         */
        private String subscriptionMode = "MASTER";
    }
}
