package com.bzlj.message.internaldistribution.queue;

import com.bzlj.message.internaldistribution.config.InternalDistributionConfig;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ArrayBlockingQueue;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2025/5/30 15:06
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class QueueManagement {
    private static ArrayBlockingQueue<Object> MAIN_QUEUE;
    private static ArrayBlockingQueue<Object> PRIORITY_QUEUE;

    @Autowired
    public QueueManagement(InternalDistributionConfig config) {
        MAIN_QUEUE = new ArrayBlockingQueue<>(config.getSize());
        PRIORITY_QUEUE = new ArrayBlockingQueue<>(config.getSize());
    }

    public static ArrayBlockingQueue<Object> mainQueue() {
        return MAIN_QUEUE;
    }

    public static ArrayBlockingQueue<Object> priorityQueue() {
        return PRIORITY_QUEUE;
    }
}
