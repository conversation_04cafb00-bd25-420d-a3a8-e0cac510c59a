package com.bzlj.message.internaldistribution.monitor;

import com.bzlj.message.common.util.VirtualThreadUtil;
import com.bzlj.message.internaldistribution.breaker.CircuitBreaker;
import com.bzlj.message.internaldistribution.config.InternalDistributionConfig;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import com.bzlj.message.internaldistribution.enums.KafkaStatus;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.repository.mongo.MessageExceptionRepository;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * kafka心跳监测
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KafkaHealthMonitor {
    private final KafkaAdmin kafkaAdmin;
    private final CircuitBreaker circuitBreaker;
    private final InternalDistributionConfig internalDistributionConfig;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final long CHECK_INTERVAL = 30_000; // 30秒
    private final MessageExceptionRepository messageRepository;

    private final AtomicBoolean running = new AtomicBoolean(false);

    //    private final KafkaTemplate<String, String> kafkaTemplate;
    private final AtomicReference<KafkaStatus> status = new AtomicReference<>(KafkaStatus.DISCONNECTED);
//    private final AdminClient adminClient;
//    private final long timeoutMs = 3000;
//
//    private final KafkaReconnectHandler reconnectHandler;

    private final List<Integer> types = Arrays.asList(
            MessageExceptionEnum.SERVICE_EXCEPTION.getType(),
            MessageExceptionEnum.ENQUEUE_EXCEPTION.getType(),
            MessageExceptionEnum.SEND_EXCEPTION.getType());


    public void startHealthCheck() {
        scheduler.scheduleWithFixedDelay(this::performHealthCheck, 0, CHECK_INTERVAL, TimeUnit.MILLISECONDS);
    }

    private void performHealthCheck() {
        try {
            if (checkKafkaAvailability() && circuitBreaker.isOpen()) {
                circuitBreaker.reset();
                startCompensationProcess();
            }
        } catch (Exception e) {
            log.error("Health check failed", e);
        }
    }

    private boolean checkKafkaAvailability() {
        try (AdminClient client = AdminClient.create(kafkaAdmin.getConfigurationProperties())) {
            boolean flag = !client.listTopics().names().get(10, TimeUnit.SECONDS).isEmpty();
            if (flag) {
                status.set(KafkaStatus.HEALTHY);
            } else {
                status.set(KafkaStatus.DISCONNECTED);
            }
            return flag;
        } catch (Exception e) {
            status.set(KafkaStatus.DISCONNECTED);
            return false;
        }
    }

    private void startCompensationProcess() {
        VirtualThreadUtil.execute(this::processPendingMessages);
    }


    private void processPendingMessages() {
        if (!running.compareAndSet(false, true)) {
            return; // 已经有其他线程在处理
        }

        try {
            while (running.get()) {
            // 查询异常消息总数
            long count = messageRepository.countByTypeInAndRetryCountLessThan(types, 3);
            if (count > 0 && !circuitBreaker.isOpen()) {
                circuitBreaker.tripNow();
            }
            if (count == 0 && circuitBreaker.isOpen()) {
                circuitBreaker.reset();
            }
            if (count == 0) {
                break;
            }
            int size = (int) Math.min(
                    Math.max(internalDistributionConfig.getSize() - QueueManagement.mainQueue().size(), 0),
                    count
            );
            // 获取需要重试的消息
            List<MessageException> retryMessageList = messageRepository.findByTypeInAndRetryCountLessThanOrderByReceiveTimeAscLimit(types, 3, size);
            for (MessageException messageException : retryMessageList) {
                QueueSendDTO queueSendDTO = new QueueSendDTO()
                        .setTopic(messageException.getTopic())
                        .setId(messageException.getTelegraphTextCode())
                        .setPayload(messageException.getTelegraphTextBody()).setReceiveTime(messageException.getReceiveTime()).setRetryCount(messageException.getRetryCount() + 1);
                QueueManagement.mainQueue().offer(queueSendDTO);
                messageRepository.delete(messageException);
            }
            }
        } finally {
            running.set(false);
        }
    }

    @PostConstruct
    public void init() {
        //初始化时加载因异常投递失败的数据到内存队列中
        startCompensationProcess();
        startHealthCheck();
    }

    @PreDestroy
    public void destroy() {
        if (!scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(30, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
//
//    @Async
//    @Scheduled(fixedRate = 30000) // 每30秒检查一次
//    public void checkKafkaHealth() {
//        try {
//            DescribeClusterResult describeCluster = adminClient.describeCluster();
//            // 获取控制器节点信息
//            describeCluster.controller().get(timeoutMs, TimeUnit.MILLISECONDS);
//            // 获取集群节点信息
//            Collection<Node> nodes = describeCluster.nodes().get(timeoutMs, TimeUnit.MILLISECONDS);
//
//            if (nodes != null && !nodes.isEmpty()) {
//                if (status.get() == KafkaStatus.DISCONNECTED || status.get() == KafkaStatus.RECONNECTING) {
//                    // 从断开状态恢复
//                    reconnectHandler.onReconnect();
//                }
//                status.set(KafkaStatus.HEALTHY);
//            } else {
//                handleDisconnection();
//            }
//        } catch (Exception e) {
//            handleDisconnection();
//        }
//    }
//
//    public void handleDisconnection() {
//        if (status.get() != KafkaStatus.DISCONNECTED) {
//            status.set(KafkaStatus.DISCONNECTED);
//        }
//
//        // 尝试重新连接
//        status.set(KafkaStatus.RECONNECTING);
//        try {
//            if (adminClient != null) {
//                adminClient.close();
//            }
//            initializeAdminClient();
//        } catch (Exception e) {
//            // 重连失败，保持断开状态
//            status.set(KafkaStatus.DISCONNECTED);
//        }
//    }
//
    public KafkaStatus getCurrentStatus() {
        return status.get();
    }

    public boolean isHealthy() {
        return status.get() == KafkaStatus.HEALTHY;
    }
}