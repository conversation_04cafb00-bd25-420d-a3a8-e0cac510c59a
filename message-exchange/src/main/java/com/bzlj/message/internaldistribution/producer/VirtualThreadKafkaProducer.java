package com.bzlj.message.internaldistribution.producer;

import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.common.util.VirtualThreadUtil;
import com.bzlj.message.internaldistribution.breaker.CircuitBreaker;
import com.bzlj.message.internaldistribution.dto.MessageSendDTO;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.handlers.MessageVerifyHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.RetriableException;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.kafka.KafkaException;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.StructuredTaskScope;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
@RequiredArgsConstructor
public class VirtualThreadKafkaProducer {
    private final StreamBridge streamBridge;
    private final CircuitBreaker circuitBreaker;
    private final MessageVerifyHandler messageVerifyHandler;

    private static final int MAX_RETRIES = 3;
    private static final Duration[] BACKOFF = {
            Duration.ofSeconds(5),
            Duration.ofSeconds(15),
            Duration.ofSeconds(30)
    };

    /**
     * 发送消息
     *
     * @param queueSend 消息
     * <AUTHOR>
     * @date 2025/6/11 16:58
     */
    public void sendMessage(QueueSendDTO queueSend) {
        VirtualThreadUtil.execute(() -> send(queueSend));
    }

    public void send(QueueSendDTO queueSend) {
        if (circuitBreaker.isOpen()) {
            storeMessage(queueSend, "数据库中存在积压的消息,直接落库");
            return;
        }
        try (StructuredTaskScope.ShutdownOnFailure scope = new StructuredTaskScope.ShutdownOnFailure()) {
            scope.fork(() -> {
                MessageSendDTO message = JsonUtils.fromJson(queueSend.getPayload(), MessageSendDTO.class);
                return attemptSend(queueSend.getTopic(), JsonUtils.toJson(message));
            });
            scope.join().throwIfFailed();
        } catch (Exception e) {
            handleFailure(queueSend, e);
        }
    }

    private void handleFailure(QueueSendDTO queueSend, Exception e) {
        log.error("Message delivery failed: {}", e.getMessage());
        storeMessage(queueSend, e.getMessage());
        circuitBreaker.trip();
    }

    /**
     * 保存消息
     *
     * @param queueSend      消息
     * @param exceptionsInfo 异常信息
     * <AUTHOR>
     * @date 2025/6/11 16:50
     */
    private void storeMessage(QueueSendDTO queueSend,
                              String exceptionsInfo) {
        VirtualThreadUtil.execute(() -> {
                    messageVerifyHandler.insertMessageException(queueSend,
                            exceptionsInfo,
                            MessageExceptionEnum.SEND_EXCEPTION.getType());
                }
        );
    }

    /**
     * 尝试发送消息
     *
     * @param topic   主题
     * @param payload 内容
     * <AUTHOR>
     * @date 2025/6/11 17:02
     */
    private Void attemptSend(String topic, String payload) throws Exception {
        AtomicInteger attempt = new AtomicInteger(0);
        while (attempt.get() <= MAX_RETRIES) {
            try {
                streamBridge.send(topic, payload);
                return null;
            } catch (Exception e) {
                if (e.getCause() instanceof RetriableException) {
                    Thread.sleep(BACKOFF[attempt.getAndIncrement()]);
                } else {
                    throw e;
                }
            }
        }
        throw new KafkaException("Max retries exceeded");
    }
}