package com.bzlj.message.externaldispatch.service.impl;

import com.bzlj.message.common.util.EPlatTokenUtil;
import com.bzlj.message.externaldispatch.enums.MessageType;
import com.bzlj.message.externaldispatch.service.IAlertMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-05-13 9:35
 */
@Service
@Slf4j
public class AlertMessageServiceImpl implements IAlertMessageService {

    @Value("${eplattest.url:http://eplattest.baocloud.cn/service}")
    private String EPLATTEST_URL;

    @Value("${eplattest.todo_message:S_BE_MS_01}")
    private String EPLATTEST_TODO_MESSAGE;

    @Value("${eplattest.push_message:S_BE_MS_02}")
    private String EPLATTEST_PUSH_MESSAGE;

    @Value("${eplattest.query_message:S_BE_MS_07}")
    private String EPLATTEST_QUERY_MESSAGE;

    @Value("${eplattest.emil_message:S_EPLAT_22}")
    private String EPLATTEST_EMIL_MESSAGE;

    @Value("${eplattest.text__message:S_BE_MS_13}")
    private String EPLATTEST_TEXT_MESSAGE;

    @Override
    public void alertMessageSending(String data, MessageType messageType) {
        String ePlatToken = EPlatTokenUtil.getEPlatToken();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) { // 使用 try-with-resources 管理资源
            HttpPost httpPost = switch (messageType) {
                case TODO -> new HttpPost(String.format("%s/%s", EPLATTEST_URL, EPLATTEST_TODO_MESSAGE));
                case NOTICE -> new HttpPost(String.format("%s/%s", EPLATTEST_URL, EPLATTEST_PUSH_MESSAGE));
            };
            if(StringUtils.isEmpty(data)){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("messageTitle", "GYCK_信使测试_消息预警");
                jsonObject.put("messageContent", "xxx工步发生异常");
                List<Map<String,String>> userIdList = new ArrayList<>();
                userIdList.add(Map.of("userId","YB2174"));
                jsonObject.put("userIdList", userIdList);
                jsonObject.put("pcUrl", "https://www.baidu.com");
                jsonObject.put("appUrl", "https://www.baidu.com");
                jsonObject.put("isSend", "1");
                jsonObject.put("sendMode", "5");
                data = jsonObject.toString();
            }

            StringEntity requestEntity = new StringEntity(data, "utf-8");
            requestEntity.setContentEncoding("UTF-8");

            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Xplat-Token", ePlatToken);
            httpPost.setEntity(requestEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) { // 使用 try-with-resources 管理响应
                String returnValue = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.error("===============返回值：{}", returnValue);
            } catch (IOException e) {
                throw new RuntimeException("Failed to send request to " + EPLATTEST_URL, e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize HTTP client", e);
        }
    }

    @Override
    public List<Object> queryAlertMessage() {
        String ePlatToken = EPlatTokenUtil.getEPlatToken();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) { // 使用 try-with-resources 管理资源
            HttpPost httpPost = new HttpPost(String.format("%s/%s", EPLATTEST_URL, EPLATTEST_QUERY_MESSAGE));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("loginName", "YB2175");
            StringEntity requestEntity = new StringEntity(jsonObject.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");

            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Xplat-Token", ePlatToken);
            httpPost.setEntity(requestEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) { // 使用 try-with-resources 管理响应
                String returnValue = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.error("===============返回值：{}", returnValue);
            } catch (IOException e) {
                throw new RuntimeException("Failed to send request to " + EPLATTEST_URL, e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize HTTP client", e);
        }
        return List.of();
    }

    @Override
    public void emilAlertMessageSending(Map<String, Object> data) {
        String ePlatToken = EPlatTokenUtil.getEPlatToken();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) { // 使用 try-with-resources 管理资源
            HttpPost httpPost = new HttpPost(String.format("%s/%s", EPLATTEST_URL, EPLATTEST_EMIL_MESSAGE));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("subject", "GYCK_信使测试_邮件消息");
            jsonObject.put("message", "xxx工步发生异常");
            List<String> userList = List.of("YB2175");
            jsonObject.put("userList", userList);
            List<String> copyEmailList = List.of("<EMAIL>");
            jsonObject.put("copyEmailList", copyEmailList);
            jsonObject.put("projectEname", "GYCK");

            StringEntity requestEntity = new StringEntity(jsonObject.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");

            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Xplat-Token", ePlatToken);
            httpPost.setEntity(requestEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) { // 使用 try-with-resources 管理响应
                String returnValue = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.error("===============返回值：{}", returnValue);
            } catch (IOException e) {
                throw new RuntimeException("Failed to send request to " + EPLATTEST_URL, e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize HTTP client", e);
        }
    }

    @Override
    public void textAlertMessageSending(Map<String, Object> data) {
        String ePlatToken = EPlatTokenUtil.getEPlatToken();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) { // 使用 try-with-resources 管理资源
            HttpPost httpPost = new HttpPost(String.format("%s/%s", EPLATTEST_URL, EPLATTEST_TEXT_MESSAGE));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("LoginName", "YB2175");
            jsonObject.put("content", "GYCK_信使测试_短信消息：xxx工步发生异常");
            StringEntity requestEntity = new StringEntity(jsonObject.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");

            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Xplat-Token", ePlatToken);
            httpPost.setEntity(requestEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) { // 使用 try-with-resources 管理响应
                String returnValue = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.error("===============返回值：{}", returnValue);
            } catch (IOException e) {
                throw new RuntimeException("Failed to send request to " + EPLATTEST_URL, e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize HTTP client", e);
        }
    }
}
