package com.bzlj.message.externaldispatch.service;

import com.bzlj.message.externaldispatch.enums.MessageType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-05-13 9:34
 */
public interface IAlertMessageService {
    /**
     * 信使消息推送
     * @param data
     */
    void alertMessageSending(String data, MessageType messageType);

    /**
     * 查询信使消息
     * @return
     */
    List<Object> queryAlertMessage();

    /**
     * 邮件消息推送
     * @param data
     */
    void emilAlertMessageSending(Map<String, Object> data);

    /**
     * 短信消息推送
     * @param data
     */
    void textAlertMessageSending(Map<String, Object> data);
}
