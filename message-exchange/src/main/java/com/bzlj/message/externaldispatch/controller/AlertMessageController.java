package com.bzlj.message.externaldispatch.controller;

import com.bzlj.message.externaldispatch.enums.MessageType;
import com.bzlj.message.externaldispatch.service.IAlertMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 预警消息推送
 * <AUTHOR>
 * @description:
 * @date 2025-05-13 9:08
 */
@RestController
@RequestMapping("/alertMessage")
@RequiredArgsConstructor
@Slf4j
public class AlertMessageController {

    @Autowired
    private IAlertMessageService alertMessageService;
    /**
     * 信使消息推送
     * @param data
     */
    @RequestMapping(value = "/alertMessageSending", method = RequestMethod.POST, consumes = "application/json", produces
            = "text/json;charset=UTF-8")
    public void alertMessageSending(@RequestBody(required = false) String data, @RequestParam(value = "messageType")MessageType messageType) {
        alertMessageService.alertMessageSending(data,messageType);
    }

    @PostMapping(value = "/queryAlertMessage")
    public void queryAlertMessage() {
        alertMessageService.queryAlertMessage();
    }

    @RequestMapping(value = "/emilAlertMessageSending", method = RequestMethod.POST, consumes = "application/json", produces
            = "text/json;charset=UTF-8")
    public void emilAlertMessageSending(@RequestBody Map<String,Object> data) {
        alertMessageService.emilAlertMessageSending(data);
    }

    @RequestMapping(value = "/textAlertMessageSending", method = RequestMethod.POST, consumes = "application/json", produces
            = "text/json;charset=UTF-8")
    public void textAlertMessageSending(@RequestBody Map<String,Object> data) {
        alertMessageService.textAlertMessageSending(data);
    }
}
