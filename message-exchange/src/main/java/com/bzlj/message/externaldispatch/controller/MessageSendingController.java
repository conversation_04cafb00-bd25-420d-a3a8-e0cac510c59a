package com.bzlj.message.externaldispatch.controller;

import com.bzlj.message.externaldispatch.service.IMessageSendingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 消息推送
 * <AUTHOR>
 * @description:
 * @date 2025-04-18 17:38
 */
@RestController
@RequestMapping("/send")
@RequiredArgsConstructor
@Slf4j
public class MessageSendingController {

    @Autowired
    private IMessageSendingService messageSendingService;

    /**
     * 普通消息推送
     * @param data
     */
    @RequestMapping(value = "/ordinarySending", method = RequestMethod.POST, consumes = "application/json", produces
            = "text/json;charset=UTF-8")
    public void ordinarySending(@RequestBody Map<String,Object> data) {
        messageSendingService.ordinarySending(data);
    }

    /**
     * 电文消息推送
     * @param data
     */
    @RequestMapping(value = "/telegramSending", method = RequestMethod.POST, consumes = "application/json", produces
            = "text/json;charset=UTF-8")
    public void telegramSending(@RequestBody Map<String,Object> data) {
        messageSendingService.telegramSending(data);
    }

}
