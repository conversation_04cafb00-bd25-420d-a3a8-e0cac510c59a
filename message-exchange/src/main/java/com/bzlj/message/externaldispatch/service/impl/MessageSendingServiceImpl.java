package com.bzlj.message.externaldispatch.service.impl;

import com.bzlj.message.common.util.EPlatTokenUtil;
import com.bzlj.message.externaldispatch.service.IMessageSendingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-04-18 16:56
 */
@Service
@Slf4j
public class MessageSendingServiceImpl implements IMessageSendingService {
    @Value("${eplat.ordinary.url:http://eplatxt.baocloud.cn/service/S_EPLAT_04}")
    private String EPLAT_ORDINARY_URL;

    @Value("${eplat.telegram.url:http://eplatxt.baocloud.cn/service/S_EPLAT_03}")
    private String EPLAT_TELEGRAM_URL;

    @Value("${message.ordinary.key:R_GYCK_01}")
    private String ORDINARY_MESSAGE_KEY;

    @Value("${IXBus.serviceId:GYCK_TEST_02}")
    private String XBUS_SERVICE_ID;

    @Override
    public void ordinarySending(Map<String, Object> data) {
        String ePlatToken = EPlatTokenUtil.getEPlatToken();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) { // 使用 try-with-resources 管理资源
            HttpPost httpPost = new HttpPost(EPLAT_ORDINARY_URL);
            JSONObject jsonObject = new JSONObject();
            //todo 非V6框架应用收发消息电文最佳实践中为 messageKey，接口文档中为 routingKey
            jsonObject.put("messageKey", ORDINARY_MESSAGE_KEY);
            jsonObject.put("messageBody", data);//生产者发送的消息内容

            StringEntity requestEntity = new StringEntity(jsonObject.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");

            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Xplat-Token", ePlatToken);
            httpPost.setEntity(requestEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) { // 使用 try-with-resources 管理响应
                String returnValue = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.error("===============返回值：{}", returnValue);
            } catch (IOException e) {
                throw new RuntimeException("Failed to send request to " + EPLAT_ORDINARY_URL, e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize HTTP client", e);
        }
    }

    @Override
    public void telegramSending(Map<String, Object> data) {
        String ePlatToken = EPlatTokenUtil.getEPlatToken();
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) { // 使用 try-with-resources 管理资源
            HttpPost httpPost = new HttpPost(EPLAT_TELEGRAM_URL);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("xbusServiceId", XBUS_SERVICE_ID);
            jsonObject.put("messageBody", data);//生产者发送的消息内容

            StringEntity requestEntity = new StringEntity(jsonObject.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");

            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Xplat-Token", ePlatToken);
            httpPost.setEntity(requestEntity);

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) { // 使用 try-with-resources 管理响应
                String returnValue = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.error("===============返回值：{}", returnValue);
            } catch (IOException e) {
                throw new RuntimeException("Failed to send request to " + EPLAT_ORDINARY_URL, e);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to initialize HTTP client", e);
        }
    }


}
