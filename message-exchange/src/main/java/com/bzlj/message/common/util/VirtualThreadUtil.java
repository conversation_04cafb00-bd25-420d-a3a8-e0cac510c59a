package com.bzlj.message.common.util;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 虚拟线程工具类
 *
 * <AUTHOR>
 * @date 2025/6/4 15:15
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class VirtualThreadUtil {
    /**
     * 自定义线程池
     */
    private static final ExecutorService VIRTUAL_POOL = Executors.newVirtualThreadPerTaskExecutor();

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                // 使用awaitTermination等待任务完成
                if (!VIRTUAL_POOL.awaitTermination(5, TimeUnit.SECONDS)) {
                    VIRTUAL_POOL.shutdownNow();
                }
            } catch (InterruptedException e) {
                VIRTUAL_POOL.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }));
    }

    /**
     * 执行无返回值的任务
     */
    public static void execute(Runnable task) {
        VIRTUAL_POOL.execute(task);
    }

    /**
     * 提交有返回值的任务
     *
     * @param task 带返回值的任务
     * @param <T>  返回值类型
     * @return Future对象
     */
    public static <T> Future<T> submit(Callable<T> task) {
        return VIRTUAL_POOL.submit(task);
    }

    /**
     * 执行带返回值的任务并阻塞获取结果
     *
     * @param supplier 提供结果的函数
     *                 <p>
     *                 /**
     *                 执行带返回值的任务并设置超时时间
     * @param supplier 提供结果的函数
     * @param timeout  超时时间
     * @param unit     时间单位
     * @param <T>      返回值类型
     * @return 任务执行结果
     * @throws ExecutionException   执行异常
     * @throws InterruptedException 中断异常
     * @throws TimeoutException     超时异常
     */
    public static <T> T get(Supplier<T> supplier, long timeout, TimeUnit unit)
            throws ExecutionException, InterruptedException, TimeoutException {
        return submit(supplier::get).get(timeout, unit);
    }

    /**
     * 批量提交任务
     *
     * @param tasks 任务集合
     * @param <T>   返回值类型
     * @return Future列表
     */
    public static <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks)
            throws InterruptedException {
        return VIRTUAL_POOL.invokeAll(tasks);
    }

    /**
     * 执行任意一个任务
     *
     * @param tasks 任务集合
     * @param <T>   返回值类型
     * @return 第一个完成的任务结果
     * @throws InterruptedException 中断异常
     * @throws ExecutionException   执行异常
     */
    public static <T> T invokeAny(Collection<? extends Callable<T>> tasks)
            throws InterruptedException, ExecutionException {
        return VIRTUAL_POOL.invokeAny(tasks);
    }

    /**
     * @param <T> 返回值类型
     * @return 任务执行结果
     * @throws ExecutionException   执行异常
     * @throws InterruptedException 中断异常
     */
    public static <T> T get(Supplier<T> supplier) throws ExecutionException, InterruptedException {
        return submit(supplier::get).get();
    }
}
