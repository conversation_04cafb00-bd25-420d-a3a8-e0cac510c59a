package com.bzlj.message.common.util;

import com.bzlj.message.common.exception.JsonException;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * JSON工具类，提供带缓存的JSON解析功能
 *  通过缓存解析结果避免重复解析相同JSON字符串，提高性能
 *
 * <AUTHOR>
 * @date 2025/3/21 10:59
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Slf4j
public class JsonUtils {
    /** Jackson JSON映射器实例 */
    private static final JsonMapper JSON_MAPPER = newJsonMapper();
    
    /**
     * JSON解析结果缓存
     * - 最大容量：1000条
     * - 写入后10分钟过期
     * - 并发级别：8
     */
    private static final Cache<String, Object> JSON_CACHE = CacheBuilder.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(10, TimeUnit.MINUTES)
        .concurrencyLevel(8)
        .build();

    /**
     * 缓存阈值：超过此大小的JSON不会被缓存（默认10KB）
     * 可通过 setCacheThreshold() 方法动态调整
     */
    private static volatile int CACHE_THRESHOLD = 10240;

    // 禁用构造函数
    private JsonUtils() {
        throw new AssertionError("工具类不可实例化");
    }

    /**
     * 构建自定义配置的 ObjectMapper
     */
    private static JsonMapper newJsonMapper() {
        return JsonMapper.builder()
                // 禁用自动检测 getter 方法（提升性能）
//                .disable(MapperFeature.AUTO_DETECT_GETTERS)
                // 忽略JSON中的未知属性（防止反序列化失败）
                 .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                // 启用美化输出（开发环境有用，生产环境按需关闭）
                .enable(SerializationFeature.INDENT_OUTPUT)
                // 枚举序列化使用 toString() 而不是 name()
                .enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING)
                // 支持 Java 8 时间类型（如 LocalDateTime）
                .addModule(new JavaTimeModule())
                .build();

    }

    /**
     * 对象转 JSON 字符串
     */
    @Nullable
    public static String toJson(@Nullable Object obj) {
        return toJson(obj, false);
    }

    /**
     * 对象转 JSON 字符串（美化输出）
     */
    @Nullable
    public static String toPrettyJson(@Nullable Object obj) {
        return toJson(obj, true);
    }

    private static String toJson(@Nullable Object obj, boolean pretty) {
        if (obj == null) return null;
        try {
            return pretty ?
                    JSON_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj) :
                    JSON_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonException("对象序列化失败", e);
        }
    }
    /**
     * JSON字符串转对象（支持泛型，智能缓存）
     * @param json JSON字符串（非空且非空白）
     * @param typeRef 目标类型引用
     * @return 解析后的对象
     * @throws JsonException 当JSON格式错误或解析失败时抛出
     * @throws IllegalArgumentException 当输入参数无效时抛出
     */
    @NonNull
    public static <T> T fromJson(@NonNull String json, @NonNull TypeReference<T> typeRef) {
        if (json == null || json.isBlank()) {
            throw new IllegalArgumentException("JSON字符串不能为空");
        }

        // 优化：只对小于阈值的JSON使用缓存，避免缓存大对象
        if (json.length() > CACHE_THRESHOLD) {
            return parseDirectly(json, typeRef);
        }

        // 简化缓存键：只使用类型名和JSON哈希值
        String cacheKey = typeRef.getType().getTypeName() + ":" + json.hashCode();

        try {
            return (T) JSON_CACHE.get(cacheKey, () -> parseDirectly(json, typeRef));
        } catch (Exception e) {
            // 简化异常处理
            Throwable cause = e.getCause();
            if (cause instanceof JsonException) {
                throw (JsonException) cause;
            }
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            }
            throw new JsonException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 直接解析JSON（不使用缓存）- TypeReference版本
     */
    private static <T> T parseDirectly(@NonNull String json, @NonNull TypeReference<T> typeRef) {
        try {
            return JSON_MAPPER.readValue(json, typeRef);
        } catch (JacksonException e) {
            String preview = json.length() > 100 ? json.substring(0, 100) + "..." : json;
            throw new JsonException(String.format("JSON解析失败: %s\n预览: %s", e.getMessage(), preview), e);
        }
    }

    /**
     * 直接解析JSON（不使用缓存）- Class版本
     */
    private static <T> T parseDirectly(@NonNull String json, @NonNull Class<T> clazz) {
        try {
            return JSON_MAPPER.readValue(json, clazz);
        } catch (JacksonException e) {
            String preview = json.length() > 100 ? json.substring(0, 100) + "..." : json;
            throw new JsonException(String.format("JSON解析失败: %s\n预览: %s", e.getMessage(), preview), e);
        }
    }

    /**
     * JSON字符串转对象（智能缓存优化）
     * @param json JSON字符串（非空且非空白）
     * @param clazz 目标类类型
     * @return 解析后的对象
     * @throws JsonException 当JSON格式错误或解析失败时抛出
     * @throws IllegalArgumentException 当输入参数无效时抛出
     */
    @NonNull
    public static <T> T fromJson(@NonNull String json, @NonNull Class<T> clazz) {
        if (json.isBlank()) {
            throw new IllegalArgumentException("JSON字符串不能为空");
        }

        // 优化：只对小于阈值的JSON使用缓存，避免缓存大对象
        if (json.length() > CACHE_THRESHOLD) {
            return parseDirectly(json, clazz);
        }

        // 简化缓存键：只使用类名和JSON哈希值
        String cacheKey = clazz.getName() + ":" + json.hashCode();

        try {
            return (T) JSON_CACHE.get(cacheKey, () -> parseDirectly(json, clazz));
        } catch (Exception e) {
            // 简化异常处理
            Throwable cause = e.getCause();
            if (cause instanceof JsonException) {
                throw (JsonException) cause;
            }
            if (cause instanceof RuntimeException) {
                throw (RuntimeException) cause;
            }
            throw new JsonException("JSON解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 安全解析 JSON 字符串（返回Optional）
     * 解析失败时返回空值，不抛出异常
     */
    @NonNull
    public static <T> Optional<T> safeParse(@Nullable String json, @NonNull Class<T> clazz) {
        if (json == null || json.isBlank()) return Optional.empty();
        try {
            return Optional.of(JSON_MAPPER.readValue(json, clazz));
        } catch (JacksonException e) {
            // 记录调试日志，便于开发时排查问题
            log.debug("JSON 安全解析失败，目标类型: {}, 错误: {}", clazz.getSimpleName(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 流式解析（适合大文件处理）
     */
    @NonNull
    public static <T> T parseStream(@NonNull InputStream input, @NonNull Class<T> clazz) {
        try {
            return JSON_MAPPER.readValue(input, clazz);
        } catch (IOException e) {
            throw new JsonException("流式解析失败", e);
        }
    }

    /**
     * 转换为 JsonNode
     */
    @NonNull
    public static JsonNode toJsonNode(@NonNull String json) {
        try {
            return JSON_MAPPER.readTree(json);
        } catch (JacksonException e) {
            throw new JsonException("JSON转换失败", e);
        }
    }

    /**
     * 获取 JSON 节点值
     * 示例：JsonUtils.getNodeValue(json, "/user/name", String.class)
     */
    @Nullable
    public static <T> T getNodeValue(@NonNull String json, @NonNull String path, @NonNull Class<T> type) {
        try {
            JsonNode root = JSON_MAPPER.readTree(json);
            JsonNode node = root.at(path);
            return JSON_MAPPER.treeToValue(node, type);
        } catch (JacksonException e) {
            return null;
        }
    }

    /**
     * 对象深拷贝
     */
    @SuppressWarnings("unchecked")
    @NonNull
    public static <T> T deepClone(@NonNull T obj) {
        return fromJson(Objects.requireNonNull(toJson(obj)), (Class<T>) obj.getClass());
    }

    /**
     * 对象转 Map（保留类型信息）
     * 支持复杂对象、Map、以及可序列化的对象
     */
    @NonNull
    public static Map<String, Object> toMap(@NonNull Object obj) {
        try {
            return JSON_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {});
        } catch (IllegalArgumentException e) {
            throw new JsonException("对象转换失败: " + e.getMessage(), e);
        }
    }

    // ============================节点创建方法=============================

    /**
     * 创建空的 ObjectNode
     * 用于构建 JSON 对象结构
     *
     * @return 新的空 ObjectNode 实例
     */
    @NonNull
    public static ObjectNode createObjectNode() {
        return JSON_MAPPER.createObjectNode();
    }

    /**
     * 创建空的 ArrayNode
     * 用于构建 JSON 数组结构
     *
     * @return 新的空 ArrayNode 实例
     */
    @NonNull
    public static ArrayNode createArrayNode() {
        return JSON_MAPPER.createArrayNode();
    }

    /**
     * 创建包含初始数据的 ObjectNode
     *
     * @param initialData 初始数据 Map
     * @return 包含初始数据的 ObjectNode
     */
    @NonNull
    public static ObjectNode createObjectNode(@Nullable Map<String, Object> initialData) {
        ObjectNode objectNode = JSON_MAPPER.createObjectNode();
        if (initialData != null) {
            initialData.forEach((key, value) -> {
                if (value == null) {
                    objectNode.putNull(key);
                } else if (value instanceof String) {
                    objectNode.put(key, (String) value);
                } else if (value instanceof Integer) {
                    objectNode.put(key, (Integer) value);
                } else if (value instanceof Long) {
                    objectNode.put(key, (Long) value);
                } else if (value instanceof Double) {
                    objectNode.put(key, (Double) value);
                } else if (value instanceof Boolean) {
                    objectNode.put(key, (Boolean) value);
                } else {
                    // 对于复杂对象，先转换为 JsonNode
                    objectNode.set(key, JSON_MAPPER.valueToTree(value));
                }
            });
        }
        return objectNode;
    }

    /**
     * 从现有对象创建 ObjectNode
     *
     * @param obj 要转换的对象
     * @return 转换后的 ObjectNode
     */
    @NonNull
    public static ObjectNode createObjectNode(@NonNull Object obj) {
        try {
            return (ObjectNode) JSON_MAPPER.valueToTree(obj);
        } catch (Exception e) {
            throw new JsonException("对象转换为 ObjectNode 失败", e);
        }
    }

    /**
     * 从 JSON 字符串创建 ObjectNode
     *
     * @param json JSON 字符串
     * @return 解析后的 ObjectNode
     */
    @NonNull
    public static ObjectNode createObjectNode(@NonNull String json) {
        try {
            JsonNode node = JSON_MAPPER.readTree(json);
            if (node.isObject()) {
                return (ObjectNode) node;
            } else {
                throw new JsonException("JSON 字符串不是有效的对象格式");
            }
        } catch (JacksonException e) {
            throw new JsonException("JSON 字符串解析为 ObjectNode 失败", e);
        }
    }

    /**
     * 从 JSON 字符串创建 ArrayNode
     *
     * @param json JSON 字符串
     * @return 解析后的 ArrayNode
     */
    @NonNull
    public static ArrayNode createArrayNode(@NonNull String json) {
        try {
            JsonNode node = JSON_MAPPER.readTree(json);
            if (node.isArray()) {
                return (ArrayNode) node;
            } else {
                throw new JsonException("JSON 字符串不是有效的数组格式");
            }
        } catch (JacksonException e) {
            throw new JsonException("JSON 字符串解析为 ArrayNode 失败", e);
        }
    }

    // ============================实用工具方法=============================

    /**
     * 判断字符串是否为有效的 JSON
     *
     * @param json 待检查的字符串
     * @return true 如果是有效的 JSON，否则 false
     */
    public static boolean isValidJson(@Nullable String json) {
        return parseJsonNode(json) != null;
    }

    /**
     * 内部方法：解析JSON并返回JsonNode，失败时返回null
     * 避免在多个验证方法中重复解析
     */
    @Nullable
    private static JsonNode parseJsonNode(@Nullable String json) {
        if (json == null || json.isBlank()) {
            return null;
        }
        try {
            return JSON_MAPPER.readTree(json);
        } catch (JacksonException e) {
            return null;
        }
    }

    /**
     * 判断字符串是否为有效的 JSON 对象
     *
     * @param json 待检查的字符串
     * @return true 如果是有效的 JSON 对象，否则 false
     */
    public static boolean isValidJsonObject(@Nullable String json) {
        JsonNode node = parseJsonNode(json);
        return node != null && node.isObject();
    }

    /**
     * 判断字符串是否为有效的 JSON 数组
     *
     * @param json 待检查的字符串
     * @return true 如果是有效的 JSON 数组，否则 false
     */
    public static boolean isValidJsonArray(@Nullable String json) {
        JsonNode node = parseJsonNode(json);
        return node != null && node.isArray();
    }

    /**
     * 获取JSON类型
     *
     * @param json 待检查的字符串
     * @return JSON类型枚举
     */
    public static JsonType getJsonType(@Nullable String json) {
        JsonNode node = parseJsonNode(json);
        if (node == null) {
            return JsonType.INVALID;
        }
        if (node.isObject()) {
            return JsonType.OBJECT;
        }
        if (node.isArray()) {
            return JsonType.ARRAY;
        }
        if (node.isTextual()) {
            return JsonType.STRING;
        }
        if (node.isNumber()) {
            return JsonType.NUMBER;
        }
        if (node.isBoolean()) {
            return JsonType.BOOLEAN;
        }
        if (node.isNull()) {
            return JsonType.NULL;
        }
        return JsonType.OTHER;
    }

    /**
     * JSON类型枚举
     */
    @Getter
    public enum JsonType {
        OBJECT("对象"),
        ARRAY("数组"),
        STRING("字符串"),
        NUMBER("数字"),
        BOOLEAN("布尔值"),
        NULL("空值"),
        INVALID("无效"),
        OTHER("其他");

        private final String description;

        JsonType(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return description;
        }
    }

    /**
     * 美化 JSON 字符串
     *
     * @param json 原始 JSON 字符串
     * @return 美化后的 JSON 字符串
     */
    @Nullable
    public static String prettify(@Nullable String json) {
        JsonNode node = parseJsonNode(json);
        if (node == null) {
            return json;
        }
        try {
            return JSON_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(node);
        } catch (JacksonException e) {
            throw new JsonException("JSON 美化失败", e);
        }
    }

    /**
     * 压缩 JSON 字符串（移除空白字符）
     *
     * @param json 原始 JSON 字符串
     * @return 压缩后的 JSON 字符串
     */
    @Nullable
    public static String minify(@Nullable String json) {
        if (json == null || json.isBlank()) {
            return json;
        }
        try {
            JsonNode node = JSON_MAPPER.readTree(json);
            return JSON_MAPPER.writeValueAsString(node);
        } catch (JacksonException e) {
            throw new JsonException("JSON 压缩失败", e);
        }
    }

    // ============================缓存管理方法=============================

    /**
     * 设置缓存阈值
     *
     * @param threshold 新的缓存阈值（字节）
     */
    public static void setCacheThreshold(int threshold) {
        if (threshold < 0) {
            throw new IllegalArgumentException("缓存阈值不能为负数");
        }
        CACHE_THRESHOLD = threshold;
        log.info("JSON缓存阈值已更新为: {} 字节", threshold);
    }

    /**
     * 获取当前缓存阈值
     *
     * @return 当前缓存阈值（字节）
     */
    public static int getCacheThreshold() {
        return CACHE_THRESHOLD;
    }

    /**
     * 清空JSON解析缓存
     */
    public static void clearCache() {
        JSON_CACHE.invalidateAll();
        log.info("JSON解析缓存已清空");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息字符串
     */
    public static String getCacheStats() {
        var stats = JSON_CACHE.stats();
        return String.format("缓存统计 - 大小: %d, 命中率: %.2f%%, 命中次数: %d, 未命中次数: %d",
            JSON_CACHE.size(),
            stats.hitRate() * 100,
            stats.hitCount(),
            stats.missCount());
    }

    /**
     * 禁用缓存（设置阈值为0）
     */
    public static void disableCache() {
        setCacheThreshold(0);
    }

    /**
     * 启用缓存（恢复默认阈值1KB）
     */
    public static void enableCache() {
        setCacheThreshold(10240);
    }

}
