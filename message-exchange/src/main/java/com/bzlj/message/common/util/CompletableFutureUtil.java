package com.bzlj.message.common.util;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * CompletableFuture使用工具类
 *
 * <AUTHOR>
 * @date 2024/9/14 10:18
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class CompletableFutureUtil {
    /**
     * 自定义线程工厂
     */
    static class UtilThreadFactory implements ThreadFactory {
        private final AtomicInteger count = new AtomicInteger(0);
    
        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r);
            thread.setName("CompletableFutureUtil-Thread-" + count.getAndIncrement());
            return thread;
        }
    }

    /**
     * 自定义线程池
     */
    private static final ThreadPoolExecutor UTIL_POOL = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 4,
            5,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(10000),
            new UtilThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 创建一个单线程的 ScheduledExecutorService 用于处理超时
     */
    private static final ScheduledExecutorService SCHEDULER_POOL = Executors.newScheduledThreadPool(1);

    static {
        // 设置优雅关闭线程池
        Runtime.getRuntime().addShutdownHook(new Thread(UTIL_POOL::shutdown));
        Runtime.getRuntime().addShutdownHook(new Thread(SCHEDULER_POOL::shutdown));
    }

    /**
     * 异步执行任务
     *
     * @param task 要执行的任务
     * @param <T>  返回值类型
     * @return CompletableFuture 包装的任务结果
     */
    public static <T> CompletableFuture<T> runAsync(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, UTIL_POOL);
    }

    /**
     * 异步执行任务并处理结果和异常
     *
     * @param task      要执行的任务
     * @param onSuccess 成功时的处理
     * @param onFailure 失败时的处理
     * @param <T>       返回值类型
     */
    public static <T> void runAsync(Supplier<T> task, Consumer<T> onSuccess, Consumer<Throwable> onFailure) {
        CompletableFuture.supplyAsync(task, UTIL_POOL)
                .thenAccept(onSuccess)
                .exceptionally(ex -> {
                    onFailure.accept(ex);
                    return null;
                });
    }


    /**
     * 异步执行无返回值的任务
     *
     * @param task 异步任务
     * @return 包含状态的 CompletableFuture
     */
    public static CompletableFuture<Void> runVoidAsync(Runnable task) {
        return CompletableFuture.runAsync(task, UTIL_POOL);
    }

    /**
     * 异步执行无返回值的任务，并在完成或失败时进行处理
     *
     * @param task      异步任务
     * @param onSuccess 成功时的处理函数
     * @param onFailure 失败时的处理函数
     */
    public static CompletableFuture<Void> runVoidAsync(Runnable task, Runnable onSuccess, Consumer<Throwable> onFailure) {
        return CompletableFuture.runAsync(task, UTIL_POOL)
                .thenRun(onSuccess)
                .exceptionally(ex -> {
                    onFailure.accept(ex);
                    return null;
                });
    }

    /**
     * 串行化两个 CompletableFuture
     *
     * @param <T>      第一个任务返回值类型
     * @param <U>      第二个任务返回值类型
     * @param future   第一个任务
     * @param nextTask 第二个任务
     * @return 串行化后的 CompletableFuture
     */
    public static <T, U> CompletableFuture<U> chain(CompletableFuture<T> future, Function<T, U> nextTask) {
        return future.thenApplyAsync(nextTask, UTIL_POOL);
    }

    /**
     * 并行执行多个 CompletableFuture 并处理结果
     *
     * @param futures   CompletableFuture 数组
     * @param onSuccess 成功时的处理
     * @param onFailure 失败时的处理
     */
    public static CompletableFuture<Void> runAll(CompletableFuture<?>[] futures, Runnable onSuccess, Consumer<Throwable> onFailure) {
        return CompletableFuture.allOf(futures)
                .thenRun(onSuccess)
                .exceptionally(ex -> {
                    onFailure.accept(ex);
                    return null;
                });
    }


    /**
     * 合并两个 CompletableFuture 的结果
     *
     * @param future1  第一个 CompletableFuture
     * @param future2  第二个 CompletableFuture
     * @param combiner 合并两个结果的函数
     * @param <T>      第一个结果的类型
     * @param <U>      第二个结果的类型
     * @param <V>      合并结果的类型
     * @return 合并后的 CompletableFuture
     */
    public static <T, U, V> CompletableFuture<V> combine(
            CompletableFuture<T> future1,
            CompletableFuture<U> future2,
            BiFunction<T, U, V> combiner) {
        return future1.thenCombineAsync(future2, combiner, UTIL_POOL);
    }

    /**
     * 等待所有 CompletableFuture 完成
     *
     * @param futures CompletableFuture 数组
     * @return 完成时的 CompletableFuture
     */
    public static CompletableFuture<Void> allOf(CompletableFuture<?>... futures) {
        return CompletableFuture.allOf(futures);
    }

    /**
     * 处理 CompletableFuture 的异常
     *
     * @param future  CompletableFuture 对象
     * @param handler 异常处理函数
     * @param <T>     返回值类型
     * @return 处理后的 CompletableFuture
     */
    public static <T> CompletableFuture<T> handleException(
            CompletableFuture<T> future,
            Function<Throwable, ? extends T> handler) {
        return future.exceptionally(handler);
    }

    /**
     * 超时控制
     *
     * @param future  CompletableFuture 对象
     * @param timeout 超时时间
     * @param unit    时间单位
     * @param <T>     返回值类型
     * @return 带超时控制的 CompletableFuture
     */
    public static <T> CompletableFuture<T> withTimeout(
            CompletableFuture<T> future,
            long timeout,
            TimeUnit unit) {
        CompletableFuture<T> timeoutFuture = new CompletableFuture<>();
        SCHEDULER_POOL.schedule(() -> timeoutFuture.completeExceptionally(new TimeoutException()), timeout, unit);
        return future.applyToEither(timeoutFuture, Function.identity());
    }
}
