package com.bzlj.message.common.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-04-15
 */
@Slf4j
@Component
public class EPlatTokenUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static String TOKEN_URL;
    private static String CLIENT_ID;
    private static String CLIENT_SECRET;
    @Value("${app_access.url:http://eplatst.baocloud.cn/eplat/oauth/token}")
    public void setTokenUrl(String url) {
        TOKEN_URL = url;
    }
    @Value("${app_access.client_id:message-exchangerje46Ac78vF6BIyA}")
    public void setClientId(String id) {
        CLIENT_ID = id;
    }

    @Value("${app_access.client_secret:BA4BF243E56293823B386E5DA4EE7937}")
    public void setClientSecret(String secret) {
        CLIENT_SECRET = secret;
    }
    public static String getEPlatToken() {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost post = new HttpPost(TOKEN_URL);
            post.setHeader("Content-Type", "application/x-www-form-urlencoded");

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("client_id", CLIENT_ID));
            params.add(new BasicNameValuePair("client_secret", CLIENT_SECRET));
            params.add(new BasicNameValuePair("grant_type", "client_credentials"));
            params.add(new BasicNameValuePair("scope", "read"));

            post.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = client.execute(post)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                    JsonNode jsonNode = objectMapper.readTree(responseBody);
                    if (jsonNode.has("access_token")) {
                        return jsonNode.get("access_token").asText();
                    } else {
                        log.error("Response does not contain 'access_token': {}", responseBody);
                        throw new RuntimeException("Access token not found in response");
                    }
                } else {
                    log.error("HTTP request failed with status code: {}", statusCode);
                    throw new RuntimeException("HTTP request failed with status code: " + statusCode);
                }
            }
        } catch (IOException e) {
            log.error("Error occurred while fetching token", e);
            throw new RuntimeException("Error occurred while fetching token", e);
        }
    }
}
