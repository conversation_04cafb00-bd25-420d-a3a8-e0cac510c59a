package com.bzlj.message.common.exception;

/**
 * 自定义Json异常
 *
 * <AUTHOR>
 * @date 2025/3/21 10:58
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public class JsonException extends RuntimeException {
    public JsonException(String message, Throwable cause) {
        super(message, cause);
    }

    public JsonException(String message) {
        super(message);
    }
}
