eplat:
  ordinary:
    url: ${ORDINARY_URL:http://eplatxt.baocloud.cn/service/S_EPLAT_04}
  telegram:
    url: ${TELEGRAM_URL:http://eplatxt.baocloud.cn/service/S_EPLAT_03}
message:
  ordinary:
    key: ${ORDINARY_KEY:R_GYCK_01} #路由规则，具体现场确认
IXBus:
  serviceId: ${IXBUS_SERVICE:GYCK_TEST_02}
eplattest:
  url: ${EPLATTEST_URL:http://eplat.baocloud.cn/service}
  todo_message: ${EPLATTEST_TODO_MESSAGE:S_BE_MS_01}
  push_message: ${EPLATTEST_PUSH_MESSAGE:S_BE_MS_02}
  query_message: ${EPLATTEST_QUERY_MESSAGE:S_BE_MS_07}
  emil_message: ${EPLATTEST_EMAIL_MESSAGE:S_EPLAT_22}
  text__message: ${EPLATTEST_TEXT_MESSAGE:S_BE_MS_13}

app_access:
  url: ${APP_ACCESS_URL:http://eplatxp.baocloud.cn/eplat/oauth/token}
  client_id: ${APP_ACCESS_CLIENT_ID:message-exchangehKgl38gdzyjuMgfD}
  client_secret: ${APP_ACCESS_CLIENT_SECRET:B2BE4EB2963EDE12107AF5D353A1D76A}

spring:
  cloud:
    stream:
      kafka:
        binder:
          brokers: ${KAFKA:10.81.12.47:9092,10.81.12.48:9092,10.81.12.49:9092}
          auto-create-topics: true # 自动创建topics
          required-acks: all
  kafka:
    consumer:
      enable-auto-commit: true #消费者的偏移量是否在后台定期提交。
      auto-offset-reset: latest
        #earliest	从 Topic 的 最早消息（第一条）开始消费。	需要处理所有历史消息（包括旧数据）。
        #latest	从 Topic 的 最新消息（当前写入位置）开始消费，忽略历史消息。	只关心最新数据，不处理积压消息。
      #none	如果无有效 offset，抛出 NoOffsetForPartitionException 异常。	需要严格手动管理 offset 的场景。
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

# Redisson Redis 配置 - 单机模式

redisson:
  mode: cluster  # 集群模式
  cluster-config:
    # 集群节点地址列表
    node-addresses:
      - "redis://${REDIS_NODE1_HOST:***********}:${REDIS_NODE1_PORT:6379}"
      - "redis://${REDIS_NODE2_HOST:***********}:${REDIS_NODE2_PORT:6379}"
      - "redis://${REDIS_NODE3_HOST:***********}:${REDIS_NODE3_PORT:6379}"
#      - "redis://${REDIS_NODE1_HOST:***********}:${REDIS_NODE1_PORT:6379}"
#      - "redis://${REDIS_NODE2_HOST:***********}:${REDIS_NODE2_PORT:6379}"
#      - "redis://${REDIS_NODE3_HOST:***********}:${REDIS_NODE3_PORT:6379}"
    # Redis 密码
    password: ${REDIS_PASSWORD:KyAbse2SvEu622da}

    # 连接池配置
    master-connection-pool-size: 64
    slave-connection-pool-size: 64
    master-connection-minimum-idle-size: 10
    slave-connection-minimum-idle-size: 10

    # 超时配置
    connect-timeout: 3000
    timeout: 3000
    retry-attempts: 3
    retry-interval: 1500
    idle-connection-timeout: 10000
    ping-connection-interval: 30000
    keep-alive: true

    # 集群特有配置
    scan-interval: 1000  # 集群扫描间隔（毫秒）
    read-mode: MASTER     # 读取模式：SLAVE（从节点）、MASTER（主节点）、MASTER_SLAVE（主从节点）
    subscription-mode: MASTER  # 订阅模式：SLAVE（从节点）、MASTER（主节点）

dynamic:
  mongo:
    enable-dynamic: true
    primary: primary
    datasource:
      primary:
        name: primary
        url: ${MESSAGE_MONGO_URL:***********:27017,***********:27017,***********:27017}
        database: ${MESSAGE_MONGO_DATABASE:bwty-craft-pro}
        username: ${MESSAGE_MONGO_USERNAME:admin}
        password: ${MESSAGE_MONGO_PWD:qbUgBCCCKZ47pI7i}
        authentication-database: admin

queue:
  max-size: 10000