# Redis 哨兵模式配置示例
# 使用方式：复制此文件为 application-sentinel.yml 并修改相应配置
# 启动命令：java -jar app.jar --spring.profiles.active=sentinel

spring:
  application:
    name: message-exchange
  profiles:
    active: sentinel

# Redisson Redis 配置 - 哨兵模式
redisson:
  mode: sentinel  # 哨兵模式
  sentinel-config:
    # 主服务器名称（在哨兵配置中定义的名称）
    master-name: ${REDIS_MASTER_NAME:mymaster}
    
    # 哨兵节点地址列表
    sentinel-addresses:
      - "redis://${SENTINEL1_HOST:************}:${SENTINEL1_PORT:26379}"
      - "redis://${SENTINEL2_HOST:************}:${SENTINEL2_PORT:26379}"
      - "redis://${SENTINEL3_HOST:************}:${SENTINEL3_PORT:26379}"
    
    # Redis 数据库索引
    database: ${REDIS_DATABASE:0}
    
    # Redis 密码
    password: ${REDIS_PASSWORD:}
    
    # 哨兵密码（如果哨兵也设置了密码）
    sentinel-password: ${SENTINEL_PASSWORD:}
    
    # 连接池配置
    master-connection-pool-size: 64
    slave-connection-pool-size: 64
    master-connection-minimum-idle-size: 10
    slave-connection-minimum-idle-size: 10
    
    # 超时配置
    connect-timeout: 3000
    timeout: 3000
    retry-attempts: 3
    retry-interval: 1500
    idle-connection-timeout: 10000
    ping-connection-interval: 30000
    keep-alive: true
    
    # 读写模式配置
    read-mode: SLAVE     # 读取模式：SLAVE（从节点）、MASTER（主节点）、MASTER_SLAVE（主从节点）
    subscription-mode: MASTER  # 订阅模式：SLAVE（从节点）、MASTER（主节点）

# 其他配置保持与主配置文件一致
server:
  port: 8080

logging:
  level:
    com.bzlj.message: INFO
    org.redisson: DEBUG  # 开启 Redisson 调试日志以便排查哨兵连接问题
