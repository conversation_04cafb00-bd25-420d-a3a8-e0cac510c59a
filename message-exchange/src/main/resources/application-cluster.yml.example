# Redis 集群模式配置示例
# 使用方式：复制此文件为 application-cluster.yml 并修改相应配置
# 启动命令：java -jar app.jar --spring.profiles.active=cluster

spring:
  application:
    name: message-exchange
  profiles:
    active: cluster

# Redisson Redis 配置 - 集群模式
redisson:
  mode: cluster  # 集群模式
  cluster-config:
    # 集群节点地址列表
    node-addresses:
      - "redis://${REDIS_NODE1_HOST:************}:${REDIS_NODE1_PORT:7000}"
      - "redis://${REDIS_NODE2_HOST:************}:${REDIS_NODE2_PORT:7001}"
      - "redis://${REDIS_NODE3_HOST:************}:${REDIS_NODE3_PORT:7002}"
      - "redis://${REDIS_NODE4_HOST:************}:${REDIS_NODE4_PORT:7003}"
      - "redis://${REDIS_NODE5_HOST:************}:${REDIS_NODE5_PORT:7004}"
      - "redis://${REDIS_NODE6_HOST:************}:${REDIS_NODE6_PORT:7005}"
    
    # Redis 密码
    password: ${REDIS_PASSWORD:}
    
    # 连接池配置
    master-connection-pool-size: 64
    slave-connection-pool-size: 64
    master-connection-minimum-idle-size: 10
    slave-connection-minimum-idle-size: 10
    
    # 超时配置
    connect-timeout: 3000
    timeout: 3000
    retry-attempts: 3
    retry-interval: 1500
    idle-connection-timeout: 10000
    ping-connection-interval: 30000
    keep-alive: true
    
    # 集群特有配置
    scan-interval: 1000  # 集群扫描间隔（毫秒）
    read-mode: SLAVE     # 读取模式：SLAVE（从节点）、MASTER（主节点）、MASTER_SLAVE（主从节点）
    subscription-mode: MASTER  # 订阅模式：SLAVE（从节点）、MASTER（主节点）

# 其他配置保持与主配置文件一致
server:
  port: 8080

logging:
  level:
    com.bzlj.message: INFO
    org.redisson: DEBUG  # 开启 Redisson 调试日志以便排查集群连接问题
