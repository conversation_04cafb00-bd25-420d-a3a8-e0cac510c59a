eplat:
  ordinary:
    url: ${ORDINARY_URL:http://eplatxt.baocloud.cn/service/S_EPLAT_04}
  telegram:
    url: ${TELEGRAM_URL:http://eplatxt.baocloud.cn/service/S_EPLAT_03}
message:
  ordinary:
    key: ${ORDINARY_KEY:R_GYCK_01} #路由规则，具体现场确认
IXBus:
  serviceId: ${IXBUS_SERVICE:GYCK_TEST_02}
eplattest:
  url: ${EPLATTEST_URL:http://eplattest.baocloud.cn/service}
  todo_message: ${EPLATTEST_TODO_MESSAGE:S_BE_MS_01}
  push_message: ${EPLATTEST_PUSH_MESSAGE:S_BE_MS_02}
  query_message: ${EPLATTEST_QUERY_MESSAGE:S_BE_MS_07}
  emil_message: ${EPLATTEST_EMAIL_MESSAGE:S_EPLAT_22}
  text__message: ${EPLATTEST_TEXT_MESSAGE:S_BE_MS_13}
app_access:
  url: ${APP_ACCESS_URL:http://eplatst.baocloud.cn/eplat/oauth/token}
  client_id: ${APP_ACCESS_CLIENT_ID:message-exchangerje46Ac78vF6BIyA}
  client_secret: ${APP_ACCESS_CLIENT_SECRET:BA4BF243E56293823B386E5DA4EE7937}

spring:
  cloud:
    stream:
      kafka:
        binder:
          brokers: ${KAFKA:192.168.100.196:9192,192.168.100.196:9292,192.168.100.196:9392}
          auto-create-topics: true # 自动创建topics
          required-acks: all
  kafka:
    consumer:
      enable-auto-commit: true #消费者的偏移量是否在后台定期提交。
      auto-offset-reset: latest
        #earliest	从 Topic 的 最早消息（第一条）开始消费。	需要处理所有历史消息（包括旧数据）。
        #latest	从 Topic 的 最新消息（当前写入位置）开始消费，忽略历史消息。	只关心最新数据，不处理积压消息。
      #none	如果无有效 offset，抛出 NoOffsetForPartitionException 异常。	需要严格手动管理 offset 的场景。
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

# Redisson Redis 配置 - 单机模式
redisson:
  mode: single  # 部署模式：single（单机）、cluster（集群）、sentinel（哨兵）
  single-server-config:
    address: "redis://${REDIS_HOST:***************}:${REDIS_PORT:31727}"
    database: ${REDIS_DATABASE:0}
    password: ${REDIS_PASSWORD:}
    connection-pool-size: 64
    connection-minimum-idle-size: 10
    connect-timeout: 3000
    timeout: 3000
    retry-attempts: 3
    retry-interval: 1500
    idle-connection-timeout: 10000
    ping-connection-interval: 30000
    keep-alive: true

dynamic:
  mongo:
    enable-dynamic: true
    primary: primary
    datasource:
      primary:
        name: primary
        url: ${MESSAGE_MONGO_URL:***************:32175}
        database: ${MESSAGE_MONGO_DATABASE:bwty-craft-dev}
        username: ${MESSAGE_MONGO_USERNAME:root}
        password: ${MESSAGE_MONGO_PWD:BICItech@123}
        authentication-database: admin

queue:
  max-size: 10000