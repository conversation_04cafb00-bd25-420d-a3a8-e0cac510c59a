# 宝武特冶消息分发中心

## 项目结构说明
message-exchange  
    ├── config      --配置信息  
    ├── controller  --控制器  
    ├── dto         --数据传输对象  
    ├── enums       --枚举  
    ├── exception   --自定义异常  
    ├── handlers    --电文解析验证***  
    │    ├── MessageVerifyHandler     -- 消息中心电文验证  
    │    └── XBusMessageVerifyHandler -- XBus电文验证  
    ├── mongo       --数据库  
    ├── monitor     --kafka心跳监测***  
    │   ├── DefaultKafkaReconnectHandler   -- 开机或停机恢复后的异常落库消息加载   
    │   └── KafkaHealthMonitor             -- kafka心跳监测  
    ├── processors  --消息推送***  
    │   └── QueueToKafkaProcessor -- 消息推送到kafka  
    ├── service     --服务类  
    └── util        --工具类  

## 项目说明文档 详见钉钉
https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJP31Y1yrfnGg2X4yVzN67Mw4?utm_scene=person_space

## 项目交接文档 详见钉钉
https://alidocs.dingtalk.com/i/nodes/mExel2BLV547G7EDhbaRoX4pWgk9rpMq?utm_scene=person_space

## 数据库结构说明mongo
db.createCollection("topic_map");           // topic主题映射表   
db.createCollection("message_rules");       // 电文解析规则表  
db.createCollection("message_exception");   // 电文解析异常表  

## 功能维护说明
电文topic需要同步维护在公司内部的在线文档方便他人查阅
电文topic的映射后续修改或添加需要维护mongo数据库中的topic_map文档
电文解析规则变更需要同步维护mongo数据库中的message_rules文档

## 消息分发常见解析异常说明
目前测试大多数异常都是电文格式或数据不正确、（例如之前破浪反馈消息结构是对的但是解析异常、分析后发现入参的电文消息中有10个固定循环体、只有第一个循环体有值、 
其余全是空字符串、但是在在该电文的规则中又有循环体中的数据必填描述、入参数据不按照规则来导致解析异常）异常信息可在mongo数据库中的message_exception查看，可根据异常消息分析异常原因    