package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-19 18:12
 */
@Data
public class ContinuousStepVO {

    @ComponentProperty(label = "工步id", display = false, path = "workStepId")
    private String workStepId;

    @ComponentProperty(label = "工步编号",display = false, order = 1, path = "stepCode")
    private String stepCode;

    @ComponentProperty(label = "工步", order = 2, path = "workStepName")
    private String workStepName;

    @ComponentProperty(label = "开始时间", order = 3, display = false, path = "startTime")
    private LocalDateTime startTime;

    @ComponentProperty(label = "结束时间", order = 4,display = false, path = "endTime")
    private LocalDateTime endTime;

    @ComponentProperty(label = "工艺参数", order = 5, path = "continuousParams",subClass = ContinuousParamsVO.class)
    private List<ContinuousParamsVO> continuousParams;

    @ComponentProperty(label = "排序",display = false, order = 6, path = "stepOrder")
    private Integer stepOrder;

}
