package com.bzlj.craft.vo.qualitytrace;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全流程追溯物料VO
 *
 * <AUTHOR>
 * @date 2025/3/25 10:39
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraceMaterialVO {
    /**
     * 物料Id
     */
    private String materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;
}
