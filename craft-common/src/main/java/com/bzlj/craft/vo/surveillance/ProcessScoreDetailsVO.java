package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 扣分明细
 * @date 2025-03-11 17:48
 */
@Data
public class ProcessScoreDetailsVO {

    @ComponentProperty(label = "id", display = false,order = 0, path = "id")
    private String id;

    @ComponentProperty(label = "工步", searchable = true, order = 1, display = true, path = "processStepName")
    private String processStepName;

    @ComponentProperty(label = "工艺参数", searchable = true, order = 2, path = "processParam")
    private String processParam;

    @ComponentProperty(label = "标准值", searchable = true, order = 3, path = "standard")
    private String standard;

    @ComponentProperty(label = "实际值", order = 4, path = "actual")
    private String actual;

    @ComponentProperty(label = "偏离程度", order = 5, path = "deviationDegree")
    private String deviationDegree;

    @ComponentProperty(label = "扣分值", order = 6, path = "deduct")
    private BigDecimal deduct;

    @ComponentProperty(label = "评分版本", order = 7, path = "version")
    private Integer version;

    @ComponentProperty(label = "操作", order = 8, path = "handle")
    private String handle;

    @ComponentProperty(label = "任务id", order = 9, path = "taskId", display = false)
    private String taskId;

    @ComponentProperty(label = "评分id", order = 10, path = "processScoreId", display = false)
    private String processScoreId;

}
