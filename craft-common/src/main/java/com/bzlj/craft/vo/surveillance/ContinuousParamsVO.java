package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-17 17:15
 */
@Data
public class ContinuousParamsVO {
    @ComponentProperty(label = "参数id", display = false, path = "id")
    private String id;

    @ComponentProperty(label = "工艺参数", order = 1, path = "stepParam")
    private String stepParam;

    @ComponentProperty(label = "数据点位", order = 2,display = false, path = "dataPointCode")
    private String dataPointCode;

}
