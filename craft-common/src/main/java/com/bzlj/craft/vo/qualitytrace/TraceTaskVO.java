package com.bzlj.craft.vo.qualitytrace;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 全流程追溯任务VO
 *
 * <AUTHOR>
 * @date 2025/3/25 10:39
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraceTaskVO {

    /**
     * 任务Id
     */
    private String taskId;
    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    /**
     * 车间Id
     */
    private String workShopId;

    /**
     * 厂区Id
     */
    private String plantId;

    /**
     * 输入物料
     */
    private List<TraceMaterialVO> inputMaterial;

    /**
     * 输出物料
     */
    private List<TraceMaterialVO> outputMaterial;

    /**
     * 设备
     */
    private List<TraceEquipVO> equip;
}
