package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-17 17:15
 */
@Data
public class OperationLogVO {
    @ComponentProperty(label = "操作记录id", display = false, path = "logId")
    private String logId;

    @ComponentProperty(label = "操作时间", order = 2, path = "operationTime")
    private LocalDateTime operationTime;

    @ComponentProperty(label = "操作者", order = 3, path = "operatorId")
    private String operator;

    @ComponentProperty(label = "操作内容", order = 4, path = "description")
    private Map<String, Object> description;

}
