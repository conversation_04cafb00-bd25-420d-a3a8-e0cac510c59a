package com.bzlj.craft.vo.qualitytrace;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全流程追溯设备VO
 *
 * <AUTHOR>
 * @date 2025/3/25 10:39
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TraceEquipVO {

    /**
     * 设备Id
     */
    private String equipId;


    /**
     * 设备名称
     */
    private String equipName;


    /**
     * 设备编码
     */
    private String equipCode;
}
