package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-21 14:19
 */
@Data
public class AbnormalRecordVO {

    @ComponentProperty(label = "记录时间", path = "alertTime")
    private LocalDateTime alertTime;

    @ComponentProperty(label = "记录方式", order = 1, path = "alertType")
    private String alertType;

    @ComponentProperty(label = "记录内容", order = 2, path = "alarmContent")
    private String alarmContent;

}
