package com.bzlj.craft.vo.qualitytrace;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 全流程追溯返回值
 *
 * <AUTHOR>
 * @date 2025/3/24 11:18
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QualityTraceVO {
    /**
     * 高亮节点,预留字段
     */
    private List<TraceTaskVO> nodeLightList;
    /**
     * 追溯路径
     */
    private List<TracePathVO> path;
    /**
     * 产出搜索条件物料的任务
     */
    private List<TraceTaskVO> selectTasks;
}
