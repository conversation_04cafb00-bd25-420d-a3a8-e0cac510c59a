package com.bzlj.craft.vo.surveillance;

import com.bzlj.base.component.annotation.ComponentProperty;
import com.bzlj.base.enums.DataSourceType;
import com.bzlj.base.enums.HttpMethod;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class DeviationTracingVO {

    @ComponentProperty(label = "预警id", display = false, path = "id")
    private String id;

    @ComponentProperty(label = "所属分厂", order = 1, path = "plantCode", searchable = true,initialRequest = true, dataSourceType = DataSourceType.API,
            dataSource = @ComponentProperty.DataSource(
                    apiUrl = "/craft/craft/findPlantList",
                    httpMethod = HttpMethod.GET,
                    apiParams = {
                            @ComponentProperty.ApiParam(key = "params", value = "{\"status\":\"active\",\"type\":\"plant\"}"),
                            @ComponentProperty.ApiParam(key = "dependencyParams", value = "{\"userId\":\"${currentUserId}\"}")
                    },
                    mapping = @ComponentProperty.Mapping(
                            valueKey = "plantCode",
                            labelKey = "plantName"
                    )

            )
    )
    private String plantCode;

    @ComponentProperty(label = "任务id", display = false, path = "taskId", order = 2)
    private String taskId;

    @ComponentProperty(label = "任务编号", order = 3, path = "taskCode", searchable = true)
    private String taskCode;

    @ComponentProperty(label = "设备id", display = false, path = "equipmentId", order = 4)
    private String equipmentId;

    @ComponentProperty(label = "设备编号", order = 5, path = "taskCode")
    private String equipmentCode;

    @ComponentProperty(label = "工序类型", order = 6, path = "processType", searchable = true,initialRequest = true, dataSourceType = DataSourceType.API,
            dependencies = {"plantCode"},dataSource = @ComponentProperty.DataSource(
            apiUrl = "/craft/craft/findProcessByPlantCode",
            httpMethod = HttpMethod.GET,
            apiParams = {@ComponentProperty.ApiParam(
                    key = "dependencyParams",
                    value = "{\"plantCode\":\"plantCode\"}"
            )},
            mapping = @ComponentProperty.Mapping(
                    valueKey = "processName",
                    labelKey = "processName"
            )

    )
    )
    private String processType;

    @ComponentProperty(label = "工步id", display = false, path = "工步id", order = 7)
    private String stepId;

    @ComponentProperty(label = "工步", order = 8, path = "stepName")
    private String stepName;

    @ComponentProperty(label = "参数id", display = false, path = "parameterId", order = 9)
    private String parameterId;

    @ComponentProperty(label = "参数名称", order = 10, path = "parameterName")
    private String parameterName;

    /**
     * 报警时间
     */
    @ComponentProperty(label = "发生时刻", order = 11, path = "startTime")
    private LocalDateTime startTime;

    @ComponentProperty(label = "持续时长(分)", order = 12, path = "duration")
    private BigDecimal duration;

    @ComponentProperty(label = "最大偏离程度%", order = 13, path = "maxDegree")
    private BigDecimal maxDegree;

    @ComponentProperty(label = "平均偏离程度%", order = 14, path = "aveDegree")
    private BigDecimal aveDegree;

}