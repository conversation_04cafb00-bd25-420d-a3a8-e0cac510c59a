package com.bzlj.craft.vo.qualitytrace;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 全流程追溯路径VO
 *
 * <AUTHOR>
 * @date 2025/3/25 10:39
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TracePathVO {

    /**
     * 起节点
     */
    private TraceTaskVO start;


    /**
     * 终节点
     */
    private TraceTaskVO end;
}
