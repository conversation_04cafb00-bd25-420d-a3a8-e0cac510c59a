# 工艺参数导入 - 支持更新功能说明

## 功能概述

工艺参数导入功能现在支持根据`paramCode`对已存在的参数进行更新，而不仅仅是新增参数。

## 实现逻辑

### 1. 参数存在性检查
在处理每个Excel行时，系统会：
1. 根据`stepId`和`paramCode`查询数据库中是否已存在该参数
2. 如果存在，则更新现有参数
3. 如果不存在，则创建新参数

### 2. 更新策略

#### 更新现有参数时：
- **保持不变的字段**：
  - `paramId`：保持原有的主键ID
  - `paramOrder`：保持原有的参数顺序
  - `version`：保持原有的版本号
  - `step`：保持原有的工步关联

- **更新的字段**：
  - `paramName`：更新为Excel中的新名称
  - `paramType`：更新为Excel中的新类型

#### 创建新参数时：
- 所有字段都按照Excel中的数据设置
- `paramOrder`基于数据库中已有参数数量计算
- `version`设置为1

### 3. 缓存机制优化

新增了`stepParameterCache`来缓存已查询的参数：
```java
// 缓存工步下已有的参数，用于判断是否需要更新
private final Map<String, Map<String, StepParameter>> stepParameterCache = new ConcurrentHashMap<>();
```

## 代码实现

### 1. 新增Repository方法
```java
/**
 * 根据工步ID和参数代码查询参数
 * @param stepId 工步ID
 * @param paramCode 参数代码
 * @return 参数对象，如果不存在则返回null
 */
StepParameter findByStepIdAndParamCode(String stepId, String paramCode);
```

### 2. 核心更新逻辑
```java
// 检查是否已存在相同paramCode的参数
StepParameter existingParameter = getExistingParameter(stepId, paramCodeFromExcel);

StepParameter stepParameter;
if (existingParameter != null) {
    // 更新现有参数
    stepParameter = existingParameter;
    stepParameter.setParamName(paramName);
    stepParameter.setParamType(paramTypeDictItem);
    // 保持原有的paramOrder和version
} else {
    // 创建新参数
    stepParameter = new StepParameter();
    stepParameter.setStep(processStep);
    stepParameter.setParamCode(paramCodeFromExcel);
    stepParameter.setParamName(paramName);
    stepParameter.setParamType(paramTypeDictItem);
    
    // 计算参数顺序
    int currentCount = stepParamCountMap.computeIfAbsent(stepId, stepParameterRepository::countByStepId);
    int paramOrder = currentCount + 1;
    stepParamCountMap.put(stepId, paramOrder);
    
    stepParameter.setParamOrder(paramOrder);
    stepParameter.setVersion(1);
}
```

### 3. 参数查询缓存
```java
private StepParameter getExistingParameter(String stepId, String paramCode) {
    Map<String, StepParameter> stepParams = stepParameterCache.computeIfAbsent(stepId, id -> new ConcurrentHashMap<>());
    
    return stepParams.computeIfAbsent(paramCode, code -> 
        stepParameterRepository.findByStepIdAndParamCode(stepId, code));
}
```

## 使用场景

### 场景1：纯新增
Excel中的所有参数在数据库中都不存在：
- 所有参数都会被创建为新参数
- 参数顺序按照数据库中已有参数数量计算

### 场景2：纯更新
Excel中的所有参数在数据库中都已存在：
- 所有参数都会被更新
- 保持原有的参数顺序和ID

### 场景3：混合操作
Excel中部分参数存在，部分不存在：
- 存在的参数被更新
- 不存在的参数被创建
- 新创建的参数顺序基于数据库中已有参数数量

## 测试验证

### 测试用例1：更新现有参数
```java
@Test
public void testUpdateExistingParameter() {
    // 模拟PARAM001已存在，PARAM002不存在
    when(stepParameterRepository.findByStepIdAndParamCode(anyString(), "PARAM001")).thenReturn(existingParam);
    when(stepParameterRepository.findByStepIdAndParamCode(anyString(), "PARAM002")).thenReturn(null);
    
    // 验证第一个参数是更新的现有参数
    assertEquals(updatedParam.getParamId(), "existing-param-id"); // 保持原有ID
    assertEquals(updatedParam.getParamName(), "温度"); // 更新为新名称
    assertEquals(updatedParam.getParamOrder(), Integer.valueOf(2)); // 保持原有顺序
}
```

### 测试用例2：创建新参数
```java
@Test
public void testCreateNewParameters() {
    // 模拟所有参数都不存在
    when(stepParameterRepository.findByStepIdAndParamCode(anyString(), anyString())).thenReturn(null);
    
    // 验证参数顺序从数据库已有数量开始
    assertEquals(param1.getParamOrder(), Integer.valueOf(4)); // 数据库已有3个，新的从4开始
}
```

## 优势

1. **数据完整性**：避免重复创建相同paramCode的参数
2. **灵活性**：支持新增和更新混合操作
3. **性能优化**：使用缓存减少重复查询
4. **向后兼容**：不影响现有的纯新增场景

## 注意事项

1. 参数的唯一性基于`stepId + paramCode`组合
2. 更新操作不会改变参数的创建时间和创建人
3. 建议在导入前备份重要数据
4. 参数类型的更新需要确保新类型在PARAM_TYPE字典中存在
