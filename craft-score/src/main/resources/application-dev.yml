spring:
  datasource:
    driver-class-name: dm.jdbc.driver.DmDriver
    url: ${SCORE_DM_URL:jdbc:dm://***************:31275/bwty-craft-score-dev?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8}
    #数据库的使用用户
    username: ${SCORE_DM_USERNAME:SYSDBA}
    #实际使用数据库的密码
    password: ${SCORE_DM_PWD:SYSDBA_dm001}
    hikari:
      #最大连接数
      maximum-pool-size: 500
      #最大超时时间
      connection-timeout: 600000
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.DmDialect
    show-sql: true
    open-in-view: true
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
      #     ddl-auto: none
      ddl-auto: none
      # 此配置做了oracle配置，必须
    database-platform: org.hibernate.dialect.DmDialect
  flyway:
    # 默认不启用，true 为启用
    enabled: false
    baseline-on-migrate: true
    # baseline-version：产生的原因是兼容已经有版本发布的项目（即数据库中原本就存在一些表），要满足 3 个条件：
    # 1. baseline-on-migrate: true
    # 2. 数据库中已经存在其他表。
    # 3. flyway_schema_history 表不存在。
    # 当以上 3 个条件成立时，设置的 baseline-version 的值是多少，那么这个版本及之前版本的脚本都不会被执行。
    # 并且，flyway_schema_history 表中会多出第一条字段 script 为 << Flyway Baseline >> 的数据记录。
    # 不需要 baseline-version 的话可以注释掉。需要的话比如配置为：baseline-version: 2020.12.11
    baseline-version:
    # 禁用 placeholder replacement，否则 sql 脚本中不能写 ${} 这样的字符。
    placeholder-replacement: false
    # flyway脚本命名规则为：V<VERSION>__<NAME>.sql (with <VERSION> an underscore-separated version, such as ‘1’ or ‘2_1’)
    # flyway在spring boot中默认配置位置为：classpath:db/migration
    locations:
      - classpath:db/migration/dev
  data:
    redis:
      cluster:
        nodes: ${RUOYI_REDIS_HOST:***********:6379,***********:6379,***********:6379}
      #      host: ${RUOYI_REDIS_HOST:***************}
      #      port: ${RUOYI_REDIS_PORT:6379}
      password: ${RUOYI_REDIS_PASSWORD:KyAbse2SvEu622da}
      database: ${RUOYI_REDIS_DATABASE:5}


jwt:
  secret: ${JWT_SECRET:""}
service:
  provider:
    name: ${RUOYI_SYSTEM:ruoyi-system}
    url: ${RUOYI_SYSTEM_URL:http://***************:32169}
    file:
      name: ${RUOYI_FILE:127.0.0.1}
      url: ${RUOYI_FILE_URL:9300}

casdoor:
  endpoint: ${CASDOOR_ENDPOINT:http://***************:31968/api/casdoor/}
  client-id: ${CASDOOR_CLIENT_ID:501a2c41c780079b8e1c}
  client-secret: ${CASDOOR_CLIENT_SECRET:dc7b9e039882d84049bcf2d49a051416d1be1f30}
  organization-name: ${CASDOOR_ORGANIZATION:bici}
  application-name: ${CASDOOR_APPLICATION:data-platform}