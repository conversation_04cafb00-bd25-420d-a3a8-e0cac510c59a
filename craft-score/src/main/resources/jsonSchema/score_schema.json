{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Parameter <PERSON><PERSON><PERSON><PERSON>", "description": "Schema for defining parameter thresholds with below/above ranges", "type": "array", "items": {"type": "object", "required": ["param"], "properties": {"param": {"type": "string", "description": "Parameter name identifier"}, "below": {"type": "object", "properties": {"below10": {"type": "number"}, "below10to20": {"type": "number"}, "below20to50": {"type": "number"}, "below50": {"type": "number"}}, "additionalProperties": false}, "above": {"type": "object", "properties": {"above10": {"type": "number"}, "above10to20": {"type": "number"}, "above20to50": {"type": "number"}, "above50": {"type": "number"}}, "additionalProperties": false}}, "additionalProperties": false}}