package com.bzlj.score.entity;

import com.bzlj.base.generator.SnowflakeId;
import com.bzlj.score.converter.RuleDetailConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
@Entity
@Table(name = "process_score")
@EntityListeners(AuditingEntityListener.class)
public class ProcessScore {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @Column(name = "process_id", nullable = false)
    private String processId;

    @Column(name = "step_id", nullable = false)
    private String stepId;

    @Column(name = "step_name")
    private String stepName;

    @Column(name = "create_user_id", nullable = false)
    @CreatedBy
    private String createUserId;


    @Column(name = "update_user_id", nullable = false)
    @LastModifiedBy
    private String updateUserId;

    @Column(name = "create_at", nullable = false)
    @CreatedDate
    private LocalDateTime createAt;

    @Column(name = "update_at", nullable = false)
    @LastModifiedDate
    private LocalDateTime updateAt;

    @Column(name = "enable_flag", nullable = false)
    private Boolean enableFlag = false;

    @Size(max = 50)
    @Column(name = "param_type", nullable = false, length = 50)
    private String paramType;

    /**
     * 编码
     */
    @Column(name = "score_code",length = 50)
    private String scoreCode;

    /**
     * 逻辑删除 0 未删除 1删除
     */
    @Column(name = "deleted")
    private Boolean deleted = false;

    /**
     * 版本
     */
    @Column(name = "version",length = 50)
    private Integer version;

    /**
     * 是否最新版本
     */
    @Column(name = "latest")
    private Boolean latest;

    /**
     * 上个版本id
     */
    @Column(name = "parent_id")
    private String parentId;

    @Column(name = "rule_detail")
    @Lob // 使用 Lob 类型存储大文本
    @Convert(converter = RuleDetailConverter.class)
    private List<Map<String, Object>> ruleDetail;

}