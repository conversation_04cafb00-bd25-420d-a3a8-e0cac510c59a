package com.bzlj.score.controller;

import com.bzlj.base.response.UnifyResponse;
import com.bzlj.base.result.DataResult;
import com.bzlj.score.dto.DeviationDegreeConfigDTO;
import com.bzlj.score.dto.ProcessScoreDTO;
import com.bzlj.score.service.IProcessScoreService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工艺评分
 * <AUTHOR>
 * @description: 工艺评分
 * @date 2025-03-10 13:35
 */
@RestController
@RequestMapping("/processScore")
@RequiredArgsConstructor
public class ProcessScoreController {
    @Autowired
    private final IProcessScoreService processScoreService;


    /**
     * 保存工序打分
     * @param processScoreDTO 评分记录
     * @return
     */
    @PostMapping("/saveProcessScore")
    public UnifyResponse<ProcessScoreDTO> saveProcessScore(@RequestBody ProcessScoreDTO processScoreDTO) {
        return UnifyResponse.success(processScoreService.saveProcessScore(processScoreDTO));
    }

    /**
     * 删除工序打分表
     * @param processScoreId
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public UnifyResponse delete(@PathVariable("id") String processScoreId) {
        processScoreService.logicDelete(processScoreId);
        return UnifyResponse.success();
    }


    /**
     * 根据工序查询评分记录
     * @param processId
     * @return
     */
    @GetMapping("/findProcessId")
    public UnifyResponse<DataResult> findProcessId(@RequestParam(name = "processId") String processId) {
        return UnifyResponse.success(processScoreService.findProcessId(processId));
    }

    /**
     * 修改评分状态
     * @param id 评分记录id
     * @return
     */
    @PutMapping("/modifyScoreState/{id}")
    public UnifyResponse<ProcessScoreDTO> modifyScoreState(@PathVariable(name = "id") String id,@RequestBody ProcessScoreDTO processScoreDTO) {
        return UnifyResponse.success(processScoreService.modifyScoreState(id,  processScoreDTO));
    }

    /**
     * 查询偏离程度配置
     * @return
     */
    @GetMapping("/findDeviationDegreeConfig")
    public UnifyResponse<List<DeviationDegreeConfigDTO>> findDeviationDegreeConfig() {
        return UnifyResponse.success(processScoreService.findDeviationDegreeConfig());
    }

    /**
     * 根据id查询评分记录
     * @param processScoreId
     * @return
     */
    @GetMapping("/findProcessScoreById/{processScoreId}")
    public UnifyResponse<ProcessScoreDTO> findProcessScoreById(@PathVariable(name = "processScoreId") String processScoreId) {
        return UnifyResponse.success(processScoreService.findProcessScoreById(processScoreId));
    }
}
