package com.bzlj.score.exception;


/**
 * <AUTHOR>
 * @description:
 * @date 2024-12-02 15:18
 */
public enum BiciErrorData {
    SCORE_DETAIL_CANNOT_NULL(001,"打分明细不能为空"),
    SCORE_DETAIL_ANALYSIS_FAIL(002,"打分明细解析失败"),
    SCORE_DETAIL_FORMAT_ERROR(003,"打分明细格式错误"),
    DATA_DELETE_FAIL(004,"数据不存在，删除失败"),
    DATA_NOT_EXIST(005,"数据不存在"),
    ;
    /**
     * 标记此异常来自哪个服务以及哪个模块: 服务名_模块_
     */
    public final static String PREFIX = "Craft_Score";

    final String code;
    final String value;

    public String getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }

    BiciErrorData(int code, String value) {
        this.code = PREFIX + String.format("%04d", code);
        this.value = value;
    }

    public RuntimeException buildException(Object... args){
        String msg = String.format(this.getValue(),args);

        return new RuntimeException(String.format("%s",msg));
    }
}
