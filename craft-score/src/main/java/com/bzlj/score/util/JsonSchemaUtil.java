package com.bzlj.score.util;

import com.networknt.schema.*;
import com.networknt.schema.serialization.JsonNodeReader;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Set;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-05-13 16:48
 */
@Slf4j
public class JsonSchemaUtil {

    @SneakyThrows
    public static Set<ValidationMessage> checkBySchema(String json, String schemaPath) {
        File jsonFile = getJsonSchemaFile(schemaPath);
        String schemaData = FileUtils.readFileToString(jsonFile, "UTF-8");
        JsonSchemaFactory factory = JsonSchemaFactory.getInstance(SpecVersion.VersionFlag.V202012,
                builder -> builder.jsonNodeReader(JsonNodeReader.builder().locationAware().build()));

        SchemaValidatorsConfig config = SchemaValidatorsConfig.builder().build();

        JsonSchema jsonSchema = factory.getSchema(schemaData, InputFormat.JSON, config);
        return jsonSchema.validate(json, InputFormat.JSON, executionContext -> {
            executionContext.getExecutionConfig().setFormatAssertionsEnabled(true);
        });
    }

    static File getJsonSchemaFile(String path){
        ClassLoader classLoader = JsonSchemaUtil.class.getClassLoader();
        return new File(classLoader.getResource(path).getFile());
    }
}
