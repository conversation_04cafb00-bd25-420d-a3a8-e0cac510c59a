package com.bzlj.score.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.score.entity.ProcessScore;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProcessScoreRepository extends BaseRepository<ProcessScore, String> {
    List<ProcessScore> findByStepIdInAndDeletedOrderByUpdateAtDesc(List<String> stepIds, boolean deleted);

//    @Modifying
//    @Query("UPDATE ProcessScore e SET e.latest = :latest WHERE e.id = :id")
//    void updateLatestById(@Param("id") String id, @Param("latest") boolean latest);
//
//    @Modifying
//    @Query("UPDATE ProcessScore e SET e.enableFlag = :enableFlag WHERE e.id = :id")
//    void updateEnableFlagById(@Param("id") String id, @Param("enableFlag") boolean enableFlag);
//
//    @Modifying
//    @Query("UPDATE ProcessScore e SET e.deleted = :deleted WHERE e.id = :id")
//    void updateDeletedById(@Param("id") String id, @Param("deleted") boolean deleted);
}