package com.bzlj.score.dto;

import jakarta.persistence.Column;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class ProcessScoreDTO {
    private String id;

    private String processId;

    private String stepId;

    private String stepName;

    private String createUserId;

    private String createUserName;

    private String updateUserId;

    private String updateUserName;

    private LocalDateTime createAt;

    private LocalDateTime updateAt;

    private Boolean enableFlag = false;

    private String paramType;

    /**
     * 编码
     */
    private String scoreCode;

    /**
     * 版本
     */
    @Column(name = "version",length = 50)
    private Integer version;

    /**
     * 是否最新版本
     */
    @Column(name = "latest")
    private Boolean latest;

    /**
     * 上个版本id
     */
    @Column(name = "parent_id")
    private String parentId;

    private List<Map<String, Object>> ruleDetail;

}