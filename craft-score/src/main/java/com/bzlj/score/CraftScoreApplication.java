package com.bzlj.score;

import com.bici.common.security.handler.GlobalExceptionHandler;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@SpringBootApplication(exclude = {QuartzAutoConfiguration.class})
@EnableFeignClients(value = {"com.bzlj.score.*","com.bici.system.api"})
@EnableWebSocket
@EnableJpaAuditing
@ComponentScan(basePackages = {"com.bzlj.*", "com.bici.*"}, excludeFilters = {@ComponentScan.Filter(
        type = FilterType.ASSIGNABLE_TYPE,
        classes = GlobalExceptionHandler.class
)})
public class CraftScoreApplication {
    public static void main(String[] args) {
        SpringApplication.run(CraftScoreApplication.class, args);
    }
}