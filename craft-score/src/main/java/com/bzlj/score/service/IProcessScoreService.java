package com.bzlj.score.service;

import com.bzlj.base.result.DataResult;
import com.bzlj.base.service.IBaseService;
import com.bzlj.score.dto.DeviationDegreeConfigDTO;
import com.bzlj.score.dto.ProcessScoreDTO;
import com.bzlj.score.entity.ProcessScore;

import java.util.List;

/**
 * 工序评分
 * <AUTHOR>
 * @description:
 * @date 2025-04-21 14:39
 */
public interface IProcessScoreService extends IBaseService<ProcessScore, ProcessScoreDTO, String> {


    /**
     * 保存工序打分
     * @param processScoreDTO
     * @return
     */
    ProcessScoreDTO saveProcessScore(ProcessScoreDTO processScoreDTO);

    /**
     * 根据工序查询评分记录
     * @param processId
     * @return
     */
    DataResult findProcessId(String processId);

    /**
     * 修改启用禁用状态
     * @param id
     * @param processScoreDTO
     * @return
     */
    ProcessScoreDTO modifyScoreState(String id,ProcessScoreDTO processScoreDTO);

    /**
     * 查询偏离程度配置
     * @return
     */
    List<DeviationDegreeConfigDTO> findDeviationDegreeConfig();


    /**
     * 逻辑删除
     * @param processScoreId
     */
    void logicDelete(String processScoreId);

    /**
     * 根据id查询
     * @param processScoreId
     * @return
     */
    ProcessScoreDTO findProcessScoreById(String processScoreId);
}
