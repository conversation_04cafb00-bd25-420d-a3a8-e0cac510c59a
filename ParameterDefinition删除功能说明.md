# ParameterDefinition删除功能说明

## 功能概述

在删除执行工步（WorkStep）前，系统会自动删除与该工步关联的参数定义（ParameterDefinition）对象及其相关数据，确保数据的完整性和一致性。

## 问题背景

原来的实现中，`deleteWorkStep`方法只删除了WorkStep对象，但没有处理与之关联的ParameterDefinition对象，这可能导致：

1. 数据库中存在孤立的参数定义记录
2. 数据不一致性问题
3. 潜在的外键约束问题

## 解决方案

### 1. 添加逻辑删除字段

为`ParameterDefinition`实体添加`deleted`字段，支持逻辑删除：

```java
/**
 * 逻辑删除 false 未删除 true 删除
 */
@Column(name = "deleted")
private Boolean deleted = false;
```

### 2. 扩展Repository方法

在`ParameterDefinitionRepository`中添加支持删除状态过滤的查询方法：

```java
/**
 * 根据工步ID列表和参数类型查询参数定义（支持删除状态过滤）
 */
List<ParameterDefinition> findByWorkStepWorkStepIdInAndParamType_ItemCodeInAndDeleted(
    List<String> workStepIds, List<String> paramType, Boolean deleted);

/**
 * 根据工步ID列表查询参数定义（未删除的）
 */
List<ParameterDefinition> findByWorkStepWorkStepIdInAndDeleted(
    List<String> workStepIds, Boolean deleted);

/**
 * 根据工步ID查询参数定义（未删除的）
 */
List<ParameterDefinition> findByWorkStepWorkStepIdAndDeleted(
    String workStepId, Boolean deleted);
```

### 3. 扩展相关Repository

为相关实体添加查询方法：

- `ParameterDefinitionExtendRepository`: 根据参数定义ID查询扩展信息
- `ProcessParameterRepository`: 根据参数定义ID查询工艺参数

### 4. 修改删除逻辑

在`SurveillanceServiceImpl`中修改`deleteWorkStep`方法：

```java
private void deleteWorkStep(String taskId) {
    List<WorkStep> workSteps = workStepRepository.findByTaskTaskIdAndDeleted(taskId, false);
    if (!CollectionUtils.isEmpty(workSteps)) {
        // 先删除参数定义
        List<String> workStepIds = workSteps.stream()
                .map(WorkStep::getWorkStepId)
                .collect(Collectors.toList());
        deleteParameterDefinitions(workStepIds);
        
        // 再删除工步
        workSteps.forEach(workStep -> workStep.setDeleted(true));
        workStepRepository.saveAll(workSteps);
    }
}
```

## 实现细节

### 1. 参数定义删除流程

```java
private void deleteParameterDefinitions(List<String> workStepIds) {
    // 1. 查询需要删除的参数定义
    List<ParameterDefinition> parameterDefinitions = 
        parameterDefinitionRepository.findByWorkStepWorkStepIdInAndDeleted(workStepIds, false);
    
    // 2. 获取参数定义ID列表
    List<String> paramDefIds = parameterDefinitions.stream()
            .map(ParameterDefinition::getParamDefId)
            .collect(Collectors.toList());

    // 3. 删除参数定义扩展信息
    deleteParameterDefinitionExtends(paramDefIds);
    
    // 4. 删除工艺参数
    deleteProcessParameters(paramDefIds);
    
    // 5. 删除参数定义
    parameterDefinitions.forEach(paramDef -> paramDef.setDeleted(true));
    parameterDefinitionRepository.saveAll(parameterDefinitions);
}
```

### 2. 关联数据删除

#### 参数定义扩展信息删除
```java
private void deleteParameterDefinitionExtends(List<String> paramDefIds) {
    List<ParameterDefinitionExtend> extends = 
        parameterDefinitionExtendRepository.findByParamDefIdInAndDeleted(paramDefIds, false);
    
    if (!CollectionUtils.isEmpty(extends)) {
        extends.forEach(extend -> extend.setDeleted(true));
        parameterDefinitionExtendRepository.saveAll(extends);
    }
}
```

#### 工艺参数删除
```java
private void deleteProcessParameters(List<String> paramDefIds) {
    List<ProcessParameter> processParameters = 
        processParameterRepository.findByParamDefParamDefIdIn(paramDefIds);
    
    if (!CollectionUtils.isEmpty(processParameters)) {
        // ProcessParameter没有deleted字段，需要物理删除
        processParameterRepository.deleteAll(processParameters);
    }
}
```

## 兼容性处理

### 1. 废弃旧方法

将原有的Repository方法标记为`@Deprecated`，并提供替代方法：

```java
/**
 * @deprecated 使用 findByWorkStepWorkStepIdInAndParamType_ItemCodeInAndDeleted 替代
 */
@Deprecated
List<ParameterDefinition> findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(
    List<String> workStepIds, List<String> paramType);
```

### 2. 更新现有调用

将所有使用旧方法的地方更新为新方法，确保只查询未删除的记录：

```java
// 旧方法
List<ParameterDefinition> parameterDefinitions = 
    parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeIn(
        workStepIds, Lists.newArrayList(ParamType.CURVE.getCode()));

// 新方法
List<ParameterDefinition> parameterDefinitions = 
    parameterDefinitionRepository.findByWorkStepWorkStepIdInAndParamType_ItemCodeInAndDeleted(
        workStepIds, Lists.newArrayList(ParamType.CURVE.getCode()), false);
```

## 执行顺序

删除操作的执行顺序非常重要，必须按照以下顺序进行：

1. **删除参数定义扩展信息** - 避免外键约束问题
2. **删除工艺参数** - 清理引用参数定义的数据
3. **删除参数定义** - 标记为逻辑删除
4. **删除工步** - 最后删除主体对象

## 日志记录

系统会记录删除操作的详细信息：

```
成功删除 {count} 个参数定义及其关联数据
删除了 {count} 个参数定义扩展信息
删除了 {count} 个工艺参数
```

## 注意事项

1. **事务性**: 所有删除操作都在同一个事务中执行，确保数据一致性
2. **逻辑删除**: ParameterDefinition和ParameterDefinitionExtend使用逻辑删除，便于数据恢复和审计
3. **物理删除**: ProcessParameter使用物理删除，因为该实体没有deleted字段
4. **性能优化**: 使用批量查询和批量保存，避免N+1查询问题
5. **向后兼容**: 保留旧方法但标记为废弃，给系统升级留出缓冲时间

## 数据库迁移

如果ParameterDefinition表中没有deleted字段，需要执行以下SQL：

```sql
ALTER TABLE parameter_definition ADD COLUMN deleted BOOLEAN DEFAULT FALSE;
```

## 测试建议

1. **单元测试**: 测试删除方法的各个分支逻辑
2. **集成测试**: 测试完整的删除流程
3. **性能测试**: 验证批量删除的性能表现
4. **数据一致性测试**: 确保删除后数据库状态正确
