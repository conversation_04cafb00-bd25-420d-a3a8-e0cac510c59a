#!/bin/bash
# 获取当前目录路径
current_dir=$(pwd)
backed_path='./'

# 设置默认的最上层目录为当前目录
top_dir=$current_dir

default_command='mvn'

log_path='${HOME/log.txt}'

deploy_result="SUCCESS"

# 如果有传入参数，则使用第一个参数作为最上层目录
if [ $# -ge 1 ]; then
  default_command="$1"
fi

if [ $# -ge 2 ]; then
  log_path="$2"
fi

function build() {
  dir=$(pwd)
  # 判断是否存在 pom.xml 文件
  if [ -e "pom.xml" ]; then
    echo "Found pom.xml in $dir"
    # 执行 mvn clean install 任务
    $default_command clean deploy -Dmaven.test.skip=true -DsendCredentialsOverHttp=true
    if [ $? -ne 0 ]; then
      deploy_result="FAILED"  # 如果失败，设置为 FAILED
    fi
    echo $deploy_result > deploy_result.txt
  else
    # 如果不存在 pom.xml 文件，则跳过该目录
    echo "No pom.xml found in $dir"
  fi
}
# if specified dir contains pom.xml ,build in it directly
if [[ -e "${top_dir}/pom.xml" ]];
then
    build
    cd $backed_path
    if [[ -e "./pom.xml" ]];
    then
      build
    fi
    exit 0
fi

