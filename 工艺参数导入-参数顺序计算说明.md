# 工艺参数导入 - 参数顺序计算说明

## 修改内容

### 问题描述
原来的实现中，参数顺序（paramOrder）是从Excel表格中的数据开始计算的，这会导致新导入的参数与数据库中已有参数的顺序冲突。

### 解决方案
修改参数顺序计算逻辑，基于数据库中已有的参数数量来计算新参数的顺序。

## 实现细节

### 1. 新增Repository方法
在 `StepParameterRepository` 中添加了计数方法：

```java
/**
 * 统计指定工步下的参数数量
 * @param stepId 工步ID
 * @return 参数数量
 */
int countByStepId(String stepId);
```

### 2. 修改参数顺序计算逻辑
在 `StepParameterExcelImporter.parseExcel()` 方法中：

```java
// 计算参数顺序
String stepId = processStep.getId();
int currentCount = stepParamCountMap.computeIfAbsent(stepId, id -> {
    // 查询数据库中该工步已有的参数数量
    return stepParameterRepository.countByStepId(id);
});
int paramOrder = currentCount + 1;
stepParamCountMap.put(stepId, paramOrder);
```

### 3. 缓存机制
使用 `stepParamCountMap` 缓存每个工步的参数计数，避免重复查询数据库：
- 第一次访问某个工步时，查询数据库获取已有参数数量
- 后续同一工步的参数直接使用缓存值并递增

## 示例场景

### 场景1：工步已有参数
- 数据库中工步A已有3个参数（paramOrder: 1, 2, 3）
- Excel中导入2个新参数
- 新参数的paramOrder将是：4, 5

### 场景2：新工步
- 数据库中工步B没有参数
- Excel中导入3个参数
- 新参数的paramOrder将是：1, 2, 3

### 场景3：多个工步混合导入
- 工步A已有2个参数，工步B已有1个参数
- Excel中包含：工步A的2个参数，工步B的1个参数
- 结果：
  - 工步A的新参数：paramOrder = 3, 4
  - 工步B的新参数：paramOrder = 2

## 测试验证

### 测试用例1：基础功能测试
```java
@Test
public void testParseExcel() {
    // 模拟数据库中已有3个参数
    when(stepParameterRepository.countByStepId(anyString())).thenReturn(3);
    
    // 验证新参数从4开始编号
    assertEquals(param1.getParamOrder(), Integer.valueOf(4));
    assertEquals(param2.getParamOrder(), Integer.valueOf(5));
}
```

### 测试用例2：参数顺序计算测试
```java
@Test
public void testParamOrderCalculation() {
    // 模拟数据库中已有5个参数
    when(stepParameterRepository.countByStepId(anyString())).thenReturn(5);
    
    // 验证参数顺序从6开始
    assertEquals(result.get(0).getParamOrder(), Integer.valueOf(6));
    assertEquals(result.get(1).getParamOrder(), Integer.valueOf(7));
}
```

## 优势

1. **数据一致性**：确保新导入参数的顺序不会与已有参数冲突
2. **性能优化**：使用计数查询而不是查询完整列表，提高性能
3. **缓存机制**：同一工步的多个参数只查询一次数据库
4. **扩展性**：支持多个工步的参数混合导入

## 注意事项

1. 参数顺序是基于工步（ProcessStep）级别计算的
2. 每个工步的参数顺序独立计算
3. 导入过程中如果同一工步有多个参数，会按Excel中的顺序依次递增
4. 建议在导入前确保PARAM_TYPE字典数据完整
