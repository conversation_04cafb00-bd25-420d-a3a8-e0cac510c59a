package com.bzlj.base.response;

import com.bzlj.base.enums.UnifyResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一返回对象
 *
 * <AUTHOR>
 * @date 2024/6/19 17:33
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnifyResponse<T> {
    /**
     * 自定义状态码
     */
    private Integer code;
    /**
     * 提示内容，如果接口出错，则存放异常信息
     */
    private String msg;
    /**
     * 返回数据体
     */
    private T data;
    /**
     * 接口成功检测-默认成功
     */
    private Boolean success = true;


    public UnifyResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> UnifyResponse<T> success() {
        return new UnifyResponse<>(UnifyResponseCodeEnum.SUCCESS.getCode(), UnifyResponseCodeEnum.SUCCESS.getMsg(), null);
    }

    public static <T> UnifyResponse<T> success(T data) {
        return new UnifyResponse<>(UnifyResponseCodeEnum.SUCCESS.getCode(), UnifyResponseCodeEnum.SUCCESS.getMsg(), data);
    }

    public static <T> UnifyResponse<T> failed(String msg) {
        return new UnifyResponse<>(UnifyResponseCodeEnum.FAILED.getCode(), msg, null,false);
    }

    public static <T> UnifyResponse<T> failed(String msg, T data) {
        return new UnifyResponse<>(UnifyResponseCodeEnum.FAILED.getCode(), msg, data,false);
    }

    public static <T> UnifyResponse<T> failed(UnifyResponseCodeEnum errorCode) {
        return new UnifyResponse<>(errorCode.getCode(), errorCode.getMsg(), null,false);
    }

    public static <T> UnifyResponse<T> failed(UnifyResponseCodeEnum errorCode, T data) {
        return new UnifyResponse<>(errorCode.getCode(), errorCode.getMsg(), data,false);
    }

}
