package com.bzlj.base.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * APi返回的状态码表
 *
 * <AUTHOR>
 * @date 2024/6/19 17:38
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@RequiredArgsConstructor
@Getter
public enum UnifyResponseCodeEnum {
    SUCCESS(200, "请求成功"),
    FAILED(500, "操作失败"),
    TOKEN_FAILED(401, "token失效"),

    NONE(99999, "无");
    private final int code;
    private final String msg;
}
