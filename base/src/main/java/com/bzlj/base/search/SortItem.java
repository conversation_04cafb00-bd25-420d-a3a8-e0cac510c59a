package com.bzlj.base.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * 排序
 * <AUTHOR>
 * @description:
 * @date 2025-03-06 16:03
 */
@Data
@EqualsAndHashCode
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SortItem {

    public static final String ASC_ORDER_NAME = "asc";
    public static final String DESC_ORDER_NAME = "desc";

    /**
     * 排序字段
     */
    @JsonProperty("fieldName")
    private String fieldName;

    /**
     * 排序规则
     */
    @JsonProperty("sortOrder")
    private String sortOrder;
}
