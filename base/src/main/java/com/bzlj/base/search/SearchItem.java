package com.bzlj.base.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * 具体查询参数
 * <AUTHOR>
 * @description:
 * @date 2025-03-06 16:07
 */
@EqualsAndHashCode
@ToString
@NoArgsConstructor
public class SearchItem {


    /**
     * 查询字段
     */
    @JsonProperty("fieldName")
    public String fieldName;

    /**
     * 查询值
     */
    @JsonProperty("value")
    public Object value;

    /**
     * 查询值1
     */
    @JsonProperty("value1")
    public Object value1;

    /**
     * 查询操作符
     */
    @JsonProperty("operator")
    public Operator operator;

    @Getter
    @AllArgsConstructor
    public enum Operator {
        EQ(1, "等于"),
        NEQ(2, "不等于"),
        LIKE(3, "模糊查询"),
        GT(4, "大于"),
        LT(5, "小于"),
        GTE(6, "大于等于"),
        LTE(7, "小于等于"),
        BTWN(8, "范围查询"),
        IN(9, "多值查询"),
        ISNULL(10, "空值查询"),
        NOTNULL(11, "非空查询"),
        NOTIN(13, "非集合查询"),
        ;

        /**
         * code编码
         */
        final String code;
        /**
         * 获取名称
         */
        final String value;

        Operator(int code, String value) {
            this.code = String.format("%04d", code);
            this.value = value;
        }

    }

    public SearchItem(String fieldName, Object value,Object value1, Operator operator) {
        this.fieldName = fieldName;
        this.value = value;
        this.operator = operator;
        this.value1 = value1;
    }
}
