package com.bzlj.base.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询参数
 * <AUTHOR>
 * @description:
 * @date 2025-03-06 16:06
 */
@Data
@EqualsAndHashCode
@ToString
@Builder
public class SearchItems {

    /**
     * 查询参数集合
     */
    @Singular("item")
    @JsonProperty("items")
    private List<SearchItem> items = new ArrayList<>();

    /**
     * 默认AND操作符
     */
    @Builder.Default
    @JsonProperty("operator")
    private BooleanOperator operator = BooleanOperator.AND;

    public SearchItems() {
        this.operator = BooleanOperator.AND;
    }

    public SearchItems(BooleanOperator operator) {
        this.operator = operator;
    }

    public enum BooleanOperator {
        AND, OR
    }

    public SearchItems(List<SearchItem> items, BooleanOperator operator) {
        this.items = new ArrayList<>(items);
        this.operator = operator;
    }

    public void addItem(SearchItem item) {
        items.add(item);
    }
}
