package com.bzlj.base.config;

import com.bici.common.security.service.TokenService;
import com.bici.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class CustomAuditorAware implements AuditorAware<String> {

    @Autowired
    private TokenService tokenService;


    @Override
    public Optional<String> getCurrentAuditor() {
        try {
            LoginUser loginUser = tokenService.getLoginUser();
            if (loginUser == null) {
                return Optional.of("admin");
            }
            return Optional.of(loginUser.getUsername());
        }catch (Exception e){
            return Optional.of("admin");
        }


    }

}
