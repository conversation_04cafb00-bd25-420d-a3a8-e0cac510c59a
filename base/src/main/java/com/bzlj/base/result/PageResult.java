package com.bzlj.base.result;

import com.bzlj.base.search.SearchCondition;
import com.google.common.collect.Lists;
import lombok.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-06 16:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> {

    @Singular("contentSingle")
    private List<T> content;

    /**
     * 当前页数
     */
    private int pageCurrent;
    /**
     * 每页条数
     */
    private int pageSize;
    /**
     * 总条数
     */
    private long total;
    /**
     * 总页数
     */
    private int totalPages;

    public Page<T> convertToPage() {
        return new PageImpl<>(content, PageRequest.of(pageCurrent, pageSize), total);
    }

    public static <T> PageResult<T> fromPage(Page<T> page) {
        PageResult<T> pageResult = PageResult.<T>builder()
                .pageCurrent(page.getNumber())
                .pageSize(page.getSize())
                .total(page.getTotalElements())
                .totalPages(page.getTotalPages())
                .build();
        //build方法的content List为unModifyList
        if (CollectionUtils.isEmpty(page.getContent())) {
            pageResult.setContent(Lists.newArrayList());
        } else {
            pageResult.setContent(Lists.newArrayList(page.getContent()));
        }
        return pageResult;
    }

    public static <T> PageResult<T> of(SearchCondition condition, long total, List<T> content) {
        PageResult<T> pageResult = PageResult.<T>builder()
                .pageCurrent(condition.getPageCurrent())
                .pageSize(condition.getPageSize())
                .total(total)
                .totalPages(total == 0L ? 0 : (int) Math.ceil((double) total / condition.getPageSize()))
                .build();
        //build方法的content List为unModifyList
        if (CollectionUtils.isEmpty(content)) {
            pageResult.setContent(Lists.newArrayList());
        } else {
            pageResult.setContent(Lists.newArrayList(content));
        }
        return pageResult;
    }
}
