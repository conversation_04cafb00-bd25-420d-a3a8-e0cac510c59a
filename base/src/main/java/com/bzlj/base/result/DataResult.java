package com.bzlj.base.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 链式数据结果
 * <AUTHOR>
 * @description:
 * @date 2025-03-13 16:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataResult {

    /**
     * 字段的数据
     */
    private List<List<Object>> list;
    /**
     * 分页信息
     */
    private int pageCurrent;
    /**
     * 每页条数
     */
    private int pageSize;
    /**
     * 总条数
     */
    private long total;
    /**
     * 总页数
     */
    private int totalPages;

    /**
     * 分页名称集合
     */
    private List<String> tabNames;
}
