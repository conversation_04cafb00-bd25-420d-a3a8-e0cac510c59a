package com.bzlj.base.querydsl.util;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathBuilder;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-03-12 13:48
 */
public class PathUtil {
    public static Path<?> resolvePath(PathBuilder<?> pathBuilder, String fieldName) {
        String[] parts = fieldName.split("\\.");
        PathBuilder<Object> path = pathBuilder.get(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            path = path.get(parts[i]);
        }
        return path;
    }
}
