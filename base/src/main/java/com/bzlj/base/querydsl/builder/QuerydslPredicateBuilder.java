package com.bzlj.base.querydsl.builder;

import com.bzlj.base.querydsl.util.PathUtil;
import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Ops;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanOperation;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.PathBuilder;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class QuerydslPredicateBuilder<T> {

    private final Class<T> entityType;
    private final SearchCondition condition;
    private final String rootAlias;

    public QuerydslPredicateBuilder(Class<T> entityType, SearchCondition condition, String rootAlias) {
        this.entityType = entityType;
        this.condition = condition;
        this.rootAlias = rootAlias;
    }

    public Predicate build() {
        BooleanBuilder predicate = new BooleanBuilder();
        SearchItems searchItems = condition.getSearchItems();
        if(ObjectUtils.isEmpty(searchItems)) return predicate;
        List<SearchItem> items = searchItems.getItems();
        if(CollectionUtils.isEmpty(items)) return predicate;
        SearchItems.BooleanOperator operator = searchItems.getOperator();
        items.forEach(item -> {
            if(SearchItems.BooleanOperator.AND.equals(operator)){
                predicate.and(buildBooleanOperation(item));
            }else{
                predicate.or(buildBooleanOperation(item));
            }
        });
        return predicate;
    }

    BooleanOperation buildBooleanOperation(SearchItem item) {
        Object value = item.value;
        Object value1 = item.value1;
        String fieldName = item.fieldName;
        PathBuilder<T> rootPath = new PathBuilder<>(entityType, rootAlias);
        Path<?> path = PathUtil.resolvePath(rootPath, fieldName);
        BooleanOperation result = null;
//        if (Objects.isEmpty(value)) {
//            result = Expressions.predicate(Ops.IS_NULL, path);
//            return result;
//        }
        switch (item.operator) {
            case EQ:
                result = Expressions.predicate(Ops.EQ, path, Expressions.constant(value));
                break;
            case NEQ:
                result = Expressions.predicate(Ops.NE, path, Expressions.constant(value));
                break;
            case LIKE:
                result = Expressions.predicate(Ops.LIKE, path, Expressions.constant("%" + value + "%"));
                break;
            case GT:
                result = Expressions.predicate(Ops.GT, path, Expressions.constant(value));
                break;
            case GTE:
                result = Expressions.predicate(Ops.GOE, path, Expressions.constant(value));
                break;
            case LT:
                result = Expressions.predicate(Ops.LT, path, Expressions.constant(value));
                break;
            case LTE:
                result = Expressions.predicate(Ops.LOE, path, Expressions.constant(value));
                break;
            case BTWN:
                if(isDate((String)value)){
                    value = LocalDateTime.parse((String)value, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                if(isDate((String)value1)){
                    value1 = LocalDateTime.parse((String)value1, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                result = Expressions.predicate(Ops.BETWEEN, path, Expressions.constant(value),Expressions.constant(value1));
                break;
            case IN:
                result = Expressions.predicate(Ops.IN, path, Expressions.constant(value));
                break;
            case NOTIN:
                result = Expressions.predicate(Ops.NOT_IN, path, Expressions.constant(value));
                break;
            case ISNULL:
                result = Expressions.predicate(Ops.IS_NULL, path);
                break;
            case NOTNULL:
                result = Expressions.predicate(Ops.IS_NOT_NULL, path);
                break;
            default:
        }
        return result;
    }

    public static boolean isDate(String value) {
        String regex = "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01]) (0\\d|1\\d|2[0-3]):[0-5]\\d:[0-5]\\d$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        return matcher.matches();
    }

    /*private Path<Object> buildPath(String field) {
        PathBuilder<T> rootPath = new PathBuilder<>(entityType, rootAlias);
        String[] parts = field.split("\\.");
        PathBuilder<Object> path = rootPath.get(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            path = path.get(parts[i]);
        }
        return path;
    }*/
}