package com.bzlj.base.querydsl.sort;

import com.bzlj.base.querydsl.util.PathUtil;
import com.bzlj.base.search.SortItem;
import com.google.common.collect.Lists;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.PathBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class QuerydslSortBuilder<T> {
    private final Class<T> entityType;
    private final String rootAlias;

    public QuerydslSortBuilder(Class<T> entityType, String rootAlias) {
        this.entityType = entityType;
        this.rootAlias = rootAlias;
    }

    public List<OrderSpecifier<?>> buildSort(List<SortItem> sortItems) {
        if(CollectionUtils.isEmpty(sortItems)){
            return Lists.newArrayList();
        }
        return sortItems.stream()
                .map(this::createOrderSpecifier)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private OrderSpecifier<?> createOrderSpecifier(SortItem sortItem) {
        try {
            PathBuilder<T> rootPath = new PathBuilder<>(entityType, rootAlias);
            Expression path = PathUtil.resolvePath(rootPath, sortItem.getFieldName());
            return new OrderSpecifier<>(
                    StringUtils.equals(sortItem.getSortOrder(), SortItem.ASC_ORDER_NAME) ? Order.ASC : Order.DESC,
                    path
            );
        } catch (IllegalArgumentException e) {
            // 处理无效字段名（可选：抛出异常或忽略）
            return null;
        }
    }

    /*private Path<Object> resolveFieldPath(PathBuilder<?> pathBuilder, String fieldName) {
        String[] parts = fieldName.split("\\.");
        PathBuilder<Object> path = pathBuilder.get(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            path = path.get(parts[i]);
        }
        return path;
    }*/
}