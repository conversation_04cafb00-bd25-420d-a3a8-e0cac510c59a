package com.bzlj.base.querydsl.fetch;

import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class QuerydslJoinFetcher<T> {

    private final JPAQuery<?> query;
    private final PathBuilder<T> rootPath;
    private final String rootAlias;

    public QuerydslJoinFetcher(JPAQuery<?> query, Class<T> entityType, String rootAlias) {
        this.query = query;
        this.rootPath = new PathBuilder<>(entityType, rootAlias);
        this.rootAlias = rootAlias;
    }

    public void fetchJoins(List<String> openProps) {
        if(CollectionUtils.isEmpty(openProps)) return;
        openProps.forEach(prop -> {
            String[] paths = prop.split("\\.");
            PathBuilder<?> currentPath = rootPath;
            for (String path : paths) {
                // 动态构建 Join
                currentPath = currentPath.get(path);
                query.leftJoin(currentPath).fetchJoin();
            }
        });
    }
}