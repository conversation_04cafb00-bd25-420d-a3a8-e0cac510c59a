package com.bzlj.base.generator;


import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.Configurable;
import org.hibernate.id.IdentifierGenerator;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2023-12-06
 **/
public class IdGenerator implements IdentifierGenerator, Configurable {
    @Override
    public Object generate(SharedSessionContractImplementor sharedSessionContractImplementor, Object o) {
        try {
            return UUIDGenerator.generateUUID();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
