package com.bzlj.base.generator;
/**
 * The type Uuid generator.
 *
 * <AUTHOR>
 */
public class UUIDGenerator {

    private static volatile SnowflakeIdGenerator generator;

    /**
     * generate UUID using snowflake algorithm
     * @return UUID
     */
    public static String generateUUID() throws InterruptedException {
        if (generator == null) {
            synchronized (UUIDGenerator.class) {
                if (generator == null) {
                    init(1,1);
                }
            }
        }
        return String.valueOf(generator.nextId());
    }

    /**
     * @param
     */
    public static void init(long datacenterId, long machineId) {
        generator = new SnowflakeIdGenerator(datacenterId,machineId);
    }
}