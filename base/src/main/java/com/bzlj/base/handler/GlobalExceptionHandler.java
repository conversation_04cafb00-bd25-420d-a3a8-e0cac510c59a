package com.bzlj.base.handler;

import com.bzlj.base.response.UnifyResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 统一异常拦截
 *
 * <AUTHOR>
 * @date 2024/6/20 00:50
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@RestControllerAdvice
@Slf4j
@ConditionalOnMissingBean(GlobalExceptionHandler.class)
public class GlobalExceptionHandler {
    @ExceptionHandler(value = Exception.class)
    public UnifyResponse<Object> catchException(Exception e) {
        todoErrorLogger(e, null);
        log.error("出错了:{}", e.getMessage(), e);
        return UnifyResponse.failed(e.getMessage());
    }

    private void todoErrorLogger(Exception e, String errorMsg) {
        StringBuilder sb = new StringBuilder();
        sb.append(e.getClass().getName()).append(":");
        if (errorMsg != null) {
            sb.append(errorMsg);
        } else {
            sb.append(e.getMessage());
        }
        StackTraceElement[] elements = e.getStackTrace();
        if (elements.length > 0) {
            StackTraceElement element = elements[0];
            sb.append("##function:")
                    .append(element.getClassName())
                    .append("-")
                    .append(element.getMethodName())
                    .append("-")
                    .append(element.getLineNumber());
        }
        e.printStackTrace();
    }

}
