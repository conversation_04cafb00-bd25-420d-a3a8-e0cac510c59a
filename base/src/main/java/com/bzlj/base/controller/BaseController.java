package com.bzlj.base.controller;

import com.bzlj.base.service.IBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 基础方法
 * <AUTHOR>
 * @description:
 * @date 2025-03-07 17:36
 */
@Slf4j
@RestController
@Transactional(rollbackFor = RuntimeException.class)
public abstract class BaseController<E,T, ID extends Serializable> {

    /**
     * 获取对应的service
     * <AUTHOR>
     * @date 2019/10/31
     * @return BaseService<E, ID>
     */
    @Autowired
    public abstract IBaseService<E,T, ID> getService();


    /**
     * 根据ID查找实体对象
     *
     * @param id 实体的唯一标识符
     * @return 返回找到的实体对象，如果没有找到则返回null
     */
    @GetMapping("/findById/{id}")
    public T findById(@PathVariable("id") ID id) {
        return this.getService().find(id);
    }


    /**
     * 根据ID列表查询实体列表
     *
     * @param ids 实体的ID列表，用于指定需要查询的实体
     * @return 返回与提供的ID列表相对应的实体列表
     */
    @GetMapping("/findList")
    public List<T> findList(@RequestParam("ids") List<ID> ids) {
        return this.getService().findList(ids);
    }

    /**
     * 根据URL映射获取所有实体对象
     *
     * @return 返回一个包含所有实体对象的列表
     */
    @GetMapping("/findAll")
    public List<T> findAll() {
        return this.getService().findAll();
    }

    /**
     * 根据ID删除资源
     *
     * @param id 要删除的资源的ID
     */
    @DeleteMapping("/deleteById/{id}")
    public void deleteById(@PathVariable("id") ID id) {
        this.getService().delete(id);
    }

    /**
     * 批量删除实体
     *
     * 该接口用于接收一个ID列表，表示需要被删除的实体的标识符，
     * 并执行批量删除操作它依赖于具体的服务实现，通过调用服务层的
     * batchDelete方法来完成删除操作
     *
     * @param ids 待删除实体的ID列表这些ID用于标识数据库中需要被删除的记录
     */
    @DeleteMapping("/batchDelete")
    public void batchDelete(@RequestParam("ids") List<ID> ids) {
        this.getService().batchDelete(ids);
    }

    /**
     * 保存实体对象
     *
     * 该方法通过HTTP POST请求接收一个实体对象E，并将其保存到数据库中
     * 使用了@PostMapping注解来映射HTTP POST请求，表明这是一个处理POST请求的方法
     *
     * @param entity 要保存的实体对象，通过请求体（@RequestBody）传递
     * @return 保存后的实体对象，包括生成的唯一标识符等信息
     */
    @PostMapping("/save")
    public T save(@RequestBody E entity) {
        return this.getService().insert(entity);
    }

    /**
     * 批量保存实体
     *
     * @param entities 实体列表，用于批量插入
     * @return 插入后的实体列表
     */
    @PostMapping("/batchSave")
    public List<T> batchSave(@RequestBody List<E> entities) {
        return this.getService().batchInsert(entities);
    }

    /**
     * 更新实体
     *
     * @param entity 需要更新的实体
     * @return 更新后的实体
     */
    @PostMapping("/update")
    public T update(@RequestBody E entity) {
        return this.getService().update(entity);
    }

    /**
     * 批量更新实体
     *
     * @param entities 实体列表，用于批量更新
     * @return 更新后的实体列表
     */
    @PostMapping("/batchUpdate")
    public List<T> batchUpdate(@RequestBody List<E> entities) {
        return this.getService().batchUpdate(entities);
    }

    /**
     * 分页查询实体
     *
     * @param pageable 分页信息，包括页码、每页大小等
     * @return 包含实体的分页结果
     */
    @PostMapping("/findPage")
    public Page<T> findPage(@RequestBody Pageable pageable) {
        return this.getService().page(pageable);
    }

    /**
     * 检查实体是否存在
     *
     * @param id 实体的唯一标识
     * @return 如果实体存在返回true，否则返回false
     */
    @GetMapping("/exists/{id}")
    public boolean exists(@PathVariable ID id) {
        return this.getService().exists(id);
    }




}