<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>bici.bzlj</groupId>
    <artifactId>bzlj</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>bici.bzlj</groupId>
  <artifactId>base</artifactId>
  <version>1.0.0</version>
  <properties>
    <maven.compiler.target>21</maven.compiler.target>
    <platform.version>1.0.0</platform.version>
    <maven.compiler.source>21</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>com.querydsl</groupId>
      <artifactId>querydsl-jpa</artifactId>
      <classifier>jakarta</classifier>
    </dependency>
    <dependency>
      <groupId>com.blazebit</groupId>
      <artifactId>blaze-persistence-integration-querydsl-expressions</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.bici</groupId>
      <artifactId>bici-common-security</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.bici.casdoor</groupId>
      <artifactId>casdoor-spring-boot-starter</artifactId>
      <version>1.0.0-1</version>
    </dependency>
  </dependencies>
</project>
